import { NextRequest, NextResponse } from 'next/server'

// DEMO ONLY - This API route simulates server behavior using client-side storage
// In production, this will be replaced with proper server-side database storage

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface ConsentData {
  preferences: CookiePreferences
  timestamp: string
  userAgent?: string
  ipAddress?: string
}

export async function GET(request: NextRequest) {
  try {
    // DEMO: Return instructions for client-side checking
    // In production, this would check server-side database
    
    return NextResponse.json({
      demo: true,
      message: 'Demo mode: Check consent status client-side',
      hasConsent: false, // Client will override this
      instructions: 'Use DemoCookieStorage.hasConsent() on client-side'
    })
  } catch (error) {
    console.error('Demo API error:', error)
    return NextResponse.json(
      { error: 'Demo API error', demo: true },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { preferences, timestamp } = body

    // Validate preferences
    if (!preferences || typeof preferences !== 'object') {
      return NextResponse.json(
        { error: 'Invalid preferences data', demo: true },
        { status: 400 }
      )
    }

    // Ensure essential cookies are always enabled
    const validatedPreferences: CookiePreferences = {
      essential: true, // Always true
      analytics: Boolean(preferences.analytics),
      marketing: Boolean(preferences.marketing),
      functional: Boolean(preferences.functional)
    }

    // Get user information for demo logging
    const userAgent = request.headers.get('user-agent') || ''
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ipAddress = forwardedFor?.split(',')[0] || realIp || 'demo-ip'

    // Create consent data for demo logging
    const consentData: ConsentData = {
      preferences: validatedPreferences,
      timestamp: timestamp || new Date().toISOString(),
      userAgent,
      ipAddress: ipAddress.substring(0, 8) + '...' // Partial IP for privacy
    }

    // Demo logging (in production, this would go to secure database)
    console.log('🍪 Demo: Cookie consent received (server-side simulation):', {
      timestamp: consentData.timestamp,
      preferences: validatedPreferences,
      userAgent: userAgent.substring(0, 50) + '...'
    })

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Return success response
    const response = NextResponse.json({
      success: true,
      demo: true,
      preferences: validatedPreferences,
      timestamp: consentData.timestamp,
      message: 'Demo: Consent processed (stored client-side)'
    })

    // Demo: Set some response headers (in production, these would be real cookies)
    response.headers.set('X-Demo-Consent', 'processed')
    response.headers.set('X-Demo-Timestamp', consentData.timestamp)

    return response
  } catch (error) {
    console.error('Demo API error:', error)
    return NextResponse.json(
      { error: 'Demo API processing error', demo: true },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Demo: Simulate clearing consent
    console.log('🍪 Demo: Cookie consent deletion requested')
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 200))

    return NextResponse.json({ 
      success: true, 
      demo: true,
      message: 'Demo: Consent cleared (client-side storage)'
    })
  } catch (error) {
    console.error('Demo API error:', error)
    return NextResponse.json(
      { error: 'Demo API deletion error', demo: true },
      { status: 500 }
    )
  }
}