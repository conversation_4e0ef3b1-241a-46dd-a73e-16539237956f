"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShoppingBag, Trash2, Heart } from "lucide-react" // Added Heart for empty state
import { Skeleton } from "@/components/ui/skeleton" // Added
import { useState, useEffect } from "react" // Added

// Skeleton for a single saved product card
function SavedProductCardSkeleton() {
  return (
    <div className="border rounded-lg overflow-hidden">
      <Skeleton className="aspect-square w-full" /> {/* Image */}
      <div className="p-4">
        <Skeleton className="h-5 w-3/4 mb-1 rounded" /> {/* Name */}
        <Skeleton className="h-4 w-1/2 mb-2 rounded" /> {/* Category */}
        <Skeleton className="h-5 w-1/4 mb-4 rounded" /> {/* Price */}
        <div className="flex space-x-2">
          <Skeleton className="h-9 flex-1 rounded-md" /> {/* Add to <PERSON><PERSON> */}
          <Skeleton className="h-9 w-9 rounded-md" /> {/* Remove Button */}
        </div>
      </div>
    </div>
  )
}

// Skeleton for the grid of saved products
function SavedProductsGridSkeleton() {
  return (
    <div className="pt-5">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => ( // Show 3 skeleton cards
          <SavedProductCardSkeleton key={i} />
        ))}
      </div>
    </div>
  )
}

export default function SavedProducts() {
  const [savedProducts, setSavedProducts] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsLoading(true)
    setTimeout(() => {
      const fetchedProducts = [
        { id: 1, name: "Benzyl Alcohol", price: 129.99, image: "/images/product-1.png", category: "liquid" },
        { id: 5, name: "Potassium Chloride", price: 59.99, image: "/images/product-5.png", category: "powder" },
        { id: 8, name: "Hydrogen Peroxide Solution", price: 49.99, image: "/images/product-8.png", category: "liquid" },
      ];
      setSavedProducts(fetchedProducts)
      setIsLoading(false)
    }, 1500); // Simulate 1.5 second delay
  }, [])

  if (isLoading) {
    return <SavedProductsGridSkeleton />;
  }

  return (
    <div className="bg-white rounded-lg overflow-hidden">
      {savedProducts.length === 0 ? (
        <div className="p-8 text-center">
          <div className="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Heart className="w-8 h-8 text-neutral-500" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Saved Products</h3>
          <p className="text-neutral-600 mb-6">You haven't saved any products yet. Browse our products and save your favorites!</p>
          <Button asChild className="bg-teal-600 hover:bg-teal-700 text-white">
            <Link href="/categories/powder">Browse Products</Link>
          </Button>
        </div>
      ) : (
        <div className="pt-5">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {savedProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border rounded-lg overflow-hidden flex flex-col" // Added flex flex-col
              >
                <div className="relative aspect-square">
                  <Image src={product.image || "/placeholder.svg"} alt={product.name} fill className="object-cover" />
                </div>
                <div className="p-4 flex flex-col flex-grow"> {/* Added flex flex-col flex-grow */}
                  <Link href={`/products/${product.id}`} className="font-medium hover:text-teal-600 transition-colors">
                    {product.name}
                  </Link>
                  <p className="text-sm text-neutral-500 mb-2">Category: {product.category}</p>
                  <p className="font-medium mb-4">₹{product.price.toFixed(2)}</p>
                  <div className="flex space-x-2 mt-auto"> {/* Added mt-auto to push buttons to bottom */}
                    <Button className="flex-1 bg-teal-600 hover:bg-teal-700 text-white">
                      <ShoppingBag className="h-4 w-4 mr-2" />
                      Request Quote
                    </Button>
                    <Button variant="outline" size="icon" className="text-neutral-500 hover:text-red-500">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
