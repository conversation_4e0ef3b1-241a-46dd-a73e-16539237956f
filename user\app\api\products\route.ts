import { NextRequest, NextResponse } from 'next/server';

// Dynamic API route for real-time data processing
export const dynamic = 'force-dynamic';

// Server-side API client for admin backend
class ServerApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      const url = `${this.baseUrl}/api/v1${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          ...options.headers,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getProducts(params: any = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.collection) searchParams.set('collection', params.collection);
    if (params.featured) searchParams.set('featured', 'true');
    
    const query = searchParams.toString();
    const endpoint = `/products${query ? `?${query}` : ''}`;
    
    return this.makeRequest(endpoint);
  }
}

const serverApiClient = new ServerApiClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');
    const search = searchParams.get('search');
    const collection = searchParams.get('collection');
    const featured = searchParams.get('featured');

    // Get products via admin API
    const result = await serverApiClient.getProducts({
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      search: search || undefined,
      collection: collection || undefined,
      featured: featured === 'true',
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        ...result
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to fetch products' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Products API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}