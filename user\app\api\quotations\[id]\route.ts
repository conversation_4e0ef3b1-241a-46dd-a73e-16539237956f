import { NextRequest, NextResponse } from 'next/server';

// Dynamic API route for real-time data processing
export const dynamic = 'force-dynamic';

// Server-side API client to call admin API
class ServerApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      const url = `${this.baseUrl}/api/v1${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          ...options.headers,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async getQuotation(quotationId: string) {
    return this.makeRequest(`/quotations/${quotationId}`);
  }
}

const serverApiClient = new ServerApiClient();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const quotationId = params.id;

    if (!quotationId) {
      return NextResponse.json(
        { success: false, error: 'Quotation ID is required' },
        { status: 400 }
      );
    }

    const result = await serverApiClient.getQuotation(quotationId);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching quotation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch quotation' 
      },
      { status: 500 }
    );
  }
}
