"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Trash2, Plus, Minus, FileText, Edit3 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Skeleton } from "@/components/ui/skeleton"
import Image from "next/image"
import Link from "next/link"

// Skeleton for a single quotation item row
function QuotationItemRowSkeleton() {
  return (
    <li className="p-6 border-b">
      <div className="flex items-center space-x-4">
        <Skeleton className="h-16 w-16 rounded" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4 rounded" />
          <Skeleton className="h-3 w-1/2 rounded" />
        </div>
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-16 rounded" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </div>
    </li>
  )
}

// Skeleton for the entire QuotationItems component
function QuotationItemsSkeleton() {
  return (
    <div className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-xl overflow-hidden shadow-sm">
      <div className="p-6 border-b border-border/20">
        <Skeleton className="h-6 w-1/2 rounded" />
      </div>
      <ul className="divide-y divide-border/20">
        <QuotationItemRowSkeleton />
        <QuotationItemRowSkeleton />
        <QuotationItemRowSkeleton />
      </ul>
    </div>
  )
}

export default function QuotationItems() {
  const { currentQuotation, removeFromQuotation, updateQuotationItem, isLoading } = useSecureQuotation()
  const [editingNotes, setEditingNotes] = useState<string | null>(null)
  const [tempNotes, setTempNotes] = useState("")

  if (isLoading) {
    return <QuotationItemsSkeleton />
  }

  if (!currentQuotation || currentQuotation.items.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-xl overflow-hidden shadow-sm"
      >
        <div className="p-12 text-center">
          <div className="w-20 h-20 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <FileText className="w-10 h-10 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-3 text-foreground">Your quotation is empty</h3>
          <p className="text-muted-foreground mb-8 max-w-md mx-auto">
            Start building your custom quote by adding chemical products from our extensive catalog.
          </p>
          <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300">
            <Link href="/products">
              <Plus className="w-4 h-4 mr-2" />
              Browse Products
            </Link>
          </Button>
        </div>
      </motion.div>
    )
  }

  const handleQuantityChange = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return
    await updateQuotationItem(itemId, { quantity: newQuantity })
  }

  const handleNotesEdit = (itemId: string, currentNotes: string) => {
    setEditingNotes(itemId)
    setTempNotes(currentNotes || "")
  }

  const handleNotesSave = async (itemId: string) => {
    await updateQuotationItem(itemId, { notes: tempNotes })
    setEditingNotes(null)
    setTempNotes("")
  }

  const handleNotesCancel = () => {
    setEditingNotes(null)
    setTempNotes("")
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-xl overflow-hidden shadow-sm"
    >
      <div className="p-6 border-b border-border/20 bg-gradient-to-r from-primary/5 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Quotation Items</h2>
            <p className="text-sm text-muted-foreground">{currentQuotation.items.length} {currentQuotation.items.length === 1 ? 'item' : 'items'} added</p>
          </div>
        </div>
      </div>
      
      <ul className="divide-y divide-border/20">
        {currentQuotation.items.map((item, index) => (
          <motion.li
            key={item.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="p-6 hover:bg-accent/5 transition-colors duration-200"
          >
            <div className="flex items-start space-x-4">
              {/* Product Image */}
              <div className="relative h-16 w-16 rounded-xl overflow-hidden bg-muted/50 flex-shrink-0 ring-1 ring-border/20">
                <Image
                  src={item.image || "/placeholder-product.jpg"}
                  alt={item.name}
                  fill
                  className="object-cover"
                />
              </div>

              {/* Product Details */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-foreground truncate text-lg">{item.name}</h3>
                <div className="flex items-center gap-2 mt-1">
                  <span className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium">
                    {item.category}
                  </span>
                  <span className="text-sm text-muted-foreground">• Unit: {item.unit}</span>
                </div>
                {item.specifications && (
                  <p className="text-sm text-muted-foreground mt-1">{item.specifications}</p>
                )}
                
                {/* Notes Section */}
                <div className="mt-3">
                  {editingNotes === item.id ? (
                    <div className="space-y-3 p-3 bg-muted/30 rounded-lg border border-border/20">
                      <Textarea
                        value={tempNotes}
                        onChange={(e) => setTempNotes(e.target.value)}
                        placeholder="Add specific requirements, purity levels, or special instructions..."
                        className="text-sm border-border/20 bg-background/50"
                        rows={2}
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleNotesSave(item.id)}
                          className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                        >
                          Save Notes
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleNotesCancel}
                          className="border-border/20"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start gap-2 p-2 rounded-lg bg-muted/20 border border-border/10">
                      <div className="flex-1">
                        {item.notes ? (
                          <p className="text-sm text-foreground/80 italic">"{item.notes}"</p>
                        ) : (
                          <p className="text-sm text-muted-foreground">Click to add notes or special requirements</p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleNotesEdit(item.id, item.notes || "")}
                        className="h-7 w-7 p-0 hover:bg-primary/10 hover:text-primary"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Quantity Controls */}
              <div className="flex items-center space-x-2 bg-muted/30 rounded-lg p-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                  disabled={item.quantity <= 1}
                  className="h-8 w-8 p-0 border-border/20 hover:bg-primary/10 hover:text-primary hover:border-primary/20"
                >
                  <Minus className="h-3 w-3" />
                </Button>
                
                <Input
                  type="number"
                  value={item.quantity}
                  onChange={(e) => {
                    const newQuantity = parseInt(e.target.value) || 1
                    handleQuantityChange(item.id, newQuantity)
                  }}
                  className="w-16 h-8 text-center text-sm border-border/20 bg-background/50 font-medium"
                  min="1"
                />
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                  className="h-8 w-8 p-0 border-border/20 hover:bg-primary/10 hover:text-primary hover:border-primary/20"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>

              {/* Remove Button */}
              <Button
                size="sm"
                variant="outline"
                onClick={() => removeFromQuotation(item.id)}
                className="h-10 w-10 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 hover:border-destructive/20 border-border/20"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </motion.li>
        ))}
      </ul>
    </motion.div>
  )
}