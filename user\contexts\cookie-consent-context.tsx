"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { cookieConsentUtils } from '@/lib/cookie-consent-storage'

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface CookieConsentContextType {
  preferences: CookiePreferences | null
  hasConsent: boolean
  isLoading: boolean
  updatePreferences: (preferences: CookiePreferences) => Promise<void>
  clearConsent: () => Promise<void>
  checkConsentStatus: () => Promise<void>
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined)

export const useCookieConsent = () => {
  const context = useContext(CookieConsentContext)
  if (context === undefined) {
    throw new Error('useCookieConsent must be used within a CookieConsentProvider')
  }
  return context
}

interface CookieConsentProviderProps {
  children: React.ReactNode
}

export const CookieConsentProvider = ({ children }: CookieConsentProviderProps) => {
  const [preferences, setPreferences] = useState<CookiePreferences | null>(null)
  const [hasConsent, setHasConsent] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const checkConsentStatus = useCallback(async () => {
    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.checkConsentStatus()

      if (result.hasConsent && result.preferences) {
        setPreferences(result.preferences)
        setHasConsent(true)
        applyConsentPreferences(result.preferences)
      } else {
        setHasConsent(false)
        setPreferences(null)
      }
    } catch (error) {
      console.error('Error checking consent status:', error)
      setHasConsent(false)
      setPreferences(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const updatePreferences = async (newPreferences: CookiePreferences) => {
    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.saveConsent(newPreferences)

      if (result.success) {
        setPreferences(newPreferences)
        setHasConsent(true)
        applyConsentPreferences(newPreferences)
      } else {
        throw new Error(result.error || 'Failed to update preferences')
      }
    } catch (error) {
      console.error('Error updating preferences:', error)
      throw error
    }
  }

  const clearConsent = async () => {
    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.clearConsent()

      if (result.success) {
        setPreferences(null)
        setHasConsent(false)
        
        // Clear any tracking scripts or cookies
        clearTrackingScripts()
      } else {
        throw new Error(result.error || 'Failed to clear consent')
      }
    } catch (error) {
      console.error('Error clearing consent:', error)
      throw error
    }
  }

  const applyConsentPreferences = (prefs: CookiePreferences) => {
    if (typeof window === 'undefined') return

    // Analytics consent
    if (prefs.analytics) {
      enableAnalytics()
    } else {
      disableAnalytics()
    }

    // Marketing consent
    if (prefs.marketing) {
      enableMarketing()
    } else {
      disableMarketing()
    }

    // Functional consent
    if (prefs.functional) {
      enableFunctional()
    } else {
      disableFunctional()
    }

    // Dispatch event for other components
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: prefs
    }))
  }

  const enableAnalytics = () => {
    // Enable Google Analytics or other analytics tools
    if (typeof window !== 'undefined') {
      // Example: Google Analytics 4
      // window.gtag?.('consent', 'update', {
      //   analytics_storage: 'granted'
      // })
      
      console.log('🍪 Analytics tracking enabled')
    }
  }

  const disableAnalytics = () => {
    // Disable analytics tracking
    if (typeof window !== 'undefined') {
      // Example: Google Analytics 4
      // window.gtag?.('consent', 'update', {
      //   analytics_storage: 'denied'
      // })
      
      console.log('🍪 Analytics tracking disabled')
    }
  }

  const enableMarketing = () => {
    // Enable marketing/advertising cookies
    if (typeof window !== 'undefined') {
      // Example: Google Ads
      // window.gtag?.('consent', 'update', {
      //   ad_storage: 'granted',
      //   ad_user_data: 'granted',
      //   ad_personalization: 'granted'
      // })
      
      console.log('🍪 Marketing cookies enabled')
    }
  }

  const disableMarketing = () => {
    // Disable marketing/advertising cookies
    if (typeof window !== 'undefined') {
      // Example: Google Ads
      // window.gtag?.('consent', 'update', {
      //   ad_storage: 'denied',
      //   ad_user_data: 'denied',
      //   ad_personalization: 'denied'
      // })
      
      console.log('🍪 Marketing cookies disabled')
    }
  }

  const enableFunctional = () => {
    // Enable functional cookies (chat widgets, preferences, etc.)
    if (typeof window !== 'undefined') {
      console.log('🍪 Functional cookies enabled')
      
      // Example: Enable chat widget
      // if (window.Intercom) {
      //   window.Intercom('boot', { ... })
      // }
    }
  }

  const disableFunctional = () => {
    // Disable functional cookies
    if (typeof window !== 'undefined') {
      console.log('🍪 Functional cookies disabled')
      
      // Example: Disable chat widget
      // if (window.Intercom) {
      //   window.Intercom('shutdown')
      // }
    }
  }

  const clearTrackingScripts = () => {
    // Clear any tracking scripts or cookies when consent is withdrawn
    if (typeof window !== 'undefined') {
      // Clear analytics
      disableAnalytics()
      
      // Clear marketing
      disableMarketing()
      
      // Clear functional
      disableFunctional()
      
      console.log('🍪 All tracking scripts cleared')
    }
  }

  // Initialize consent status on mount
  useEffect(() => {
    checkConsentStatus()
  }, [checkConsentStatus])

  // Listen for consent changes from other tabs/windows and cookie changes
  useEffect(() => {
    const handleConsentChange = (e: CustomEvent) => {
      if (e.detail) {
        setPreferences(e.detail)
        setHasConsent(true)
      }
    }

    // Listen for cookie changes (cross-tab communication)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Check consent status when tab becomes visible
        checkConsentStatus()
      }
    }

    // Listen for focus events to detect potential cookie changes
    const handleFocus = () => {
      checkConsentStatus()
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }, [checkConsentStatus])

  const value = {
    preferences,
    hasConsent,
    isLoading,
    updatePreferences,
    clearConsent,
    checkConsentStatus
  }

  return (
    <CookieConsentContext.Provider value={value}>
      {children}
    </CookieConsentContext.Provider>
  )
}