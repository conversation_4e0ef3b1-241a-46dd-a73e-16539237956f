"use client";

import { Suspense } from "react";
import ProductDetail from "@/components/product-detail";
import EnhancedProductDetailSkeleton from "@/components/enhanced-product-detail-skeleton";
import type { Product } from "@/lib/types";

interface ProductDetailWrapperProps {
  product?: Product;
  isLoading?: boolean;
}

export default function ProductDetailWrapper({ product, isLoading = false }: ProductDetailWrapperProps) {
  if (isLoading || !product) {
    return <EnhancedProductDetailSkeleton />;
  }

  return (
    <Suspense fallback={<EnhancedProductDetailSkeleton />}>
      <ProductDetail product={product} />
    </Suspense>
  );
}