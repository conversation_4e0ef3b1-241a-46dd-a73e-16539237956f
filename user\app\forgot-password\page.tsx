import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import ForgotPasswordForm from "@/components/forgot-password-form"
import { ArrowLeft, Shield, Mail, Clock } from "lucide-react"

export const metadata: Metadata = {
  title: "Forgot Password | Benzochem Industries",
  description: "Reset your Benzochem Industries account password",
}

export default function ForgotPasswordPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[calc(100vh-8rem)]">
            
            {/* Left side - Information */}
            <div className="space-y-8 lg:pr-8">
              {/* Back to Login */}
              <Link
                href="/login"
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Login
              </Link>

              {/* Hero Section */}
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
                  <Shield className="w-4 h-4" />
                  Secure Password Reset
                </div>
                
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight">
                    Reset Your
                    <span className="block text-primary">Password</span>
                  </h1>
                  <p className="text-lg text-muted-foreground max-w-lg">
                    Enter your email address and we'll send you instructions to reset your password securely.
                  </p>
                </div>
              </div>

              {/* Process Steps */}
              <div className="space-y-4">
                <h3 className="font-semibold text-foreground mb-4">How it works:</h3>
                <div className="space-y-4">
                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Mail className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">1. Enter Your Email</h4>
                      <p className="text-sm text-muted-foreground">Provide the email address associated with your account</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Mail className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">2. Check Your Email</h4>
                      <p className="text-sm text-muted-foreground">We'll send you a secure reset link to your email</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Clock className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">3. Reset Password</h4>
                      <p className="text-sm text-muted-foreground">Click the link and create a new secure password</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Notice */}
              <div className="hidden lg:block relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-3xl"></div>
                <div className="relative bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <Shield className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground">Security First</h4>
                      <p className="text-sm text-muted-foreground">Your account security is our priority</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Reset links expire in 15 minutes
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Secure token-based authentication
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Email verification required
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Form */}
            <div className="w-full max-w-md mx-auto lg:mx-0">
              <div className="bg-card border border-border rounded-2xl shadow-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-card-foreground mb-2">Forgot Password</h2>
                  <p className="text-muted-foreground">We'll help you get back into your account</p>
                </div>

                <ForgotPasswordForm />

                <div className="mt-8 text-center">
                  <p className="text-muted-foreground text-sm">
                    Remember your password?{" "}
                    <Link
                      href="/login"
                      className="text-primary hover:text-primary/80 font-medium transition-colors duration-200"
                    >
                      Sign In
                    </Link>
                  </p>
                </div>
              </div>

              {/* Help Section */}
              <div className="mt-6 bg-muted/50 rounded-xl p-4">
                <h4 className="font-medium text-foreground mb-2">Need Help?</h4>
                <p className="text-sm text-muted-foreground">
                  If you don't receive the reset email within a few minutes, check your spam folder or{" "}
                  <Link href="/contact" className="text-primary hover:text-primary/80 font-medium">
                    contact support
                  </Link>
                  .
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}