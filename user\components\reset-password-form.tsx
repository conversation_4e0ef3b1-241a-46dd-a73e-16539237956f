"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Lock, Eye, EyeOff, Loader2, AlertCircle, CheckCircle, Shield } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function ResetPasswordForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string | null>>({})
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [tokenValid, setTokenValid] = useState<boolean | null>(null)
  const [email, setEmail] = useState<string>("")

  // Get token and email from URL params
  const token = searchParams.get('token')
  const emailParam = searchParams.get('email')

  // Password strength indicators
  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  })

  // Validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token || !emailParam) {
        setError("Invalid reset link. Please request a new password reset.")
        setTokenValid(false)
        return
      }

      try {
        const response = await fetch(`/api/auth/reset-password?token=${encodeURIComponent(token)}&email=${encodeURIComponent(emailParam)}`)
        const data = await response.json()

        if (response.ok && data.valid) {
          setTokenValid(true)
          setEmail(data.email)
        } else {
          setError(data.error || "Invalid or expired reset link. Please request a new password reset.")
          setTokenValid(false)
        }
      } catch (error) {
        console.error("Token validation error:", error)
        setError("Failed to validate reset link. Please try again.")
        setTokenValid(false)
      }
    }

    validateToken()
  }, [token, emailParam])

  // Update password strength indicators
  useEffect(() => {
    const password = formData.newPassword
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    })
  }, [formData.newPassword])

  // Validation functions
  const validatePassword = (password: string) => {
    if (!password) return "Please enter a new password"
    if (password.length < 8) return "Password must be at least 8 characters long"
    return null
  }

  const validateConfirmPassword = (confirmPassword: string, password: string) => {
    if (!confirmPassword) return "Please confirm your password"
    if (confirmPassword !== password) return "Passwords do not match"
    return null
  }

  const validateForm = () => {
    const errors: Record<string, string | null> = {}
    errors.newPassword = validatePassword(formData.newPassword)
    errors.confirmPassword = validateConfirmPassword(formData.confirmPassword, formData.newPassword)

    setFieldErrors(errors)
    return Object.values(errors).every((err) => err === null)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }))
    }
    // Clear general error when user starts typing
    if (error) {
      setError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!validateForm()) {
      return
    }

    if (!token || !email) {
      setError("Invalid reset link. Please request a new password reset.")
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          email,
          newPassword: formData.newPassword,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/login?message=Password successfully reset. Please sign in with your new password.')
        }, 3000)
      } else {
        setError(data.error || "Failed to reset password. Please try again.")
      }
    } catch (error) {
      console.error("Reset password error:", error)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while validating token
  if (tokenValid === null) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <p className="text-center text-muted-foreground">Validating reset link...</p>
      </div>
    )
  }

  // Show error if token is invalid
  if (tokenValid === false) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        
        <div className="text-center space-y-4">
          <Button
            onClick={() => router.push('/forgot-password')}
            className="w-full"
          >
            Request New Reset Link
          </Button>
          
          <Button
            variant="outline"
            onClick={() => router.push('/login')}
            className="w-full"
          >
            Back to Login
          </Button>
        </div>
      </div>
    )
  }

  // Show success message
  if (success) {
    return (
      <div className="space-y-6">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            <div className="space-y-2">
              <p className="font-medium">Password successfully reset!</p>
              <p className="text-sm">
                Your password has been updated. You will be redirected to the login page shortly.
              </p>
            </div>
          </AlertDescription>
        </Alert>

        <div className="text-center">
          <Button
            onClick={() => router.push('/login')}
            className="w-full"
          >
            Continue to Login
          </Button>
        </div>
      </div>
    )
  }

  const getStrengthColor = (met: boolean) => met ? "text-green-600" : "text-muted-foreground"
  const getStrengthIcon = (met: boolean) => met ? "✓" : "○"

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Email Display */}
        <div className="bg-muted/50 rounded-xl p-4">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium">Resetting password for:</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1 ml-6">{email}</p>
        </div>

        {/* New Password Field */}
        <div className="space-y-2">
          <Label htmlFor="newPassword" className="text-sm font-medium flex items-center gap-2">
            <Lock className="w-4 h-4 text-primary" />
            New Password
          </Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showPassword ? "text" : "password"}
              value={formData.newPassword}
              onChange={(e) => handleInputChange("newPassword", e.target.value)}
              className={`pr-10 transition-all duration-200 ${
                fieldErrors.newPassword 
                  ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                  : "focus:border-primary focus:ring-primary/20"
              }`}
              placeholder="Enter your new password"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors duration-200"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {fieldErrors.newPassword && (
            <p className="text-destructive text-sm flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {fieldErrors.newPassword}
            </p>
          )}
        </div>

        {/* Password Strength Indicators */}
        {formData.newPassword && (
          <div className="bg-muted/50 rounded-xl p-4 space-y-2">
            <h4 className="text-sm font-medium text-foreground">Password Strength:</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className={`flex items-center gap-1 ${getStrengthColor(passwordStrength.length)}`}>
                <span>{getStrengthIcon(passwordStrength.length)}</span>
                <span>8+ characters</span>
              </div>
              <div className={`flex items-center gap-1 ${getStrengthColor(passwordStrength.uppercase)}`}>
                <span>{getStrengthIcon(passwordStrength.uppercase)}</span>
                <span>Uppercase letter</span>
              </div>
              <div className={`flex items-center gap-1 ${getStrengthColor(passwordStrength.lowercase)}`}>
                <span>{getStrengthIcon(passwordStrength.lowercase)}</span>
                <span>Lowercase letter</span>
              </div>
              <div className={`flex items-center gap-1 ${getStrengthColor(passwordStrength.number)}`}>
                <span>{getStrengthIcon(passwordStrength.number)}</span>
                <span>Number</span>
              </div>
            </div>
          </div>
        )}

        {/* Confirm Password Field */}
        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-sm font-medium flex items-center gap-2">
            <Lock className="w-4 h-4 text-primary" />
            Confirm New Password
          </Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
              className={`pr-10 transition-all duration-200 ${
                fieldErrors.confirmPassword 
                  ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                  : "focus:border-primary focus:ring-primary/20"
              }`}
              placeholder="Confirm your new password"
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors duration-200"
            >
              {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {fieldErrors.confirmPassword && (
            <p className="text-destructive text-sm flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {fieldErrors.confirmPassword}
            </p>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full h-11 font-medium transition-all duration-200 hover:shadow-md"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Resetting Password...
            </>
          ) : (
            <>
              <Lock className="mr-2 h-4 w-4" />
              Reset Password
            </>
          )}
        </Button>
      </form>
    </div>
  )
}