"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Linkedin, Mail, Award, Calendar, MapPin, ExternalLink } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface TeamMemberProps {
  name: string
  position: string
  image: string
  bio: string
  experience?: string
  location?: string
  achievements?: string[]
  specialties?: string[]
  linkedinUrl?: string
  email?: string
}

export default function EnhancedTeamMember({ 
  name, 
  position, 
  image, 
  bio, 
  experience,
  location,
  achievements = [],
  specialties = [],
  linkedinUrl,
  email
}: TeamMemberProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="group"
    >
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-500 bg-card/50 backdrop-blur-sm hover:bg-card/80">
        <div className="relative">
          {/* Profile Image with Gradient Overlay */}
          <div className="relative aspect-[4/5] overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent z-10" />
            <Image 
              src={image || "/placeholder.svg"} 
              alt={name} 
              fill 
              className="object-cover transition-transform duration-700 group-hover:scale-105" 
            />
            
            {/* Floating Badge */}
            <div className="absolute top-4 right-4 z-20">
              <Badge variant="secondary" className="bg-background/90 backdrop-blur-sm text-foreground border-0">
                Leadership
              </Badge>
            </div>

            {/* Bottom Info Overlay */}
            <div className="absolute bottom-0 left-0 right-0 z-20 p-6 text-white">
              <h3 className="text-xl font-semibold mb-1">{name}</h3>
              <p className="text-primary-foreground/90 text-sm font-medium">{position}</p>
              {location && (
                <div className="flex items-center mt-2 text-xs text-white/80">
                  <MapPin className="h-3 w-3 mr-1" />
                  {location}
                </div>
              )}
            </div>
          </div>

          <CardContent className="p-6 space-y-4">
            {/* Experience */}
            {experience && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-2 text-primary" />
                <span>{experience}</span>
              </div>
            )}

            {/* Bio */}
            <p className="text-sm text-muted-foreground leading-relaxed">{bio}</p>

            {/* Specialties */}
            {specialties.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-foreground">Specialties</h4>
                <div className="flex flex-wrap gap-1">
                  {specialties.map((specialty, index) => (
                    <Badge 
                      key={index} 
                      variant="outline" 
                      className="text-xs bg-secondary/50 hover:bg-secondary transition-colors"
                    >
                      {specialty}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Achievements */}
            {achievements.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-foreground flex items-center">
                  <Award className="h-4 w-4 mr-1 text-primary" />
                  Key Achievements
                </h4>
                <ul className="space-y-1">
                  {achievements.map((achievement, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start">
                      <span className="w-1 h-1 rounded-full bg-primary mt-2 mr-2 flex-shrink-0" />
                      {achievement}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              {linkedinUrl && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 group/btn hover:bg-primary hover:text-primary-foreground transition-all duration-300"
                  onClick={() => window.open(linkedinUrl, '_blank')}
                >
                  <Linkedin className="h-4 w-4 mr-2 transition-transform group-hover/btn:scale-110" />
                  LinkedIn
                  <ExternalLink className="h-3 w-3 ml-1 opacity-0 group-hover/btn:opacity-100 transition-opacity" />
                </Button>
              )}
              {email && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1 group/btn hover:bg-primary hover:text-primary-foreground transition-all duration-300"
                  onClick={() => window.location.href = `mailto:${email}`}
                >
                  <Mail className="h-4 w-4 mr-2 transition-transform group-hover/btn:scale-110" />
                  Contact
                </Button>
              )}
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  )
}