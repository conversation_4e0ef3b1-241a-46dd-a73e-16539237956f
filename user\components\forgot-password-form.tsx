"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail, Loader2, AlertCircle, CheckCircle, Send } from "lucide-react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"

export default function ForgotPasswordForm() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [fieldError, setFieldError] = useState<string | null>(null)

  // Validation function
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address"
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return "Please enter a valid email address"
    return null
  }

  const handleInputChange = (value: string) => {
    setEmail(value)
    // Clear errors when user starts typing
    if (fieldError) setFieldError(null)
    if (error) setError(null)
    if (success) setSuccess(false)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(false)

    // Validate email
    const emailError = validateEmail(email)
    if (emailError) {
      setFieldError(emailError)
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
        setEmail("") // Clear the form
      } else {
        setError(data.error || "Failed to send reset email. Please try again.")
      }
    } catch (error) {
      console.error("Forgot password error:", error)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendEmail = async () => {
    if (!email) {
      setError("Please enter your email address first")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
      } else {
        setError(data.error || "Failed to resend email. Please try again.")
      }
    } catch (error) {
      console.error("Resend email error:", error)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-6">
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            <div className="space-y-2">
              <p className="font-medium">Reset instructions sent!</p>
              <p className="text-sm">
                We've sent password reset instructions to your email address. 
                Please check your inbox and follow the link to reset your password.
              </p>
            </div>
          </AlertDescription>
        </Alert>

        <div className="bg-muted/50 rounded-xl p-4 space-y-3">
          <h4 className="font-medium text-foreground">What's next?</h4>
          <ul className="text-sm text-muted-foreground space-y-2">
            <li className="flex items-start gap-2">
              <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              Check your email inbox for the reset link
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              The link will expire in 15 minutes for security
            </li>
            <li className="flex items-start gap-2">
              <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              Check your spam folder if you don't see the email
            </li>
          </ul>
        </div>

        <div className="flex flex-col gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleResendEmail}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Resending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Resend Email
              </>
            )}
          </Button>

          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              setSuccess(false)
              setEmail("")
              setError(null)
              setFieldError(null)
            }}
            className="w-full"
          >
            Try Different Email
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
            <Mail className="w-4 h-4 text-primary" />
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => handleInputChange(e.target.value)}
            className={`transition-all duration-200 ${
              fieldError 
                ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                : "focus:border-primary focus:ring-primary/20"
            }`}
            placeholder="Enter your registered email address"
            disabled={isLoading}
          />
          {fieldError && (
            <p className="text-destructive text-sm flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {fieldError}
            </p>
          )}
          <p className="text-xs text-muted-foreground">
            Enter the email address associated with your account
          </p>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full h-11 font-medium transition-all duration-200 hover:shadow-md"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending Reset Link...
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              Send Reset Link
            </>
          )}
        </Button>
      </form>

      {/* Security Notice */}
      <div className="bg-muted/50 rounded-xl p-4 space-y-3">
        <div className="flex items-center gap-2">
          <AlertCircle className="w-4 h-4 text-primary" />
          <h4 className="font-medium text-foreground">Security Notice</h4>
        </div>
        <ul className="text-sm text-muted-foreground space-y-2 ml-6">
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            Reset links are valid for 15 minutes only
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            Each link can only be used once
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            If you don't receive the email, check your spam folder
          </li>
        </ul>
      </div>
    </div>
  )
}