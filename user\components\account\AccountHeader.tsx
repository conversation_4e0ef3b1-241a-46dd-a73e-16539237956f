"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, LogOut, ShoppingBag, CheckCircle2, AlertCircle, RefreshCw } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { useAuth } from "@/contexts/auth-context"
import { useEffect, useState } from "react"

interface AccountHeaderProps {
  isLoading: boolean;
  error: string | null;
  shopifyData: any; // Consider defining a more specific type
  user: any; // From useAuth, consider defining a more specific type
  setActiveTab: (tab: string) => void;
  logout: () => void;
  redirectToLogin: () => void;
}

// Skeleton for the Account Header
function AccountHeaderSkeleton() {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
      <div>
        <Skeleton className="h-7 w-48 mb-2 rounded" /> {/* Welcome message */}
        <Skeleton className="h-5 w-56 mb-2 rounded" /> {/* Email */}
        <Skeleton className="h-6 w-28 mt-1 rounded-md" /> {/* GST Badge */}
      </div>
      <div className="mt-4 md:mt-0 flex space-x-3">
        <Skeleton className="h-9 w-28 rounded-md" /> {/* My Orders Button */}
        <Skeleton className="h-9 w-28 rounded-md" /> {/* Sign Out Button */}
      </div>
    </div>
  );
}

export default function AccountHeader({
  isLoading,
  error,
  shopifyData,
  user,
  setActiveTab,
  logout,
  redirectToLogin,
}: AccountHeaderProps) {
  const { isRefreshing, refreshUserData } = useAuth()
  const [gstStatusAnimation, setGstStatusAnimation] = useState(false)

  // Listen for real-time user data updates
  useEffect(() => {
    const handleUserDataUpdate = (event: CustomEvent) => {
      const updatedData = event.detail

      // Trigger animation for GST status changes
      if (updatedData.isGstVerified !== shopifyData?.isGstVerified) {
        setGstStatusAnimation(true)
        setTimeout(() => setGstStatusAnimation(false), 2000)
      }
    }

    window.addEventListener('user-data-updated', handleUserDataUpdate as EventListener)

    return () => {
      window.removeEventListener('user-data-updated', handleUserDataUpdate as EventListener)
    }
  }, [shopifyData])
  return (
    <div className="mb-5">
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-gradient-to-r from-teal-600 to-teal-700 rounded-xl p-6 text-white shadow-lg min-h-[120px]" // Added min-h for consistent height
      >
        {isLoading && !error ? ( // Show skeleton only if loading and no error
          <AccountHeaderSkeleton />
        ) : error ? (
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-2xl font-bold mb-1">Please sign in</h1> 
              <p className="text-teal-100">
                {/* Display generic message or user email if available from clerk/auth context */}
                {user?.email || "Access your account details after signing in."} 
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <Button
                variant="outline"
                className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                onClick={redirectToLogin}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign In
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <h1 className="text-2xl font-bold mb-1">Welcome back, {shopifyData?.firstName || user?.firstName || 'there'}!</h1>
              <p className="text-teal-100">
                {shopifyData?.email || user?.email}
              </p>
              <div className="mt-2 flex items-center space-x-2">
                <motion.div
                  animate={gstStatusAnimation ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  {shopifyData?.isGstVerified ? (
                    <Badge className="bg-green-500/20 text-green-100 hover:bg-green-500/30 border-green-500/30">
                      <CheckCircle2 className="h-3 w-3 mr-1" /> GST Verified
                    </Badge>
                  ) : shopifyData?.gstNumber ? (
                    <Badge className="bg-yellow-500/20 text-yellow-100 hover:bg-yellow-500/30 border-yellow-500/30">
                      <AlertCircle className="h-3 w-3 mr-1" /> GST Pending
                    </Badge>
                  ) : (
                    <Badge className="bg-neutral-500/20 text-neutral-100 hover:bg-neutral-500/30 border-neutral-500/30">
                      GST Not Provided
                    </Badge>
                  )}
                </motion.div>
                {isRefreshing && (
                  <RefreshCw className="h-3 w-3 text-white/70 animate-spin" />
                )}
              </div>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <Button
                variant="outline"
                className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                onClick={() => setActiveTab("quotations")}
              >
                <ShoppingBag className="h-4 w-4 mr-2" />
                My Quotations
              </Button>
              <Button
                variant="outline"
                className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                onClick={logout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}