"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { ArrowR<PERSON>, CheckCircle, Sparkles } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"

interface CategorySectionProps {
  title: string
  description: string
  image: string
  href: string
  features?: string[]
}

export default function CategorySection({ title, description, image, href, features = [] }: CategorySectionProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.6 }}
      className="group"
    >
      <Card className="border-0 bg-white-500 dark:bg-card/30 backdrop-blur-sm shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden h-full">
        <div className="relative">
          {/* Image Section */}
          <div className="relative aspect-[16/10] overflow-hidden">
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
              sizes="(max-width: 768px) 100vw, 50vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            
            {/* Floating Badge */}
            <div className="absolute top-6 left-6">
              <Badge className="bg-white-900 dark:bg-background/90 text-foreground border-0 backdrop-blur-sm">
                <Sparkles className="w-3 h-3 mr-1" />
                Premium Quality
              </Badge>
            </div>
          </div>

          {/* Content Section */}
          <CardContent className="p-8">
            <div className="space-y-6">
              {/* Title and Description */}
              <div>
                <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                  {title}
                </h3>
                <p className="text-muted-foreground leading-relaxed text-lg">
                  {description}
                </p>
              </div>

              {/* Features */}
              {features.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-foreground uppercase tracking-wide">
                    Key Features
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {features.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="flex items-center gap-3"
                      >
                        <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                          <CheckCircle className="w-3 h-3 text-primary" />
                        </div>
                        <span className="text-sm text-muted-foreground font-medium">
                          {feature}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* CTA Button */}
              <div className="pt-4">
                <Button 
                  asChild 
                  className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-warm hover:shadow-warm-lg transition-all duration-300 group/btn h-12"
                >
                  <Link href={href}>
                    <span className="flex items-center justify-center gap-2">
                      Explore Products
                      <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
                    </span>
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </div>

        {/* Hover Effect Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
      </Card>
    </motion.div>
  )
}