// Production cookie consent utilities
// Server-side consent checking with proper cookie handling

import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'

export interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export interface ConsentData {
  preferences: CookiePreferences
  timestamp: string
  userAgent?: string
  ipAddress?: string
}

const CONSENT_COOKIE_NAME = 'cookie_consent'

// Server-side utility to get consent from cookies
export function getCookieConsentFromRequest(request: NextRequest): CookiePreferences | null {
  try {
    const cookieStore = cookies()
    const consentCookie = cookieStore.get(CONSENT_COOKIE_NAME)
    
    if (!consentCookie) {
      return null
    }

    const consentData: ConsentData = JSON.parse(decodeURIComponent(consentCookie.value))
    
    // Validate consent data
    if (!consentData.preferences || !consentData.timestamp) {
      return null
    }

    // Check if consent is expired (1 year)
    const consentDate = new Date(consentData.timestamp)
    const now = new Date()
    const diffDays = Math.ceil((now.getTime() - consentDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffDays > 365) {
      return null
    }

    return consentData.preferences
  } catch (error) {
    console.error('Error getting consent from request:', error)
    return null
  }
}

// Analytics tracking check
export function canTrackAnalytics(request: NextRequest): boolean {
  const preferences = getCookieConsentFromRequest(request)
  return preferences?.analytics === true
}

// Marketing tracking check
export function canTrackMarketing(request: NextRequest): boolean {
  const preferences = getCookieConsentFromRequest(request)
  return preferences?.marketing === true
}

// Functional features check
export function canUseFunctional(request: NextRequest): boolean {
  const preferences = getCookieConsentFromRequest(request)
  return preferences?.functional === true
}

// Get user IP address from request headers
export function getClientIP(request: NextRequest): string {
  const forwardedFor = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('x-vercel-forwarded-for')
  
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim()
  }
  
  if (realIp) {
    return realIp.trim()
  }
  
  if (remoteAddr) {
    return remoteAddr.split(',')[0].trim()
  }
  
  return 'unknown'
}

// Validate cookie preferences structure
export function validateCookiePreferences(preferences: any): CookiePreferences | null {
  if (!preferences || typeof preferences !== 'object') {
    return null
  }

  try {
    return {
      essential: true, // Always true for essential cookies
      analytics: Boolean(preferences.analytics),
      marketing: Boolean(preferences.marketing),
      functional: Boolean(preferences.functional)
    }
  } catch (error) {
    console.error('Error validating cookie preferences:', error)
    return null
  }
}

// Create consent audit log entry
export function createConsentAuditLog(
  request: NextRequest,
  preferences: CookiePreferences,
  action: 'granted' | 'updated' | 'withdrawn'
): ConsentData {
  return {
    preferences,
    timestamp: new Date().toISOString(),
    userAgent: request.headers.get('user-agent') || 'unknown',
    ipAddress: getClientIP(request)
  }
}

// GDPR compliance utilities
export class GDPRCompliance {
  // Check if request is from EU (simplified - in production use proper geolocation)
  static isEURequest(request: NextRequest): boolean {
    // In production, implement proper geolocation checking
    return true // Conservative approach - assume GDPR applies
  }

  // Check if consent is required
  static requiresConsent(request: NextRequest): boolean {
    return this.isEURequest(request)
  }

  // Get consent status from server-side cookies
  static getConsentStatus(request: NextRequest): {
    hasConsent: boolean
    preferences: CookiePreferences | null
    requiresConsent: boolean
  } {
    const preferences = getCookieConsentFromRequest(request)
    
    return {
      hasConsent: preferences !== null,
      preferences,
      requiresConsent: this.requiresConsent(request)
    }
  }
}

// Default cookie preferences (essential only)
export const DEFAULT_COOKIE_PREFERENCES: CookiePreferences = {
  essential: true,
  analytics: false,
  marketing: false,
  functional: false
}

// Cookie consent configuration
export const COOKIE_CONSENT_CONFIG = {
  cookieName: CONSENT_COOKIE_NAME,
  version: '1.0',
  expiryDays: 365,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const
}

// Production info for development
export const logProductionInfo = () => {
  if (typeof console !== 'undefined' && process.env.NODE_ENV === 'development') {
    console.info(
      '🍪 Production cookie consent system using server-side cookies and API endpoints.'
    )
  }
}