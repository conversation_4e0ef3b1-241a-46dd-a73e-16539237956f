/* Premium Typography for Benzochem Industries */

/* SF Pro Display / Inter for Headers */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  letter-spacing: -0.02em;
}

h1, h2 {
  font-weight: 700; /* Bold */
  line-height: 1.2;
}

h3, h4 {
  font-weight: 600; /* Semibold */
  line-height: 1.3;
}

h5, h6 {
  font-weight: 500; /* Medium */
  line-height: 1.3;
}

/* SF Pro Text / Inter for Body Text */
body, p {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 400; /* Regular */
  line-height: 1.5;
  letter-spacing: normal;
}

/* Navigation & UI Elements */
.nav-item, .ui-element, button, .tabs-trigger {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 500; /* Medium */
  letter-spacing: -0.01em;
}

/* Product Names */
.product-name {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 600; /* Semibold */
  letter-spacing: -0.01em;
}

/* Price & CTAs */
.price, .cta-button, button[type="submit"] {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 700; /* Bold */
  letter-spacing: -0.01em;
}

/* Section Headers */
.section-header {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 600; /* Semibold */
  letter-spacing: -0.02em;
  line-height: 1.3;
}

/* Form Labels */
label {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 500; /* Medium */
  letter-spacing: -0.01em;
}

/* Input Text */
input, textarea, select {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 400; /* Regular */
  letter-spacing: normal;
}

/* Helper Text & Small Text */
.helper-text, small {
  font-family: var(--font-sf-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 400; /* Regular */
  letter-spacing: normal;
  line-height: 1.4;
}

/* Font Weight Utilities */
.font-regular {
  font-weight: 400 !important;
}

.font-medium {
  font-weight: 500 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-bold {
  font-weight: 700 !important;
}

.font-heavy {
  font-weight: 800 !important;
}

/* Line Height Utilities */
.leading-tight {
  line-height: 1.2 !important;
}

.leading-snug {
  line-height: 1.3 !important;
}

.leading-normal {
  line-height: 1.5 !important;
}

.leading-relaxed {
  line-height: 1.6 !important;
}

/* Letter Spacing Utilities */
.tracking-tight {
  letter-spacing: -0.02em !important;
}

.tracking-normal {
  letter-spacing: normal !important;
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
  body, p {
    font-weight: 500; /* Slightly heavier for better readability on dark backgrounds */
  }
}

/* Mobile Typography Adjustments */
@media (max-width: 768px) {
  body, p {
    font-size: 16px; /* Ensure minimum 16px font size for body text on mobile */
  }
}

/* Alternative Font Options */
.font-modern-heading {
  font-family: var(--font-poppins), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.font-modern-body {
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.font-elegant-heading {
  font-family: var(--font-montserrat), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.font-elegant-body {
  font-family: var(--font-source-sans-pro), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.font-tech-heading {
  font-family: var(--font-space-grotesk), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.font-tech-body {
  font-family: var(--font-dm-sans), var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
