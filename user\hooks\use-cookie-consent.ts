"use client"

import { useEffect, useState } from 'react'
import { CookieConsentStorage } from '@/lib/cookie-consent-storage'

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface CookieConsentHook {
  hasConsent: boolean
  preferences: CookiePreferences | null
  isLoading: boolean
  canUseAnalytics: boolean
  canUseMarketing: boolean
  canUseFunctional: boolean
  checkConsent: () => Promise<void>
}

export function useCookieConsent(): CookieConsentHook {
  const [hasConsent, setHasConsent] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const checkConsent = async () => {
    try {
      // Use secure cookie storage instead of localStorage
      const consent = await CookieConsentStorage.getConsent()
      
      if (consent && !(await CookieConsentStorage.needsRenewal())) {
        setHasConsent(true)
        setPreferences(consent.preferences)
      } else {
        setHasConsent(false)
        setPreferences(null)
      }
    } catch (error) {
      console.error('Error checking cookie consent:', error)
      setHasConsent(false)
      setPreferences(null)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    checkConsent()

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      if (event.detail) {
        setPreferences(event.detail)
        setHasConsent(true)
      }
    }

    // Listen for cookie changes (cross-tab sync is limited with cookies)
    // We'll rely on the custom event for now
    const handleConsentUpdate = () => {
      checkConsent()
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    window.addEventListener('cookieConsentUpdated', handleConsentUpdate)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
      window.removeEventListener('cookieConsentUpdated', handleConsentUpdate)
    }
  }, [])

  // Helper functions to check specific consent types
  const canUseAnalytics = hasConsent && preferences?.analytics === true
  const canUseMarketing = hasConsent && preferences?.marketing === true
  const canUseFunctional = hasConsent && preferences?.functional === true

  return {
    hasConsent,
    preferences,
    isLoading,
    canUseAnalytics,
    canUseMarketing,
    canUseFunctional,
    checkConsent
  }
}

// Utility function to get cookie consent from secure cookies
export async function getCookieConsent(): Promise<CookiePreferences | null> {
  if (typeof window === 'undefined') return null
  
  try {
    const consent = await CookieConsentStorage.getConsent()
    return consent?.preferences || null
  } catch (error) {
    console.error('Error getting cookie consent:', error)
    return null
  }
}

// Utility function to check if a specific cookie type is allowed
export function isCookieAllowed(type: 'analytics' | 'marketing' | 'functional'): boolean {
  // For synchronous checks, we'll use a simple cookie read
  if (typeof window === 'undefined') return false
  
  try {
    const cookieName = 'cookie_consent'
    const nameEQ = cookieName + "="
    const ca = document.cookie.split(';')
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) {
        const cookieValue = c.substring(nameEQ.length, c.length)
        
        // Safely parse the cookie value
        try {
          let decodedValue = cookieValue
          
          // Check if the value is URL-encoded (starts with %)
          if (cookieValue.includes('%')) {
            decodedValue = decodeURIComponent(cookieValue)
          }
          
          // Validate that it's valid JSON before parsing
          if (!decodedValue.trim().startsWith('{') || !decodedValue.trim().endsWith('}')) {
            console.warn('Invalid cookie format, skipping:', decodedValue.substring(0, 50) + '...')
            continue
          }
          
          const consentData = JSON.parse(decodedValue)
          return consentData.preferences?.[type] === true
        } catch (parseError) {
          console.warn('Failed to parse cookie consent data:', parseError)
          // Try to clean up the cookie if it's malformed
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
          continue
        }
      }
    }
    
    return false
  } catch (error) {
    console.error('Error checking cookie consent:', error)
    return false
  }
}

// Utility function for conditional analytics tracking
export function trackEvent(eventName: string, properties?: Record<string, any>) {
  if (!isCookieAllowed('analytics')) {
    console.log('🍪 Analytics tracking blocked by cookie consent')
    return
  }

  // Analytics tracking
  console.log('📊 Analytics event tracked:', eventName, properties)
  
  // In production, add your analytics tracking code here
  // Example: Google Analytics 4
  // if (typeof window !== 'undefined' && window.gtag) {
  //   window.gtag('event', eventName, properties)
  // }
}

// Utility function for conditional marketing pixels
export function fireMarketingPixel(pixelName: string, data?: Record<string, any>) {
  if (!isCookieAllowed('marketing')) {
    console.log('🍪 Marketing pixel blocked by cookie consent')
    return
  }

  // Marketing pixel
  console.log('📈 Marketing pixel fired:', pixelName, data)
  
  // In production, add your marketing pixel code here
  // Example: Facebook Pixel
  // if (typeof window !== 'undefined' && window.fbq) {
  //   window.fbq('track', pixelName, data)
  // }
}

// Utility to clear all consent (for testing)
export async function clearConsent() {
  if (typeof window !== 'undefined') {
    await CookieConsentStorage.clearConsent()
    // Trigger consent update event
    window.dispatchEvent(new CustomEvent('cookieConsentUpdated'))
  }
}