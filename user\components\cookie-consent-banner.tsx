"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { 
  Cookie, 
  Shield, 
  Settings, 
  ChevronDown, 
  ChevronUp,
  Check,
  X,
  Info
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cookieConsentUtils } from '@/lib/cookie-consent-storage'

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface CookieConsentBannerProps {
  onConsentChange?: (preferences: CookiePreferences) => void
}

export default function CookieConsentBanner({ onConsentChange }: CookieConsentBannerProps) {
  const [showBanner, setShowBanner] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    functional: false
  })

  // Check if consent has been given
  useEffect(() => {
    checkConsentStatus()
    
    // Set up periodic check for consent status (every 30 seconds)
    // This will detect if consent was removed from database manually
    const intervalId = setInterval(() => {
      console.log('🍪 Periodic consent check...')
      checkConsentStatus()
    }, 30000) // Check every 30 seconds
    
    return () => {
      clearInterval(intervalId)
    }
  }, [])

  // Block scrolling and navigation when banner is shown
  useEffect(() => {
    if (showBanner) {
      // Disable scrolling
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overflow = 'hidden'
      
      // Block navigation
      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        e.preventDefault()
        e.returnValue = 'Please complete cookie consent before navigating away.'
        return 'Please complete cookie consent before navigating away.'
      }

      // Block keyboard navigation
      const handleKeyDown = (e: KeyboardEvent) => {
        // Block common navigation keys
        if (
          e.key === 'Tab' ||
          e.key === 'Enter' ||
          e.key === 'Space' ||
          e.key === 'ArrowUp' ||
          e.key === 'ArrowDown' ||
          e.key === 'PageUp' ||
          e.key === 'PageDown' ||
          e.key === 'Home' ||
          e.key === 'End' ||
          (e.ctrlKey && (e.key === 'f' || e.key === 'F')) || // Ctrl+F
          (e.ctrlKey && (e.key === 'l' || e.key === 'L')) || // Ctrl+L
          (e.altKey && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) // Alt+Arrow (browser back/forward)
        ) {
          // Allow interaction only within the cookie banner
          const target = e.target as HTMLElement
          const cookieBanner = document.querySelector('[data-cookie-banner]')
          if (!cookieBanner?.contains(target)) {
            e.preventDefault()
            e.stopPropagation()
          }
        }
      }

      // Block mouse wheel scrolling
      const handleWheel = (e: WheelEvent) => {
        e.preventDefault()
        e.stopPropagation()
      }

      // Block touch scrolling on mobile
      const handleTouchMove = (e: TouchEvent) => {
        const target = e.target as HTMLElement
        const cookieBanner = document.querySelector('[data-cookie-banner]')
        if (!cookieBanner?.contains(target)) {
          e.preventDefault()
        }
      }

      // Add event listeners
      window.addEventListener('beforeunload', handleBeforeUnload)
      document.addEventListener('keydown', handleKeyDown, true)
      document.addEventListener('wheel', handleWheel, { passive: false })
      document.addEventListener('touchmove', handleTouchMove, { passive: false })

      // Block link clicks outside cookie banner
      const handleClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement
        const cookieBanner = document.querySelector('[data-cookie-banner]')
        
        if (!cookieBanner?.contains(target)) {
          // Check if it's a link or button
          const link = target.closest('a, button')
          if (link && !cookieBanner?.contains(link)) {
            e.preventDefault()
            e.stopPropagation()
          }
        }
      }

      document.addEventListener('click', handleClick, true)

      return () => {
        // Cleanup event listeners
        window.removeEventListener('beforeunload', handleBeforeUnload)
        document.removeEventListener('keydown', handleKeyDown, true)
        document.removeEventListener('wheel', handleWheel)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('click', handleClick, true)
      }
    } else {
      // Re-enable scrolling when banner is hidden
      document.body.style.overflow = ''
      document.documentElement.style.overflow = ''
    }
  }, [showBanner])

  const checkConsentStatus = async () => {
    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.checkConsentStatus()
      
      console.log('🍪 Consent check result:', result)
      
      if (result.hasConsent && result.preferences) {
        setPreferences(result.preferences)
        setShowBanner(false)
        console.log('🍪 Existing consent found, banner hidden')
      } else {
        setShowBanner(true)
        console.log('🍪 No consent found, showing banner')
        
        // If database consent was cleared, show a message
        if (result.source === 'database_cleared') {
          console.log('🍪 Database consent was manually removed - showing banner')
        }
      }
    } catch (error) {
      console.error('Error checking consent status:', error)
      setShowBanner(true)
      console.log('🍪 Error checking consent, showing banner')
    }
  }

  const handleAcceptAll = async () => {
    const allAccepted: CookiePreferences = {
      essential: true,
      analytics: true,
      marketing: true,
      functional: true
    }
    await saveConsent(allAccepted, 'banner_accept_all')
  }

  const handleRejectAll = async () => {
    const essentialOnly: CookiePreferences = {
      essential: true,
      analytics: false,
      marketing: false,
      functional: false
    }
    await saveConsent(essentialOnly, 'banner_essential_only')
  }

  const handleSavePreferences = async () => {
    await saveConsent(preferences, 'banner_custom')
  }

  const saveConsent = async (consentPreferences: CookiePreferences, method: string) => {
    setIsLoading(true)
    try {
      // Use secure cookie storage with consent method
      const result = await cookieConsentUtils.saveConsent(consentPreferences, method)
      
      if (result.success) {
        setShowBanner(false)
        onConsentChange?.(consentPreferences)
        
        // Apply consent preferences
        applyConsentPreferences(consentPreferences)
        
        // Re-enable page interactions
        document.body.style.overflow = ''
        document.documentElement.style.overflow = ''
      } else {
        console.error('Failed to save consent preferences:', result.error)
      }
    } catch (error) {
      console.error('Error saving consent:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const applyConsentPreferences = (prefs: CookiePreferences) => {
    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: prefs
    }))

    // Apply analytics consent
    if (typeof window !== 'undefined') {
      if (prefs.analytics) {
        // Enable analytics tracking
        console.log('🍪 Analytics cookies enabled')
      } else {
        // Disable analytics tracking
        console.log('🍪 Analytics cookies disabled')
      }

      if (prefs.marketing) {
        // Enable marketing cookies
        console.log('🍪 Marketing cookies enabled')
      } else {
        // Disable marketing cookies
        console.log('🍪 Marketing cookies disabled')
      }

      if (prefs.functional) {
        // Enable functional cookies
        console.log('🍪 Functional cookies enabled')
      } else {
        // Disable functional cookies
        console.log('🍪 Functional cookies disabled')
      }
    }
  }

  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'essential') return // Cannot disable essential cookies
    setPreferences(prev => ({ ...prev, [key]: value }))
  }

  if (!showBanner) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
        data-cookie-banner
      >
        {/* Full screen overlay - blocks all interactions */}
        <div className="absolute inset-0 bg-black/60 dark:bg-black/80" />
        
        {/* Cookie consent modal */}
        <motion.div
          initial={{ scale: 0.9, y: 20 }}
          animate={{ scale: 1, y: 0 }}
          exit={{ scale: 0.9, y: 20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative w-full max-w-4xl max-h-[90vh] overflow-y-auto custom-scrollbar"
        >
          <Card className="bg-card border border-border shadow-2xl">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start gap-4 mb-6">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <Cookie className="w-6 h-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-card-foreground mb-3">
                    Cookie Consent Required
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    We need your consent to use cookies on our website. Please choose your preferences below 
                    to continue browsing. This is required for GDPR compliance and to ensure the best user experience.
                  </p>
                </div>
              </div>

              {/* Important Notice */}
              <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
                <div className="flex items-start gap-3">
                  <Info className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                      Consent Required to Continue
                    </h4>
                    <p className="text-sm text-amber-700 dark:text-amber-300">
                      You must provide your cookie preferences to access our website. 
                      Navigation and scrolling are disabled until consent is given.
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              {!showDetails && (
                <div className="flex flex-col sm:flex-row gap-3 mb-6">
                  <Button
                    onClick={handleAcceptAll}
                    disabled={isLoading}
                    className="flex-1 sm:flex-none"
                    size="lg"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Accept All & Continue
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleRejectAll}
                    disabled={isLoading}
                    className="flex-1 sm:flex-none"
                    size="lg"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Essential Only
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => setShowDetails(true)}
                    className="flex-1 sm:flex-none"
                    size="lg"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Customize
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              )}

              {/* Detailed Preferences */}
              <AnimatePresence>
                {showDetails && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <Separator className="mb-6" />
                    
                    <div className="space-y-4 mb-6">
                      {/* Essential Cookies */}
                      <div className="flex items-start justify-between p-4 bg-muted/50 rounded-lg border border-border/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Shield className="w-4 h-4 text-green-600" />
                            <h4 className="font-medium text-card-foreground">Essential Cookies</h4>
                            <Badge variant="secondary" className="text-xs">Required</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Necessary for basic website functionality, security, and user authentication.
                          </p>
                        </div>
                        <Switch
                          checked={preferences.essential}
                          disabled={true}
                          className="ml-4"
                        />
                      </div>

                      {/* Analytics Cookies */}
                      <div className="flex items-start justify-between p-4 bg-muted/50 rounded-lg border border-border/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Info className="w-4 h-4 text-blue-600" />
                            <h4 className="font-medium text-card-foreground">Analytics Cookies</h4>
                            <Badge variant="outline" className="text-xs">Optional</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Help us understand how visitors interact with our website to improve user experience.
                          </p>
                        </div>
                        <Switch
                          checked={preferences.analytics}
                          onCheckedChange={(checked) => updatePreference('analytics', checked)}
                          className="ml-4"
                        />
                      </div>

                      {/* Marketing Cookies */}
                      <div className="flex items-start justify-between p-4 bg-muted/50 rounded-lg border border-border/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Cookie className="w-4 h-4 text-purple-600" />
                            <h4 className="font-medium text-card-foreground">Marketing Cookies</h4>
                            <Badge variant="outline" className="text-xs">Optional</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Used to deliver personalized advertisements and measure marketing campaign effectiveness.
                          </p>
                        </div>
                        <Switch
                          checked={preferences.marketing}
                          onCheckedChange={(checked) => updatePreference('marketing', checked)}
                          className="ml-4"
                        />
                      </div>

                      {/* Functional Cookies */}
                      <div className="flex items-start justify-between p-4 bg-muted/50 rounded-lg border border-border/50">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Settings className="w-4 h-4 text-orange-600" />
                            <h4 className="font-medium text-card-foreground">Functional Cookies</h4>
                            <Badge variant="outline" className="text-xs">Optional</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Enable enhanced features like chat support, preferences, and personalized content.
                          </p>
                        </div>
                        <Switch
                          checked={preferences.functional}
                          onCheckedChange={(checked) => updatePreference('functional', checked)}
                          className="ml-4"
                        />
                      </div>
                    </div>

                    {/* Detailed Actions */}
                    <div className="flex flex-col sm:flex-row gap-3 mb-4">
                      <Button
                        onClick={handleSavePreferences}
                        disabled={isLoading}
                        className="flex-1"
                        size="lg"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Save Preferences & Continue
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowDetails(false)}
                        className="flex-1 sm:flex-none"
                        size="lg"
                      >
                        <ChevronUp className="w-4 h-4 mr-2" />
                        Hide Details
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Footer */}
              <div className="pt-4 border-t border-border">
                <p className="text-xs text-muted-foreground text-center">
                  By providing consent, you agree to our{' '}
                  <span className="text-primary font-medium">Privacy Policy</span>{' '}
                  and{' '}
                  <span className="text-primary font-medium">Terms of Service</span>
                  . You can change your preferences anytime after accessing the website.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}