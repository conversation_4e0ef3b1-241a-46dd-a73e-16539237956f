import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { Search, Filter, Calendar, User, Clock, BookOpen, TrendingUp, Eye, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export const metadata: Metadata = {
  title: "Blog | Benzochem Industries",
  description: "Industry insights, company updates, and chemical knowledge from our experts. Stay informed with the latest trends and innovations.",
}

export default function BlogPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium mb-6">
              <BookOpen className="w-4 h-4" />
              Knowledge Hub
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
              Blog & <span className="text-primary">Insights</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Stay informed with the latest industry trends, chemical innovations, 
              and expert insights from our team of professionals.
            </p>
          </div>

          {/* Featured Article */}
          <div className="bg-card border border-border rounded-2xl overflow-hidden mb-12 hover:shadow-lg transition-all duration-200">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              <div className="aspect-[16/10] lg:aspect-auto bg-muted flex items-center justify-center">
                <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-10 h-10 text-primary" />
                </div>
              </div>
              
              <div className="p-8 flex flex-col justify-center">
                <Badge className="w-fit mb-4 bg-primary/10 text-primary hover:bg-primary/10">
                  Featured Article
                </Badge>
                
                <h2 className="text-2xl lg:text-3xl font-bold text-card-foreground mb-4">
                  The Future of Sustainable Chemistry: Innovations and Trends
                </h2>
                <p className="text-muted-foreground mb-6">
                  Explore how the chemical industry is evolving towards more sustainable practices, 
                  featuring breakthrough technologies and eco-friendly solutions that are shaping the future.
                </p>
                
                <div className="flex items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback>DR</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-muted-foreground">Dr. Sarah Chen</span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Calendar className="w-4 h-4" />
                    Dec 15, 2024
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="w-4 h-4" />
                    8 min read
                  </div>
                </div>
                
                <Button className="w-fit">
                  Read Full Article
                </Button>
              </div>
            </div>
          </div>

          {/* Blog Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Total Articles</h3>
              <p className="text-2xl font-bold text-blue-600">247</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Eye className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Total Views</h3>
              <p className="text-2xl font-bold text-green-600">125K</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Comments</h3>
              <p className="text-2xl font-bold text-purple-600">3.2K</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <User className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Contributors</h3>
              <p className="text-2xl font-bold text-orange-600">12</p>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search articles..."
                    className="pl-10 focus:border-primary focus:ring-primary/20"
                  />
                </div>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="industry">Industry News</SelectItem>
                    <SelectItem value="innovation">Innovation</SelectItem>
                    <SelectItem value="sustainability">Sustainability</SelectItem>
                    <SelectItem value="research">Research</SelectItem>
                    <SelectItem value="company">Company Updates</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="trending">Trending</SelectItem>
                    <SelectItem value="oldest">Oldest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>

          {/* Articles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {Array.from({ length: 9 }).map((_, index) => {
              const categories = ["Industry News", "Innovation", "Sustainability", "Research", "Company Updates"]
              const authors = ["Dr. Sarah Chen", "Prof. Michael Kumar", "Dr. Emily Rodriguez", "James Wilson", "Dr. Lisa Park"]
              const category = categories[index % categories.length]
              const author = authors[index % authors.length]
              const views = Math.floor(Math.random() * 5000) + 500
              const comments = Math.floor(Math.random() * 50) + 5
              
              return (
                <article key={index} className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200">
                  <div className="aspect-[16/10] bg-muted flex items-center justify-center">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                      <BookOpen className="w-8 h-8 text-primary" />
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="secondary" className="text-xs">
                        {category}
                      </Badge>
                      <span className="text-xs text-muted-foreground">Dec {15 - index}, 2024</span>
                    </div>
                    
                    <h3 className="font-semibold text-card-foreground mb-3 line-clamp-2 hover:text-primary transition-colors cursor-pointer">
                      Advanced Chemical Processing Techniques for Modern Industry Applications
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                      Discover the latest advancements in chemical processing that are revolutionizing 
                      industrial applications and improving efficiency across various sectors.
                    </p>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">
                            {author.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">{author}</span>
                      </div>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {views.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <MessageCircle className="w-3 h-3" />
                          {comments}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {Math.floor(Math.random() * 10) + 3} min read
                      </span>
                      <Button size="sm" variant="ghost" className="h-8 text-primary hover:text-primary">
                        Read More →
                      </Button>
                    </div>
                  </div>
                </article>
              )
            })}
          </div>

          {/* Load More */}
          <div className="text-center mb-16">
            <Button variant="outline" size="lg" className="px-8">
              Load More Articles
            </Button>
          </div>

          {/* Newsletter Subscription */}
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Stay Updated with Our Latest Insights
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Subscribe to our newsletter and receive the latest articles, industry news, 
              and expert insights directly in your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1"
              />
              <Button className="px-6">
                Subscribe
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Join 10,000+ professionals staying informed about chemical industry trends
            </p>
          </div>
        </div>
      </div>
    </main>
  )
}
