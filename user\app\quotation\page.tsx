import type { Metadata } from "next"
import QuotationItems from "@/components/quotation-items"
import QuotationSummary from "@/components/quotation-summary"
import AuthGuard from "@/components/auth-guard"

export const metadata: Metadata = {
  title: "Request Quotation | Benzochem Industries",
  description: "Request quotes for chemical products with competitive pricing and expert consultation.",
}

export default function QuotationPage() {
  return (
    <AuthGuard>
      <main className="flex min-h-screen flex-col pt-9 bg-gradient-to-br from-background via-background to-secondary/5">
        <section className="py-12 sm:py-16">
          <div className="container mx-auto px-4 sm:px-6">
            {/* Modern Header */}
            <div className="mb-12 text-center">
              <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Request Quotation
              </div>
              <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-4">
                Get Your Custom Quote
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                Add products to your quotation request and receive competitive pricing with expert consultation from our chemical specialists.
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
              <div className="lg:col-span-2">
                <QuotationItems />
              </div>
              <div className="lg:col-span-1">
                <QuotationSummary />
              </div>
            </div>
          </div>
        </section>
      </main>
    </AuthGuard>
  )
}