export interface EmailTemplateResult {
  html: string
  text: string
}

export class EmailTemplate {
  private static baseStyles = `
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #1f2937;
        background-color: #f9fafb;
      }
      
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }
      
      .email-header {
        background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        padding: 40px 32px;
        text-align: center;
      }
      
      .email-logo {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 24px;
        font-weight: 700;
        color: white;
      }
      
      .email-title {
        color: white;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .email-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 400;
      }
      
      .email-content {
        padding: 40px 32px;
      }
      
      .content-section {
        margin-bottom: 32px;
      }
      
      .content-section:last-child {
        margin-bottom: 0;
      }
      
      .greeting {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 16px;
      }
      
      .message {
        font-size: 16px;
        color: #4b5563;
        line-height: 1.7;
        margin-bottom: 24px;
      }
      
      .cta-button {
        display: inline-block;
        background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        color: white;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        text-align: center;
        transition: all 0.2s ease;
        box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.3);
      }
      
      .cta-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 8px -1px rgba(8, 145, 178, 0.4);
      }
      
      .info-box {
        background-color: #f0f9ff;
        border: 1px solid #e0f2fe;
        border-radius: 8px;
        padding: 20px;
        margin: 24px 0;
      }
      
      .info-box-title {
        font-size: 16px;
        font-weight: 600;
        color: #0891b2;
        margin-bottom: 8px;
      }
      
      .info-box-content {
        font-size: 14px;
        color: #0e7490;
        line-height: 1.6;
      }
      
      .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .status-approved {
        background-color: #dcfce7;
        color: #166534;
      }
      
      .status-pending {
        background-color: #fef3c7;
        color: #92400e;
      }
      
      .status-rejected {
        background-color: #fee2e2;
        color: #991b1b;
      }
      
      .email-footer {
        background-color: #f9fafb;
        padding: 32px;
        text-align: center;
        border-top: 1px solid #e5e7eb;
      }
      
      .footer-content {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.6;
      }
      
      .footer-links {
        margin: 16px 0;
      }
      
      .footer-link {
        color: #0891b2;
        text-decoration: none;
        margin: 0 12px;
        font-weight: 500;
      }
      
      .footer-link:hover {
        text-decoration: underline;
      }
      
      .social-links {
        margin-top: 20px;
      }
      
      .social-link {
        display: inline-block;
        margin: 0 8px;
        width: 36px;
        height: 36px;
        background-color: #e5e7eb;
        border-radius: 50%;
        text-align: center;
        line-height: 36px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.2s ease;
      }
      
      .social-link:hover {
        background-color: #0891b2;
        color: white;
      }
      
      .divider {
        height: 1px;
        background: linear-gradient(to right, transparent, #e5e7eb, transparent);
        margin: 24px 0;
      }
      
      .highlight {
        background-color: #fef3c7;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
      }
      
      .quotation-details {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }
      
      .quotation-id {
        font-family: 'Courier New', monospace;
        background-color: #e2e8f0;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
      }
      
      @media only screen and (max-width: 600px) {
        .email-container {
          margin: 0;
          border-radius: 0;
        }
        
        .email-header,
        .email-content,
        .email-footer {
          padding: 24px 20px;
        }
        
        .email-title {
          font-size: 20px;
        }
        
        .cta-button {
          display: block;
          width: 100%;
          text-align: center;
        }
        
        .footer-link {
          display: block;
          margin: 8px 0;
        }
      }
    </style>
  `

  private static getBaseTemplate(content: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Benzochem Industries</title>
        ${this.baseStyles}
      </head>
      <body>
        <div style="background-color: #f9fafb; padding: 20px 0; min-height: 100vh;">
          <div class="email-container">
            ${content}
          </div>
        </div>
      </body>
      </html>
    `
  }

  private static getFooter(): string {
    return `
      <div class="email-footer">
        <div class="footer-content">
          <strong>Benzochem Industries</strong><br>
          Premium Chemical Solutions & Trading<br>
          E-45 Jitali Road<br>
          Phone: +91 83206 67594<br>
          Email: <EMAIL>
        </div>
        
        <div class="footer-links">
          <a href="#" class="footer-link">Privacy Policy</a>
          <a href="#" class="footer-link">Terms of Service</a>
          <a href="#" class="footer-link">Contact Support</a>
        </div>
        
        <div class="social-links">
          <a href="#" class="social-link">f</a>
          <a href="#" class="social-link">t</a>
          <a href="#" class="social-link">in</a>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
          © ${new Date().getFullYear()} Benzochem Industries. All rights reserved.
        </div>
      </div>
    `
  }

  static getPasswordResetTemplate(data: { resetLink: string; email: string; expiryTime: string }): EmailTemplateResult {
    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">Password Reset Request</h1>
        <p class="email-subtitle">Secure your account with a new password</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Hello,</h2>
          <p class="message">
            We received a request to reset the password for your Benzochem Industries account 
            associated with <span class="highlight">${data.email}</span>.
          </p>
          <p class="message">
            Click the button below to create a new password. This link will expire in 
            <strong>${data.expiryTime}</strong> for your security.
          </p>
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="${data.resetLink}" class="cta-button">Reset Your Password</a>
        </div>
        
        <div class="info-box">
          <div class="info-box-title">🔒 Security Notice</div>
          <div class="info-box-content">
            If you didn't request this password reset, please ignore this email. 
            Your account remains secure and no changes have been made.
          </div>
        </div>
        
        <div class="divider"></div>
        
        <p style="font-size: 14px; color: #6b7280; text-align: center;">
          If the button doesn't work, copy and paste this link into your browser:<br>
          <a href="${data.resetLink}" style="color: #0891b2; word-break: break-all;">${data.resetLink}</a>
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
Password Reset Request - Benzochem Industries

Hello,

We received a request to reset the password for your Benzochem Industries account associated with ${data.email}.

To reset your password, please visit the following link:
${data.resetLink}

This link will expire in ${data.expiryTime} for your security.

If you didn't request this password reset, please ignore this email. Your account remains secure and no changes have been made.

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getWelcomeTemplate(data: { firstName: string; email: string; loginLink: string; supportEmail: string }): EmailTemplateResult {
    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">Welcome to Benzochem Industries</h1>
        <p class="email-subtitle">Your journey with premium chemicals begins here</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Welcome, ${data.firstName}!</h2>
          <p class="message">
            Thank you for registering with Benzochem Industries. We're excited to have you join our 
            community of professionals in the chemical industry.
          </p>
          <p class="message">
            Your account has been created successfully and is currently under review. Our team will 
            verify your information to ensure compliance with our quality standards.
          </p>
        </div>
        
        <div class="info-box">
          <div class="info-box-title">📋 What's Next?</div>
          <div class="info-box-content">
            <strong>1. Account Review:</strong> Our team will review your application within 24-48 hours<br>
            <strong>2. Email Notification:</strong> You'll receive an email once your account is approved<br>
            <strong>3. Start Shopping:</strong> Access our premium chemical products and services
          </div>
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="${data.loginLink}" class="cta-button">Access Your Account</a>
        </div>
        
        <div class="divider"></div>
        
        <p class="message">
          If you have any questions or need assistance, please don't hesitate to contact our 
          support team at <a href="mailto:${data.supportEmail}" style="color: #0891b2;">${data.supportEmail}</a>.
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
Welcome to Benzochem Industries

Welcome, ${data.firstName}!

Thank you for registering with Benzochem Industries. We're excited to have you join our community of professionals in the chemical industry.

Your account has been created successfully and is currently under review. Our team will verify your information to ensure compliance with our quality standards.

What's Next?
1. Account Review: Our team will review your application within 24-48 hours
2. Email Notification: You'll receive an email once your account is approved
3. Start Shopping: Access our premium chemical products and services

Access your account: ${data.loginLink}

If you have any questions or need assistance, please contact our support team at ${data.supportEmail}.

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getAccountStatusTemplate(data: { 
    firstName: string; 
    email: string; 
    status: 'approved' | 'rejected' | 'pending';
    loginLink: string;
    contactLink: string;
    supportEmail: string;
  }): EmailTemplateResult {
    const statusConfig = {
      approved: {
        title: 'Account Approved',
        subtitle: 'Welcome to Benzochem Industries',
        badge: 'status-approved',
        badgeText: 'Approved',
        message: 'Congratulations! Your account has been approved and you now have full access to our premium chemical products and services.',
        cta: 'Access Your Dashboard',
        ctaLink: data.loginLink
      },
      rejected: {
        title: 'Application Update',
        subtitle: 'Thank you for your interest',
        badge: 'status-rejected',
        badgeText: 'Not Approved',
        message: 'Unfortunately, your application does not meet our current requirements. This decision is based on our strict quality and compliance standards.',
        cta: 'Contact Support',
        ctaLink: data.contactLink
      },
      pending: {
        title: 'Application Under Review',
        subtitle: 'We\'re reviewing your application',
        badge: 'status-pending',
        badgeText: 'Under Review',
        message: 'Your application is currently being reviewed by our team. We\'ll notify you once the review process is complete.',
        cta: 'Check Status',
        ctaLink: data.loginLink
      }
    }

    const config = statusConfig[data.status]

    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">${config.title}</h1>
        <p class="email-subtitle">${config.subtitle}</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Hello, ${data.firstName}!</h2>
          <div style="text-align: center; margin: 24px 0;">
            <span class="status-badge ${config.badge}">${config.badgeText}</span>
          </div>
          <p class="message">${config.message}</p>
          
          ${data.status === 'approved' ? `
            <div class="info-box">
              <div class="info-box-title">🎉 You can now:</div>
              <div class="info-box-content">
                <strong>• Browse Products:</strong> Access our complete catalog of chemical products<br>
                <strong>• Request Quotations:</strong> Get competitive pricing for bulk orders<br>
                <strong>• Track Orders:</strong> Monitor your order status and delivery updates<br>
                <strong>• Premium Support:</strong> Get priority customer service
              </div>
            </div>
          ` : data.status === 'rejected' ? `
            <div class="info-box">
              <div class="info-box-title">📞 Need Help?</div>
              <div class="info-box-content">
                Please contact our support team for detailed feedback and potential next steps. 
                We're here to help you understand our requirements and assist with any questions.
              </div>
            </div>
          ` : `
            <div class="info-box">
              <div class="info-box-title">⏱️ Review Process</div>
              <div class="info-box-content">
                Our team is carefully evaluating your application to ensure compliance with our 
                quality standards. You'll receive another email once the review is complete.
              </div>
            </div>
          `}
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="${config.ctaLink}" class="cta-button">${config.cta}</a>
        </div>
        
        <div class="divider"></div>
        
        <p class="message">
          For any questions or assistance, please contact us at 
          <a href="mailto:${data.supportEmail}" style="color: #0891b2;">${data.supportEmail}</a>.
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
${config.title} - Benzochem Industries

Hello, ${data.firstName}!

Status: ${config.badgeText}

${config.message}

${data.status === 'approved' ? `
You can now:
• Browse Products: Access our complete catalog of chemical products
• Request Quotations: Get competitive pricing for bulk orders
• Track Orders: Monitor your order status and delivery updates
• Premium Support: Get priority customer service
` : data.status === 'rejected' ? `
Please contact our support team for detailed feedback and potential next steps. We're here to help you understand our requirements and assist with any questions.
` : `
Our team is carefully evaluating your application to ensure compliance with our quality standards. You'll receive another email once the review is complete.
`}

${config.cta}: ${config.ctaLink}

For any questions or assistance, please contact us at ${data.supportEmail}.

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getQuotationTemplate(data: {
    firstName: string;
    email: string;
    quotationId: string;
    type: 'created' | 'updated' | 'approved' | 'rejected';
    quotationLink: string;
    supportEmail: string;
  }): EmailTemplateResult {
    const typeConfig = {
      created: {
        title: 'Quotation Request Received',
        subtitle: 'We\'re preparing your quote',
        message: 'Thank you for your quotation request. Our team is reviewing your requirements and will provide a competitive quote soon.',
        cta: 'View Quotation'
      },
      updated: {
        title: 'Quotation Updated',
        subtitle: 'Your quote has been revised',
        message: 'Your quotation has been updated with new pricing or product information. Please review the changes.',
        cta: 'Review Updates'
      },
      approved: {
        title: 'Quotation Approved',
        subtitle: 'Ready to proceed with your order',
        message: 'Great news! Your quotation has been approved. You can now proceed with placing your order.',
        cta: 'Place Order'
      },
      rejected: {
        title: 'Quotation Update',
        subtitle: 'Alternative options available',
        message: 'We\'re unable to fulfill your current quotation request, but we have alternative solutions that might meet your needs.',
        cta: 'View Alternatives'
      }
    }

    const config = typeConfig[data.type]

    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">${config.title}</h1>
        <p class="email-subtitle">${config.subtitle}</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Hello, ${data.firstName}!</h2>
          <p class="message">${config.message}</p>
        </div>
        
        <div class="quotation-details">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
            <strong>Quotation ID:</strong>
            <span class="quotation-id">${data.quotationId}</span>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <strong>Status:</strong>
            <span class="status-badge ${data.type === 'approved' ? 'status-approved' : data.type === 'rejected' ? 'status-rejected' : 'status-pending'}">
              ${data.type.charAt(0).toUpperCase() + data.type.slice(1)}
            </span>
          </div>
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="${data.quotationLink}" class="cta-button">${config.cta}</a>
        </div>
        
        <div class="info-box">
          <div class="info-box-title">💡 Need Assistance?</div>
          <div class="info-box-content">
            Our team is available to help with any questions about your quotation, 
            product specifications, or ordering process.
          </div>
        </div>
        
        <div class="divider"></div>
        
        <p class="message">
          For questions about this quotation, please contact us at 
          <a href="mailto:${data.supportEmail}" style="color: #0891b2;">${data.supportEmail}</a> 
          and reference quotation ID <strong>${data.quotationId}</strong>.
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
${config.title} - Benzochem Industries

Hello, ${data.firstName}!

${config.message}

Quotation ID: ${data.quotationId}
Status: ${data.type.charAt(0).toUpperCase() + data.type.slice(1)}

${config.cta}: ${data.quotationLink}

For questions about this quotation, please contact us at ${data.supportEmail} and reference quotation ID ${data.quotationId}.

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getContactFormTemplate(data: {
    name: string;
    email: string;
    message: string;
    timestamp: string;
  }): EmailTemplateResult {
    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">New Contact Form Submission</h1>
        <p class="email-subtitle">Someone has reached out through your website</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">New Contact Form Submission</h2>
          <p class="message">
            You have received a new message through the contact form on your website.
          </p>
        </div>
        
        <div class="quotation-details">
          <div style="margin-bottom: 16px;">
            <strong>Name:</strong> ${data.name}
          </div>
          <div style="margin-bottom: 16px;">
            <strong>Email:</strong> <a href="mailto:${data.email}" style="color: #0891b2;">${data.email}</a>
          </div>
          <div style="margin-bottom: 16px;">
            <strong>Submitted:</strong> ${data.timestamp}
          </div>
          <div>
            <strong>Message:</strong><br>
            <div style="background-color: #f8fafc; padding: 16px; border-radius: 6px; margin-top: 8px; white-space: pre-wrap;">${data.message}</div>
          </div>
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="mailto:${data.email}?subject=Re: Your inquiry to Benzochem Industries" class="cta-button">Reply to ${data.name}</a>
        </div>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
New Contact Form Submission - Benzochem Industries

You have received a new message through the contact form on your website.

Name: ${data.name}
Email: ${data.email}
Submitted: ${data.timestamp}

Message:
${data.message}

Reply to: ${data.email}

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getNewsletterTemplate(data: {
    firstName: string;
    email: string;
    title: string;
    excerpt: string;
    articles: Array<{ title: string; link: string; excerpt: string }>;
    unsubscribeLink: string;
    websiteLink: string;
  }): EmailTemplateResult {
    const articlesHtml = data.articles.map(article => `
      <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
        <h3 style="font-size: 18px; font-weight: 600; color: #111827; margin-bottom: 12px;">
          <a href="${article.link}" style="color: #111827; text-decoration: none;">${article.title}</a>
        </h3>
        <p style="font-size: 14px; color: #6b7280; line-height: 1.6; margin-bottom: 16px;">
          ${article.excerpt}
        </p>
        <a href="${article.link}" style="color: #0891b2; font-weight: 500; text-decoration: none;">
          Read More →
        </a>
      </div>
    `).join('')

    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">${data.title}</h1>
        <p class="email-subtitle">${data.excerpt}</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Hello, ${data.firstName}!</h2>
          <p class="message">
            Here's the latest news and updates from Benzochem Industries. Stay informed about 
            industry trends, new products, and company developments.
          </p>
        </div>
        
        <div class="content-section">
          <h3 style="font-size: 20px; font-weight: 600; color: #111827; margin-bottom: 20px;">Featured Articles</h3>
          ${articlesHtml}
        </div>
        
        <div class="content-section" style="text-align: center;">
          <a href="${data.websiteLink}" class="cta-button">Visit Our Website</a>
        </div>
        
        <div class="divider"></div>
        
        <p style="font-size: 12px; color: #9ca3af; text-align: center;">
          You're receiving this email because you subscribed to our newsletter.<br>
          <a href="${data.unsubscribeLink}" style="color: #6b7280;">Unsubscribe</a> | 
          <a href="${data.websiteLink}" style="color: #6b7280;">Update Preferences</a>
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const articlesText = data.articles.map(article => `
${article.title}
${article.excerpt}
Read more: ${article.link}
    `).join('\n')

    const text = `
${data.title} - Benzochem Industries

Hello, ${data.firstName}!

${data.excerpt}

Here's the latest news and updates from Benzochem Industries. Stay informed about industry trends, new products, and company developments.

Featured Articles:
${articlesText}

Visit our website: ${data.websiteLink}

You're receiving this email because you subscribed to our newsletter.
Unsubscribe: ${data.unsubscribeLink}

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getContactConfirmationTemplate(data: {
    firstName: string;
    message: string;
    supportEmail: string;
  }): EmailTemplateResult {
    const html = this.getBaseTemplate(`
      <div class="email-header">
        <div class="email-logo">B</div>
        <h1 class="email-title">Message Received</h1>
        <p class="email-subtitle">Thank you for contacting us</p>
      </div>
      
      <div class="email-content">
        <div class="content-section">
          <h2 class="greeting">Hello, ${data.firstName}!</h2>
          <p class="message">${data.message}</p>
        </div>
        
        <div class="info-box">
          <div class="info-box-title">📞 What happens next?</div>
          <div class="info-box-content">
            <strong>1. Review:</strong> Our team will carefully review your message<br>
            <strong>2. Response:</strong> We'll get back to you within 24 hours<br>
            <strong>3. Follow-up:</strong> If needed, we'll schedule a call to discuss your requirements
          </div>
        </div>
        
        <div class="divider"></div>
        
        <p class="message">
          If you have any urgent questions, please don't hesitate to contact us directly at 
          <a href="mailto:${data.supportEmail}" style="color: #0891b2;">${data.supportEmail}</a> 
          or call us at +91 83206 67594.
        </p>
      </div>
      
      ${this.getFooter()}
    `)

    const text = `
Message Received - Benzochem Industries

Hello, ${data.firstName}!

${data.message}

What happens next?
1. Review: Our team will carefully review your message
2. Response: We'll get back to you within 24 hours
3. Follow-up: If needed, we'll schedule a call to discuss your requirements

If you have any urgent questions, please contact us directly at ${data.supportEmail} or call us at +91 83206 67594.

---
Benzochem Industries
Premium Chemical Solutions & Trading
E-45 Jitali Road
Phone: +91 83206 67594
Email: <EMAIL>
    `

    return { html, text }
  }

  static getTemplate(templateName: string, data: Record<string, any>): EmailTemplateResult {
    switch (templateName) {
      case 'password-reset':
        return this.getPasswordResetTemplate(data)
      case 'welcome':
        return this.getWelcomeTemplate(data)
      case 'account-status':
        return this.getAccountStatusTemplate(data)
      case 'quotation':
        return this.getQuotationTemplate(data)
      case 'contact-form':
        return this.getContactFormTemplate(data)
      case 'contact-confirmation':
        return this.getContactConfirmationTemplate(data)
      case 'newsletter':
        return this.getNewsletterTemplate(data)
      default:
        throw new Error(`Unknown email template: ${templateName}`)
    }
  }
}