import type { <PERSON>ada<PERSON> } from "next"
import RegisterForm from "@/components/register-form"
import Link from "next/link"
import { ArrowLeft, Shield, Clock, CheckCircle, Users, Zap, Award } from "lucide-react"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Register | Benzochem Industries",
  description: "Create a new account with Benzochem Industries",
}

export default function RegisterPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start min-h-[calc(100vh-8rem)]">
            
            {/* Left side - Branding and Benefits */}
            <div className="space-y-8 lg:pr-8 lg:sticky lg:top-8">
              {/* Back to Login */}
              <Link 
                href="/login" 
                className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Sign In
              </Link>

              {/* Hero Section */}
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
                  <Users className="w-4 h-4" />
                  Join 500+ Businesses
                </div>
                
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight">
                    Join Our Network of
                    <span className="block text-primary">Trusted Partners</span>
                  </h1>
                  <p className="text-lg text-muted-foreground max-w-lg">
                    Create your account to access exclusive pricing, streamlined quotations, and dedicated support for your business growth. All fields are required for registration.
                  </p>
                </div>
              </div>

              {/* Benefits Grid */}
              <div className="space-y-4">
                <h3 className="font-semibold text-foreground mb-4">What you'll get:</h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 rounded-lg bg-card/50 border border-border/50">
                    <div className="p-1.5 bg-green-100 rounded-lg">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-card-foreground text-sm">Exclusive Pricing</h4>
                      <p className="text-xs text-muted-foreground">Access to wholesale rates and bulk discounts</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 rounded-lg bg-card/50 border border-border/50">
                    <div className="p-1.5 bg-blue-100 rounded-lg">
                      <Zap className="w-4 h-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-card-foreground text-sm">Quick Quotations</h4>
                      <p className="text-xs text-muted-foreground">Instant quotes and streamlined ordering process</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-3 rounded-lg bg-card/50 border border-border/50">
                    <div className="p-1.5 bg-purple-100 rounded-lg">
                      <Award className="w-4 h-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-card-foreground text-sm">Dedicated Support</h4>
                      <p className="text-xs text-muted-foreground">Personal account manager for your business</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Process Timeline */}
              <div className="bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Clock className="w-5 h-5 text-primary" />
                  <h4 className="font-semibold text-card-foreground">Registration Process</h4>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">1</div>
                    <span className="text-muted-foreground">Complete registration form</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-xs font-medium">2</div>
                    <span className="text-muted-foreground">Admin review (24-48 hours)</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-xs font-medium">3</div>
                    <span className="text-muted-foreground">Account activation & welcome</span>
                  </div>
                </div>
              </div>

              {/* Security Badge */}
              <div className="hidden lg:block">
                <div className="flex items-center gap-3 p-4 bg-card/30 border border-border rounded-xl">
                  <Shield className="w-8 h-8 text-primary" />
                  <div>
                    <h4 className="font-semibold text-card-foreground text-sm">Enterprise Security</h4>
                    <p className="text-xs text-muted-foreground">Your data is protected with bank-level encryption</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Registration Form */}
            <div className="w-full max-w-lg mx-auto lg:mx-0">
              <div className="bg-card border border-border rounded-2xl shadow-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-card-foreground mb-2">Create Your Account</h2>
                  <p className="text-muted-foreground">Join our network of trusted business partners</p>
                </div>

                <Suspense fallback={
                  <div className="space-y-4">
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-32 rounded-lg"></div>
                  </div>
                }>
                  <RegisterForm />
                </Suspense>

                {/* Terms and Privacy */}
                <div className="mt-8 text-center">
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    By creating an account, you agree to our{" "}
                    <Link href="/terms" className="text-primary hover:text-primary/80 font-medium transition-colors duration-200">
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link href="/privacy" className="text-primary hover:text-primary/80 font-medium transition-colors duration-200">
                      Privacy Policy
                    </Link>
                  </p>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="mt-6 text-center">
                <p className="text-xs text-muted-foreground mb-3">Trusted by leading businesses</p>
                <div className="flex items-center justify-center gap-4 opacity-60">
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
