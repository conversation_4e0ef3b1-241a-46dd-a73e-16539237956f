import type { Metadata } from "next"
import { Suspense } from "react" // Added
import Hero from "@/components/hero"
import CategorySection from "@/components/category-section"
import FeaturedProducts from "@/components/featured-products"
import FeaturedProductsSkeleton from "@/components/featured-products-skeleton" // Added
import Newsletter from "@/components/newsletter"

export const metadata: Metadata = {
  title: "Benzochem Industries | Premium Chemical Products",
  description:
    "Specialized chemical trading company offering high-quality powder and liquid products for industrial applications.",
}

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      <Hero />
      <section className="py-20 md:py-32 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10" />
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
        
        <div className="container mx-auto px-4 relative z-10">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              Product Categories
            </div>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-tight">
              Our Product Categories
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Discover our comprehensive range of high-quality chemical products, carefully curated for industrial and laboratory applications.
            </p>
          </div>

          {/* Categories Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
            <CategorySection
              title="Powder Products"
              description="High-purity powder chemicals for industrial applications with guaranteed quality and consistency"
              image="https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?q=80&w=2070&auto=format&fit=crop"
              href="/categories/powder"
              features={["99.9% Purity", "ISO Certified", "Bulk Available"]}
            />
            <CategorySection
              title="Liquid Products"
              description="Premium liquid solutions for specialized chemical processes and advanced manufacturing"
              image="https://images.visualelectric.com/08e4505c-6e36-4d72-91f0-74a3d8b72982/large"
              href="/categories/liquid"
              features={["Laboratory Grade", "Custom Formulations", "Fast Delivery"]}
            />
          </div>
        </div>
      </section>
      <section className="py-20 md:py-32 bg-vanilla-gradient dark:bg-dark-gradient relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-transparent to-secondary/10" />
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
        
        <div className="container mx-auto px-4 relative z-10">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
              Featured Products
            </div>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-tight">
              Featured Products
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
              Explore our most popular and trusted chemical products, selected for their exceptional quality and proven performance.
            </p>
          </div>

          <Suspense fallback={<FeaturedProductsSkeleton />}>
            <FeaturedProducts />
          </Suspense>
        </div>
      </section>
      <Newsletter />
    </main>
  )
}