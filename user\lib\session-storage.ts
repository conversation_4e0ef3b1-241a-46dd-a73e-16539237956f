// Enhanced server-side session management using secure HTTP cookies
// Completely replaces localStorage usage for sensitive session data

export interface SessionData {
  userId: string
  email: string
  timestamp: string
  version: string
  sessionId: string // Add unique session identifier
  lastActivity: string // Track last activity for security
}

const SESSION_COOKIE_NAME = 'user_session'
const SESSION_VERSION = '2.0' // Updated version for enhanced security
const SESSION_MAX_AGE = 30 * 24 * 60 * 60 // 30 days in seconds
const ACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000 // 24 hours of inactivity before requiring re-auth

export class SessionStorage {
  // Generate secure session ID
  private static generateSessionId(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Validate session data structure and security
  private static validateSessionData(data: any): data is SessionData {
    return data && 
           typeof data.userId === 'string' && 
           typeof data.email === 'string' && 
           typeof data.timestamp === 'string' && 
           typeof data.sessionId === 'string' &&
           typeof data.lastActivity === 'string' &&
           data.version === SESSION_VERSION;
  }

  // Check if session exists and is valid
  static hasSession(): boolean {
    if (typeof window === 'undefined') return false
    
    try {
      const cookieValue = this.getCookieValue(SESSION_COOKIE_NAME)
      if (!cookieValue) return false
      
      const data = JSON.parse(decodeURIComponent(cookieValue))
      if (!this.validateSessionData(data)) {
        this.clearSession()
        return false
      }

      // Check session expiration
      const sessionDate = new Date(data.timestamp)
      const lastActivity = new Date(data.lastActivity)
      const now = new Date()
      
      // Check if session is expired (30 days from creation)
      const daysSinceCreation = Math.ceil((now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24))
      if (daysSinceCreation > 30) {
        this.clearSession()
        return false
      }

      // Check if session is inactive (24 hours since last activity)
      const timeSinceActivity = now.getTime() - lastActivity.getTime()
      if (timeSinceActivity > ACTIVITY_TIMEOUT) {
        this.clearSession()
        return false
      }

      return true
    } catch (error) {
      console.error('Error checking session:', error)
      this.clearSession()
      return false
    }
  }

  // Get stored session data with validation
  static getSession(): SessionData | null {
    if (typeof window === 'undefined') return null
    
    try {
      const cookieValue = this.getCookieValue(SESSION_COOKIE_NAME)
      if (!cookieValue) return null
      
      const data = JSON.parse(decodeURIComponent(cookieValue))

      // Validate data structure and version
      if (!this.validateSessionData(data)) {
        console.warn('Invalid session data structure, clearing session')
        this.clearSession()
        return null
      }

      // Check session expiration
      const sessionDate = new Date(data.timestamp)
      const lastActivity = new Date(data.lastActivity)
      const now = new Date()
      
      // Check if session is expired (30 days from creation)
      const daysSinceCreation = Math.ceil((now.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24))
      if (daysSinceCreation > 30) {
        console.log('Session expired (30 days), clearing session')
        this.clearSession()
        return null
      }

      // Check if session is inactive (24 hours since last activity)
      const timeSinceActivity = now.getTime() - lastActivity.getTime()
      if (timeSinceActivity > ACTIVITY_TIMEOUT) {
        console.log('Session inactive (24 hours), clearing session')
        this.clearSession()
        return null
      }

      return data
    } catch (error) {
      console.error('Error getting session:', error)
      this.clearSession()
      return null
    }
  }

  // Store session data in secure cookie with enhanced security
  static setSession(userId: string, email: string): boolean {
    if (typeof window === 'undefined') return false
    
    try {
      // Input validation
      if (!userId || !email) {
        console.error('Invalid session data: userId and email are required')
        return false
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        console.error('Invalid email format for session')
        return false
      }

      const now = new Date().toISOString()
      const sessionData: SessionData = {
        userId: userId.trim(),
        email: email.toLowerCase().trim(),
        timestamp: now,
        lastActivity: now,
        sessionId: this.generateSessionId(),
        version: SESSION_VERSION
      }
      
      const cookieValue = encodeURIComponent(JSON.stringify(sessionData))
      const expires = new Date(Date.now() + SESSION_MAX_AGE * 1000).toUTCString()
      
      // Set secure cookie with enhanced security attributes
      const isSecure = location.protocol === 'https:'
      const cookieString = `${SESSION_COOKIE_NAME}=${cookieValue}; expires=${expires}; path=/; SameSite=Strict${isSecure ? '; Secure' : ''}`
      
      document.cookie = cookieString
      
      console.log('🔐 User session stored securely', {
        userId: userId.substring(0, 8) + '...', // Log partial ID for privacy
        email: email.split('@')[0] + '@***', // Log partial email for privacy
        sessionId: sessionData.sessionId.substring(0, 8) + '...',
        timestamp: sessionData.timestamp,
        secure: isSecure
      })
      
      return true
    } catch (error) {
      console.error('Error storing session:', error)
      return false
    }
  }

  // Update last activity timestamp
  static updateActivity(): boolean {
    const session = this.getSession()
    if (!session) return false

    try {
      const updatedSession: SessionData = {
        ...session,
        lastActivity: new Date().toISOString()
      }

      const cookieValue = encodeURIComponent(JSON.stringify(updatedSession))
      const expires = new Date(Date.now() + SESSION_MAX_AGE * 1000).toUTCString()
      const isSecure = location.protocol === 'https:'
      
      document.cookie = `${SESSION_COOKIE_NAME}=${cookieValue}; expires=${expires}; path=/; SameSite=Strict${isSecure ? '; Secure' : ''}`
      
      return true
    } catch (error) {
      console.error('Error updating session activity:', error)
      return false
    }
  }

  // Clear session cookie securely
  static clearSession(): void {
    if (typeof window === 'undefined') return
    
    try {
      // Set cookie with past expiration date to delete it
      document.cookie = `${SESSION_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict`
      console.log('🔐 User session cleared securely')
    } catch (error) {
      console.error('Error clearing session:', error)
    }
  }

  // Get session age in days
  static getSessionAge(): number {
    const session = this.getSession()
    if (!session) return 0
    
    const sessionDate = new Date(session.timestamp)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - sessionDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  // Check if session needs renewal (after 25 days, prompt for renewal)
  static needsRenewal(): boolean {
    return this.getSessionAge() > 25
  }

  // Get time since last activity in hours
  static getInactivityHours(): number {
    const session = this.getSession()
    if (!session) return 0

    const lastActivity = new Date(session.lastActivity)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - lastActivity.getTime())
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60))

    return diffHours
  }

  // Update session timestamp (extend session)
  static refreshSession(): boolean {
    const session = this.getSession()
    if (!session) return false
    
    return this.setSession(session.userId, session.email)
  }

  // Helper method to get cookie value by name
  private static getCookieValue(name: string): string | null {
    if (typeof window === 'undefined') return null
    
    const nameEQ = name + "="
    const ca = document.cookie.split(';')
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
    }
    
    return null
  }

  // Get session info for debugging (safe for logging)
  static getSessionInfo(): { age: number; inactivity: number; needsRenewal: boolean; hasSession: boolean } {
    return {
      age: this.getSessionAge(),
      inactivity: this.getInactivityHours(),
      needsRenewal: this.needsRenewal(),
      hasSession: this.hasSession()
    }
  }
}

// Enhanced session utility functions for compatibility
export const sessionUtils = {
  // Get current user session
  getCurrentUserSession(): { userId: string; email: string } | null {
    const session = SessionStorage.getSession()
    if (!session) return null
    
    // Update activity on session access
    SessionStorage.updateActivity()
    
    return {
      userId: session.userId,
      email: session.email
    }
  },

  // Set current user session
  setCurrentUserSession(session: { userId: string; email: string } | null): void {
    if (session) {
      SessionStorage.setSession(session.userId, session.email)
    } else {
      SessionStorage.clearSession()
    }
  },

  // Check if session exists
  hasSession(): boolean {
    return SessionStorage.hasSession()
  },

  // Clear session
  clearSession(): void {
    SessionStorage.clearSession()
  },

  // Refresh session
  refreshSession(): boolean {
    return SessionStorage.refreshSession()
  },

  // Get session information for debugging
  getSessionInfo(): { age: number; inactivity: number; needsRenewal: boolean; hasSession: boolean } {
    return SessionStorage.getSessionInfo()
  },

  // Update activity timestamp
  updateActivity(): boolean {
    return SessionStorage.updateActivity()
  }
}