/**
 * Real-time Notification Context
 * Provides global notification state and actions across the application
 */

"use client";

import React, { createContext, useContext, useCallback, useEffect } from "react";
import { useConvex } from "convex/react";
import { getNotificationService, NotificationData } from "@/lib/notification-service";
import { useNotifications } from "@/hooks/use-notifications";
import { toast } from "sonner";

interface NotificationContextType {
  // Notification data
  unreadCount: number;
  recentNotifications: any[];
  hasUnreadNotifications: boolean;
  
  // Actions
  createNotification: (data: NotificationData) => Promise<string | null>;
  markAsRead: (notificationId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  deleteNotification: (notificationId: string) => Promise<boolean>;
  
  // Convenience methods for common notifications
  notifyUserRegistration: (userData: {
    firstName: string;
    lastName: string;
    email: string;
    businessName?: string;
    userId: string;
  }) => Promise<string | null>;
  
  notifyNewQuotation: (quotationData: {
    quotationId: string;
    userName: string;
    userEmail: string;
    businessName?: string;
    productCount: number;
    urgency?: string;
  }) => Promise<string | null>;
  
  notifySystemAlert: (alertData: {
    title: string;
    message: string;
    priority?: "low" | "medium" | "high" | "urgent";
  }) => Promise<string | null>;
  
  // Cleanup
  cleanupExpiredNotifications: () => Promise<string[]>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const convex = useConvex();
  const notificationService = getNotificationService(convex);
  
  // Get real-time notification data
  const {
    recentNotifications,
    unreadCount,
    hasUnreadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    createNotification,
    cleanupExpired,
  } = useNotifications({
    limit: 10,
    offset: 0,
  });

  // Convenience methods using the notification service
  const notifyUserRegistration = useCallback(async (userData: {
    firstName: string;
    lastName: string;
    email: string;
    businessName?: string;
    userId: string;
  }) => {
    return await notificationService.notifyUserRegistration(userData);
  }, [notificationService]);

  const notifyNewQuotation = useCallback(async (quotationData: {
    quotationId: string;
    userName: string;
    userEmail: string;
    businessName?: string;
    productCount: number;
    urgency?: string;
  }) => {
    return await notificationService.notifyNewQuotation(quotationData);
  }, [notificationService]);

  const notifySystemAlert = useCallback(async (alertData: {
    title: string;
    message: string;
    priority?: "low" | "medium" | "high" | "urgent";
  }) => {
    return await notificationService.notifySystemAlert(alertData);
  }, [notificationService]);

  const cleanupExpiredNotifications = useCallback(async () => {
    return await cleanupExpired();
  }, [cleanupExpired]);

  // Auto-cleanup expired notifications every hour
  useEffect(() => {
    const interval = setInterval(() => {
      cleanupExpiredNotifications();
    }, 60 * 60 * 1000); // 1 hour

    return () => clearInterval(interval);
  }, [cleanupExpiredNotifications]);

  // Show toast notifications for new unread notifications
  useEffect(() => {
    if (recentNotifications && recentNotifications.length > 0) {
      const latestNotification = recentNotifications[0];
      
      // Only show toast for very recent notifications (within last 30 seconds)
      const isVeryRecent = Date.now() - latestNotification.createdAt < 30000;
      
      if (!latestNotification.isRead && isVeryRecent) {
        // Show different toast styles based on priority
        switch (latestNotification.priority) {
          case "urgent":
            toast.error(latestNotification.title, {
              description: latestNotification.message,
              duration: 10000,
            });
            break;
          case "high":
            toast.warning(latestNotification.title, {
              description: latestNotification.message,
              duration: 8000,
            });
            break;
          default:
            toast.info(latestNotification.title, {
              description: latestNotification.message,
              duration: 5000,
            });
            break;
        }
      }
    }
  }, [recentNotifications]);

  const contextValue: NotificationContextType = {
    // Data
    unreadCount: unreadCount || 0,
    recentNotifications: recentNotifications || [],
    hasUnreadNotifications,
    
    // Actions
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    
    // Convenience methods
    notifyUserRegistration,
    notifyNewQuotation,
    notifySystemAlert,
    
    // Cleanup
    cleanupExpiredNotifications,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotificationContext() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotificationContext must be used within a NotificationProvider");
  }
  return context;
}

// Hook for creating specific types of notifications
export function useNotificationActions() {
  const context = useNotificationContext();
  
  return {
    // User-related notifications
    notifyUserRegistration: context.notifyUserRegistration,
    notifyUserApproval: useCallback(async (userData: {
      firstName: string;
      lastName: string;
      email: string;
      userId: string;
      customMessage?: string;
    }) => {
      return await context.createNotification({
        type: "user_approval",
        title: "Account Approved",
        message: userData.customMessage || `Your account has been approved. You can now access all features.`,
        recipientType: "specific_user",
        recipientId: userData.userId,
        priority: "high",
        relatedEntityType: "user",
        relatedEntityId: userData.userId,
      });
    }, [context]),
    
    notifyUserRejection: useCallback(async (userData: {
      firstName: string;
      lastName: string;
      email: string;
      userId: string;
      reason: string;
      customMessage?: string;
    }) => {
      return await context.createNotification({
        type: "user_rejection",
        title: "Account Application Declined",
        message: userData.customMessage || `Your account application has been declined. Reason: ${userData.reason}`,
        recipientType: "specific_user",
        recipientId: userData.userId,
        priority: "high",
        relatedEntityType: "user",
        relatedEntityId: userData.userId,
      });
    }, [context]),
    
    // Order-related notifications
    notifyNewQuotation: context.notifyNewQuotation,
    notifyQuotationUpdate: useCallback(async (quotationData: {
      quotationId: string;
      userName: string;
      status: string;
      updateType: string;
    }) => {
      return await context.createNotification({
        type: "order_notification",
        title: `Quotation ${quotationData.updateType}`,
        message: `Your quotation request has been ${quotationData.status.toLowerCase()}.`,
        recipientType: "all_admins",
        priority: "medium",
        relatedEntityType: "order",
        relatedEntityId: quotationData.quotationId,
      });
    }, [context]),
    
    // Product-related notifications
    notifyProductUpdate: useCallback(async (productData: {
      productId: string;
      productName: string;
      updateType: string;
    }) => {
      return await context.createNotification({
        type: "product_update",
        title: "Product Updated",
        message: `${productData.productName} has been ${productData.updateType}.`,
        recipientType: "all_admins",
        priority: "low",
        relatedEntityType: "product",
        relatedEntityId: productData.productId,
      });
    }, [context]),
    
    // System notifications
    notifySystemAlert: context.notifySystemAlert,
    notifyGSTVerification: useCallback(async (gstData: {
      userId: string;
      userName: string;
      businessName: string;
      gstNumber: string;
      status: "verified" | "failed";
    }) => {
      return await context.createNotification({
        type: "gst_verification",
        title: `GST ${gstData.status === "verified" ? "Verified" : "Verification Failed"}`,
        message: `GST number ${gstData.gstNumber} for ${gstData.businessName} has ${gstData.status === "verified" ? "been verified successfully" : "verification failed"}.`,
        recipientType: "all_admins",
        priority: gstData.status === "failed" ? "high" : "medium",
        relatedEntityType: "user",
        relatedEntityId: gstData.userId,
      });
    }, [context]),
  };
}