"use client";

import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Users,
  Package,
  Bell,
  Settings,
  Key,
  Activity,
  UserCog,
  LogOut,
  ChevronDown,
  ChevronRight,
  FileText,
  ShoppingBag,
  BarChart3,
  CreditCard,
  Truck,
  MessageSquare,
  Cookie,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

interface SidebarProps {
  onNavigate?: () => void;
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  children?: NavItem[];
  permission?: string;
}

export function Sidebar({ onNavigate }: SidebarProps) {
  const { admin, logout, hasPermission } = useAuth();
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  // Early return if auth context is not ready
  if (!hasPermission) {
    return (
      <div className="flex h-full flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <div className="flex h-16 items-center px-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">B</span>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Benzochem</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">Admin</p>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-sm text-gray-500 dark:text-gray-400">Loading...</div>
        </div>
      </div>
    );
  }

  const navItems: NavItem[] = [
    {
      title: "Home",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Orders",
      href: "/dashboard/orders",
      icon: ShoppingBag,
      badge: "12",
    },
    {
      title: "Products",
      icon: Package,
      permission: "products.read",
      children: [
        {
          title: "All products",
          href: "/dashboard/products",
          icon: Package,
          permission: "products.read",
        },
        {
          title: "Collections",
          href: "/dashboard/products/collections",
          icon: Package,
          permission: "products.read",
        },
        {
          title: "Inventory",
          href: "/dashboard/products/inventory",
          icon: Package,
          permission: "products.read",
        },
      ],
    },
    {
      title: "Customers",
      icon: Users,
      permission: "users.read",
      children: [
        {
          title: "All customers",
          href: "/dashboard/users",
          icon: Users,
          permission: "users.read",
        },
        {
          title: "Pending approvals",
          href: "/dashboard/users/pending",
          icon: Users,
          badge: "3",
          permission: "users.approve",
        },
        {
          title: "Segments",
          href: "/dashboard/users/segments",
          icon: Users,
          permission: "users.read",
        },
      ],
    },
    {
      title: "Analytics",
      href: "/dashboard/analytics",
      icon: BarChart3,
    },
    {
      title: "Marketing",
      href: "/dashboard/marketing",
      icon: MessageSquare,
    },
    {
      title: "Discounts",
      href: "/dashboard/discounts",
      icon: CreditCard,
    },
    {
      title: "Quotations",
      href: "/dashboard/quotations",
      icon: FileText,
      permission: "quotations.read",
    },
    {
      title: "Settings",
      icon: Settings,
      permission: "settings.read",
      children: [
        {
          title: "General",
          href: "/dashboard/settings",
          icon: Settings,
          permission: "settings.read",
        },
        {
          title: "Cookie Consent",
          href: "/dashboard/cookie-consent",
          icon: Cookie,
          permission: "cookie-consent:read",
        },
        {
          title: "Notifications",
          href: "/dashboard/settings/notifications",
          icon: Bell,
          permission: "settings.write",
        },
        {
          title: "Shipping",
          href: "/dashboard/settings/shipping",
          icon: Truck,
          permission: "settings.read",
        },
        {
          title: "API Keys",
          href: "/dashboard/api-keys",
          icon: Key,
          permission: "api_keys.read",
        },
        {
          title: "Staff accounts",
          href: "/dashboard/admins",
          icon: UserCog,
          permission: "admins.read",
        },
      ],
    },
  ];

  const filteredNavItems = navItems.filter(item =>
    !item.permission || (hasPermission && hasPermission(item.permission))
  );

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const isActive = item.href ? pathname === item.href : false;
    const hasItemPermission = !item.permission || (hasPermission && hasPermission(item.permission));

    if (!hasItemPermission) return null;

    const filteredChildren = item.children?.filter(child =>
      !child.permission || (hasPermission && hasPermission(child.permission))
    );

    if (hasChildren && (!filteredChildren || filteredChildren.length === 0)) {
      return null;
    }

    const content = (
      <div className={cn("space-y-1", level > 0 && "ml-6")}>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-start gap-3 h-9 text-left font-medium transition-all duration-200 text-gray-700 dark:text-gray-300",
            level > 0 && "h-8 text-sm",
            isActive && "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white font-semibold",
            !isActive && "hover:bg-gray-50 dark:hover:bg-gray-700/50"
          )}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.title);
            } else if (item.href) {
              onNavigate?.();
            }
          }}
          asChild={!hasChildren}
        >
          {hasChildren ? (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <item.icon className="h-4 w-4" />
                <span>{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                    {item.badge}
                  </Badge>
                )}
              </div>
              <div className="transition-transform duration-200">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </div>
          ) : (
            <Link href={item.href!} className="flex items-center gap-3 w-full">
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                  {item.badge}
                </Badge>
              )}
            </Link>
          )}
        </Button>

        {hasChildren && isExpanded && filteredChildren && (
          <div className="space-y-1">
            {filteredChildren.map((child) => (
              <div key={child.title}>
                {renderNavItem(child, level + 1)}
              </div>
            ))}
          </div>
        )}
      </div>
    );

    return content;
  };

  return (
    <div className="flex h-full flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
      {/* Logo */}
      <div className="flex h-16 items-center px-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">B</span>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Benzochem</h2>
            <p className="text-xs text-gray-500 dark:text-gray-400">Admin</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-auto py-4">
        <nav className="px-3 space-y-1">
          {filteredNavItems.map((item) => (
            <div key={item.title}>
              {renderNavItem(item)}
            </div>
          ))}
        </nav>
      </div>

      {/* User info and logout */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {admin?.firstName?.[0]}{admin?.lastName?.[0]}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate text-gray-900 dark:text-white">
              {admin?.firstName} {admin?.lastName}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {admin?.email}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start gap-3 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          onClick={logout}
        >
          <LogOut className="h-4 w-4" />
          Log out
        </Button>
      </div>
    </div>
  );
}
