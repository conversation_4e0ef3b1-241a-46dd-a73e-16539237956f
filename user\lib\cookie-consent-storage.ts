// Server-side cookie consent storage using API endpoints
// Production-ready implementation with real server-side storage

export interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

export interface ConsentData {
  preferences: CookiePreferences
  timestamp: string
}

export class CookieConsentStorage {
  // Check if consent exists via API
  static async hasConsent(): Promise<boolean> {
    try {
      const response = await fetch('/api/cookie-consent', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) return false
      
      const data = await response.json()
      return data.hasConsent === true
    } catch (error) {
      console.error('Error checking consent:', error)
      return false
    }
  }

  // Get stored consent preferences via API
  static async getConsent(): Promise<ConsentData | null> {
    try {
      const response = await fetch('/api/cookie-consent', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) return null
      
      const data = await response.json()
      
      if (!data.hasConsent || !data.preferences) return null
      
      return {
        preferences: data.preferences,
        timestamp: data.timestamp
      }
    } catch (error) {
      console.error('Error getting consent:', error)
      return null
    }
  }

  // Store consent preferences via API
  static async setConsent(preferences: CookiePreferences, consentMethod?: string): Promise<boolean> {
    try {
      const response = await fetch('/api/cookie-consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferences: {
            essential: true, // Always true
            analytics: Boolean(preferences.analytics),
            marketing: Boolean(preferences.marketing),
            functional: Boolean(preferences.functional)
          },
          consentMethod: consentMethod || 'banner_custom',
          timestamp: new Date().toISOString()
        })
      })
      
      if (!response.ok) {
        console.error('🍪 API response not ok:', response.status)
        return false
      }
      
      const data = await response.json()
      
      console.log('🍪 Cookie consent API response:', data)
      
      // Check if database save was successful
      if (data.success && data.dbSaved) {
        console.log('🍪 Cookie consent stored successfully via API')
        return true
      } else {
        console.error('🍪 Database save failed:', data.dbResult)
        return false
      }
    } catch (error) {
      console.error('Error storing consent:', error)
      return false
    }
  }

  // Clear consent via API
  static async clearConsent(): Promise<boolean> {
    try {
      const response = await fetch('/api/cookie-consent', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) return false
      
      const data = await response.json()
      console.log('🍪 Cookie consent cleared via API')
      
      return data.success === true
    } catch (error) {
      console.error('Error clearing consent:', error)
      return false
    }
  }

  // Get consent age in days
  static async getConsentAge(): Promise<number> {
    const consent = await this.getConsent()
    if (!consent) return 0
    
    const consentDate = new Date(consent.timestamp)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - consentDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  // Check if consent needs renewal (after 365 days)
  static async needsRenewal(): Promise<boolean> {
    const age = await this.getConsentAge()
    return age > 365
  }
}

// Cookie consent utility functions
export const cookieConsentUtils = {
  // Check consent status - now directly calls API for authoritative status
  async checkConsentStatus(): Promise<{ hasConsent: boolean; preferences?: CookiePreferences; timestamp?: string; source?: string }> {
    try {
      console.log('🍪 Checking consent status via API...')
      
      const response = await fetch('/api/cookie-consent', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        console.log('🍪 API response not ok:', response.status)
        return { hasConsent: false }
      }
      
      const data = await response.json()
      console.log('🍪 API response data:', data)
      
      // Handle the case where database consent was cleared but local cookie exists
      if (data.source === 'database_cleared') {
        console.log('🍪 Database consent was cleared, local cookie removed')
        return { hasConsent: false, source: 'database_cleared' }
      }
      
      if (data.hasConsent && data.preferences) {
        console.log('🍪 Valid consent found')
        return {
          hasConsent: true,
          preferences: data.preferences,
          timestamp: data.timestamp,
          source: data.source || 'unknown'
        }
      }
      
      console.log('🍪 No valid consent found')
      return { hasConsent: false, source: data.source || 'none' }
    } catch (error) {
      console.error('Error checking consent status:', error)
      return { hasConsent: false }
    }
  },

  // Save consent preferences
  async saveConsent(preferences: CookiePreferences, consentMethod?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const success = await CookieConsentStorage.setConsent(preferences, consentMethod || 'settings_page')
      
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: 'Failed to store consent preferences in database' }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  },

  // Clear consent
  async clearConsent(): Promise<{ success: boolean; error?: string }> {
    try {
      const success = await CookieConsentStorage.clearConsent()
      
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: 'Failed to clear consent' }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
}

// Production info
export const showProductionInfo = () => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    console.info(
      '🍪 Cookie consent is managed via server-side API with secure database storage.'
    )
  }
}