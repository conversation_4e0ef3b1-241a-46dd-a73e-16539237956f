"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { ShoppingBag, Info, Star, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getProductImageUrl, getProductCategory } from "@/lib/image-utils"
import type { Product } from "@/lib/types"

export default function ProductCardEnhanced({ product }: { product: Product }) {
  const [isHovered, setIsHovered] = useState(false)

  // Helper function to get the main image URL
  const getImageUrl = () => {
    return getProductImageUrl(product)
  }

  // Helper function to clean and format description
  const getCleanDescription = () => {
    if (!product.description) return ''
    
    const cleanText = product.description
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\\s+/g, ' ')
      .trim()
    
    return cleanText.length > 100 ? cleanText.substring(0, 100) + '...' : cleanText
  }

  // Helper function to format price
  const getFormattedPrice = () => {
    const price = product.priceRange?.minVariantPrice?.amount
    const currencyCode = product.priceRange?.minVariantPrice?.currencyCode || 'USD'
    
    if (!price) return 'Quote on Request'
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(parseFloat(price))
  }

  return (
    <motion.div
      whileHover={{ y: -8 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className="group overflow-hidden border-0 bg-gradient-to-br from-card via-card to-accent/10 shadow-warm hover:shadow-warm-lg transition-all duration-300">
        <div className="relative overflow-hidden">
          <Image
            src={getImageUrl()}
            alt={product.title}
            width={300}
            height={200}
            className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
          />
          
          {/* Category Badge */}
          <Badge 
            variant="secondary" 
            className="absolute top-3 left-3 bg-primary/90 text-primary-foreground border-0"
          >
            {getProductCategory(product)}
          </Badge>

          {/* Quick View Button */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: isHovered ? 1 : 0, scale: isHovered ? 1 : 0.8 }}
            transition={{ duration: 0.2 }}
            className="absolute top-3 right-3"
          >
            <Button
              size="sm"
              variant="secondary"
              className="bg-background/90 hover:bg-background border-0 shadow-warm"
            >
              <Info className="w-4 h-4" />
            </Button>
          </motion.div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-background/20 via-transparent to-transparent" />
        </div>

        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="flex items-start justify-between">
              <h3 className="font-semibold text-lg text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                {product.title}
              </h3>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <Star className="w-3 h-3 fill-current text-yellow-500" />
                <span>4.8</span>
              </div>
            </div>

            {getCleanDescription() && (
              <p className="text-sm text-muted-foreground line-clamp-3">
                {getCleanDescription()}
              </p>
            )}

            <div className="flex items-center justify-between">
              <div className="text-lg font-bold text-primary">
                {getFormattedPrice()}
              </div>
              <Badge variant="outline" className="text-xs">
                High Purity
              </Badge>
            </div>
          </div>
        </CardContent>

        <CardFooter className="p-6 pt-0 space-y-3">
          <div className="flex space-x-2 w-full">
            <Button 
              className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground shadow-warm hover:shadow-warm-lg transition-all duration-200"
              size="sm"
            >
              <ShoppingBag className="w-4 h-4 mr-2" />
              Request Quote
            </Button>
            <Link href={`/products/${product.handle || product.id}`} className="flex-shrink-0">
              <Button 
                variant="outline" 
                size="sm"
                className="border-border hover:border-primary/40 hover:bg-accent/50 transition-all duration-200"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </Link>
          </div>

          {/* Product Features */}
          <div className="flex flex-wrap gap-1">
            <Badge variant="secondary" className="text-xs bg-accent/50 text-accent-foreground">
              ✓ Lab Grade
            </Badge>
            <Badge variant="secondary" className="text-xs bg-accent/50 text-accent-foreground">
              ✓ Fast Delivery
            </Badge>
            <Badge variant="secondary" className="text-xs bg-accent/50 text-accent-foreground">
              ✓ COA Included
            </Badge>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}