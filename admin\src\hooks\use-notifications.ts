/**
 * React Hook for Real-time Notifications
 * Provides easy access to notification data and actions
 */

import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";
import { useCallback, useMemo } from "react";

export interface UseNotificationsOptions {
  limit?: number;
  offset?: number;
  type?: string;
  priority?: string;
  isRead?: boolean;
  search?: string;
  autoRefresh?: boolean;
}

export function useNotifications(options: UseNotificationsOptions = {}) {
  const { admin } = useAuth();
  
  // Queries
  const notifications = useQuery(api.notifications.getNotifications, {
    search: options.search || undefined,
    type: options.type === "all" ? undefined : options.type as any,
    priority: options.priority === "all" ? undefined : options.priority as any,
    isRead: options.isRead,
    limit: options.limit || 20,
    offset: options.offset || 0,
  });

  const notificationStats = useQuery(api.notifications.getNotificationStats);
  
  const unreadCount = useQuery(api.notifications.getUnreadCount, {
    recipientType: "all_admins",
  });

  // Mutations
  const createNotification = useMutation(api.notifications.createNotification);
  const markAsRead = useMutation(api.notifications.markAsRead);
  const markAllAsRead = useMutation(api.notifications.markAllAsRead);
  const markMultipleAsRead = useMutation(api.notifications.markMultipleAsRead);
  const deleteNotification = useMutation(api.notifications.deleteNotification);
  const deleteExpiredNotifications = useMutation(api.notifications.deleteExpiredNotifications);
  const getOrCreateAdmin = useMutation(api.admins.getOrCreateAdmin);

  // Helper functions
  const handleMarkAsRead = useCallback(async (notificationId: string) => {
    if (!admin) {
      toast.error("Authentication required");
      return false;
    }

    try {
      const adminId = await getOrCreateAdmin({ email: admin.email });
      await markAsRead({
        notificationId: notificationId as any,
        adminId: adminId,
      });
      toast.success("Notification marked as read");
      return true;
    } catch (error) {
      toast.error("Failed to mark notification as read");
      console.error(error);
      return false;
    }
  }, [admin, getOrCreateAdmin, markAsRead]);

  const handleMarkAllAsRead = useCallback(async () => {
    if (!admin) {
      toast.error("Authentication required");
      return false;
    }

    try {
      const adminId = await getOrCreateAdmin({ email: admin.email });
      await markAllAsRead({ adminId: adminId });
      toast.success("All notifications marked as read");
      return true;
    } catch (error) {
      toast.error("Failed to mark all notifications as read");
      console.error(error);
      return false;
    }
  }, [admin, getOrCreateAdmin, markAllAsRead]);

  const handleMarkMultipleAsRead = useCallback(async (notificationIds: string[]) => {
    if (!admin) {
      toast.error("Authentication required");
      return false;
    }

    try {
      const adminId = await getOrCreateAdmin({ email: admin.email });
      await markMultipleAsRead({
        notificationIds: notificationIds as any[],
        readBy: adminId,
      });
      toast.success(`${notificationIds.length} notifications marked as read`);
      return true;
    } catch (error) {
      toast.error("Failed to mark notifications as read");
      console.error(error);
      return false;
    }
  }, [admin, getOrCreateAdmin, markMultipleAsRead]);

  const handleDeleteNotification = useCallback(async (notificationId: string) => {
    try {
      await deleteNotification({
        notificationId: notificationId as any,
      });
      toast.success("Notification deleted successfully");
      return true;
    } catch (error) {
      toast.error("Failed to delete notification");
      console.error(error);
      return false;
    }
  }, [deleteNotification]);

  const handleCreateNotification = useCallback(async (notificationData: {
    type: "user_registration" | "user_approval" | "user_rejection" | "product_update" | "system_alert" | "gst_verification" | "order_notification";
    title: string;
    message: string;
    recipientType: "admin" | "user" | "all_admins" | "specific_user";
    recipientId?: string;
    priority?: "low" | "medium" | "high" | "urgent";
    relatedEntityType?: "user" | "product" | "order";
    relatedEntityId?: string;
    expiresAt?: number;
  }) => {
    if (!admin) {
      toast.error("Authentication required");
      return null;
    }

    try {
      const adminId = await getOrCreateAdmin({ email: admin.email });
      const notificationId = await createNotification({
        ...notificationData,
        recipientId: notificationData.recipientId as any,
        priority: notificationData.priority || "medium",
        createdBy: adminId,
      });
      toast.success("Notification created successfully");
      return notificationId;
    } catch (error) {
      toast.error("Failed to create notification");
      console.error(error);
      return null;
    }
  }, [admin, getOrCreateAdmin, createNotification]);

  const handleCleanupExpired = useCallback(async () => {
    try {
      const deletedIds = await deleteExpiredNotifications({});
      if (deletedIds.length > 0) {
        toast.success(`Cleaned up ${deletedIds.length} expired notifications`);
      }
      return deletedIds;
    } catch (error) {
      toast.error("Failed to cleanup expired notifications");
      console.error(error);
      return [];
    }
  }, [deleteExpiredNotifications]);

  // Computed values
  const hasUnreadNotifications = useMemo(() => {
    return (unreadCount || 0) > 0;
  }, [unreadCount]);

  const recentNotifications = useMemo(() => {
    if (!notifications) return [];
    return notifications.slice(0, 5);
  }, [notifications]);

  const priorityStats = useMemo(() => {
    if (!notificationStats) return null;
    return {
      urgent: notificationStats.byPriority?.urgent || 0,
      high: notificationStats.byPriority?.high || 0,
      medium: notificationStats.byPriority?.medium || 0,
      low: notificationStats.byPriority?.low || 0,
    };
  }, [notificationStats]);

  const typeStats = useMemo(() => {
    if (!notificationStats) return null;
    return {
      user_registration: notificationStats.byType?.user_registration || 0,
      user_approval: notificationStats.byType?.user_approval || 0,
      user_rejection: notificationStats.byType?.user_rejection || 0,
      product_update: notificationStats.byType?.product_update || 0,
      system_alert: notificationStats.byType?.system_alert || 0,
      gst_verification: notificationStats.byType?.gst_verification || 0,
      order_notification: notificationStats.byType?.order_notification || 0,
    };
  }, [notificationStats]);

  // Loading states
  const isLoading = notifications === undefined || notificationStats === undefined;
  const isUnreadCountLoading = unreadCount === undefined;

  return {
    // Data
    notifications,
    notificationStats,
    unreadCount,
    recentNotifications,
    priorityStats,
    typeStats,
    hasUnreadNotifications,

    // Loading states
    isLoading,
    isUnreadCountLoading,

    // Actions
    markAsRead: handleMarkAsRead,
    markAllAsRead: handleMarkAllAsRead,
    markMultipleAsRead: handleMarkMultipleAsRead,
    deleteNotification: handleDeleteNotification,
    createNotification: handleCreateNotification,
    cleanupExpired: handleCleanupExpired,
  };
}

// Utility hook for notification icons and formatting
export function useNotificationHelpers() {
  const getNotificationIcon = useCallback((type: string) => {
    const iconMap: Record<string, string> = {
      user_registration: "User",
      user_approval: "CheckCircle",
      user_rejection: "XCircle",
      product_update: "Package",
      system_alert: "AlertTriangle",
      gst_verification: "CheckCircle",
      order_notification: "Package",
    };
    return iconMap[type] || "Bell";
  }, []);

  const getPriorityColor = useCallback((priority: string) => {
    const colorMap: Record<string, string> = {
      urgent: "text-red-600 bg-red-100",
      high: "text-orange-600 bg-orange-100",
      medium: "text-blue-600 bg-blue-100",
      low: "text-gray-600 bg-gray-100",
    };
    return colorMap[priority] || "text-gray-600 bg-gray-100";
  }, []);

  const getTypeLabel = useCallback((type: string) => {
    const labelMap: Record<string, string> = {
      user_registration: "User Registration",
      user_approval: "User Approval",
      user_rejection: "User Rejection",
      product_update: "Product Update",
      system_alert: "System Alert",
      gst_verification: "GST Verification",
      order_notification: "Order Notification",
    };
    return labelMap[type] || type;
  }, []);

  const formatTimeAgo = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return "Just now";
    if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }, []);

  const formatDate = useCallback((timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }, []);

  return {
    getNotificationIcon,
    getPriorityColor,
    getTypeLabel,
    formatTimeAgo,
    formatDate,
  };
}