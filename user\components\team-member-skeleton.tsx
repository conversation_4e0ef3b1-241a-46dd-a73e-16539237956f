"use client"

import { Card, CardContent } from "@/components/ui/card"

export default function TeamMemberSkeleton() {
  return (
    <Card className="overflow-hidden border-0 shadow-lg bg-card/50 backdrop-blur-sm">
      <div className="relative">
        {/* Profile Image Skeleton */}
        <div className="relative aspect-[4/5] overflow-hidden bg-muted animate-pulse">
          <div className="absolute inset-0 bg-gradient-to-t from-muted via-transparent to-transparent" />
          
          {/* Badge Skeleton */}
          <div className="absolute top-4 right-4">
            <div className="w-20 h-6 bg-muted-foreground/20 rounded-full animate-pulse" />
          </div>

          {/* Bottom Info Skeleton */}
          <div className="absolute bottom-0 left-0 right-0 p-6">
            <div className="w-3/4 h-6 bg-muted-foreground/30 rounded mb-2 animate-pulse" />
            <div className="w-1/2 h-4 bg-muted-foreground/20 rounded mb-2 animate-pulse" />
            <div className="w-1/3 h-3 bg-muted-foreground/20 rounded animate-pulse" />
          </div>
        </div>

        <CardContent className="p-6 space-y-4">
          {/* Experience Skeleton */}
          <div className="flex items-center">
            <div className="w-4 h-4 bg-muted rounded mr-2 animate-pulse" />
            <div className="w-32 h-4 bg-muted rounded animate-pulse" />
          </div>

          {/* Bio Skeleton */}
          <div className="space-y-2">
            <div className="w-full h-4 bg-muted rounded animate-pulse" />
            <div className="w-5/6 h-4 bg-muted rounded animate-pulse" />
            <div className="w-4/6 h-4 bg-muted rounded animate-pulse" />
          </div>

          {/* Specialties Skeleton */}
          <div className="space-y-2">
            <div className="w-20 h-4 bg-muted rounded animate-pulse" />
            <div className="flex flex-wrap gap-1">
              {[1, 2, 3].map((i) => (
                <div key={i} className="w-16 h-6 bg-muted rounded-full animate-pulse" />
              ))}
            </div>
          </div>

          {/* Achievements Skeleton */}
          <div className="space-y-2">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-muted rounded mr-1 animate-pulse" />
              <div className="w-28 h-4 bg-muted rounded animate-pulse" />
            </div>
            <div className="space-y-1">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start">
                  <div className="w-1 h-1 bg-muted rounded-full mt-2 mr-2 animate-pulse" />
                  <div className="w-full h-3 bg-muted rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>

          {/* Buttons Skeleton */}
          <div className="flex gap-2 pt-2">
            <div className="flex-1 h-8 bg-muted rounded animate-pulse" />
            <div className="flex-1 h-8 bg-muted rounded animate-pulse" />
          </div>
        </CardContent>
      </div>
    </Card>
  )
}