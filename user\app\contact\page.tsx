import type { Metadata } from "next"
import PageHeader from "@/components/page-header"
import ContactForm from "@/components/contact-form"
import Faq from "@/components/faq"

export const metadata: Metadata = {
  title: "Contact Us | Benzochem Industries",
  description: "Get in touch with Benzochem Industries for inquiries, quotes, or support.",
}

export default function ContactPage() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <PageHeader
        title="Contact Us"
        description="Get in touch with our team for inquiries, quotes, or technical support. We're here to help you find the perfect chemical solutions."
        badge="Get In Touch"
        imageUrl="https://images.unsplash.com/photo-1423666639041-f56000c27a9a?q=80&w=2074&auto=format&fit=crop"
      />

      {/* Main Contact Section */}
      <section className="py-20 md:py-32 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10" />
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
        
        <div className="container mx-auto px-4 relative z-10">
          <ContactForm />
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 md:py-32 bg-vanilla-gradient dark:bg-dark-gradient relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-transparent to-secondary/10" />
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
        
        <div className="container mx-auto px-4 relative z-10">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              FAQ
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Find answers to common questions about our products, ordering process, and technical support.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Faq
              items={[
                {
                  question: "What is the minimum quotation quantity?",
                  answer:
                    "Our standard minimum quotation quantity varies by product. For most powder products, the minimum is 25kg, and for liquid products, it's 10L. For smaller quantities, please contact our customer service team for custom pricing.",
                },
                {
                  question: "Do you provide certificates of analysis?",
                  answer:
                    "Yes, we provide a Certificate of Analysis (CoA) with every shipment. The CoA includes detailed information about the product's specifications, batch number, and test results.",
                },
                {
                  question: "What are your shipping options?",
                  answer:
                    "We offer various shipping options including standard ground shipping, expedited shipping, and international shipping. The available options depend on your location and the products being quoted.",
                },
                {
                  question: "Can you provide custom formulations?",
                  answer:
                    "Yes, we offer custom formulation services for clients with specific requirements. Our R&D team can work with you to develop products tailored to your exact specifications.",
                },
                {
                  question: "What payment methods do you accept?",
                  answer:
                    "We accept various payment methods including credit cards, wire transfers, and purchase orders for established business accounts. For large quotations, we can also arrange flexible payment terms.",
                },
                {
                  question: "How long does it take to process a quotation?",
                  answer:
                    "Standard quotations are typically processed within 24-48 hours. For complex or custom formulations, it may take 3-5 business days. Rush quotations can be accommodated for urgent requirements.",
                },
              ]}
            />
          </div>
        </div>
      </section>
    </main>
  )
}
