import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const { email, firstName, status } = await request.json()

    if (!email || !firstName || !status) {
      return NextResponse.json(
        { error: 'Email, firstName, and status are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['approved', 'rejected', 'pending']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: approved, rejected, pending' },
        { status: 400 }
      )
    }

    // Send account status email
    const emailResult = await emailService.sendAccountStatusEmail(email, firstName, status)
    
    if (!emailResult.success) {
      console.error('Failed to send account status email:', emailResult.error)
      return NextResponse.json(
        { error: 'Failed to send account status email' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Account status email sent successfully'
    })

  } catch (error) {
    console.error('Send account status email error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}