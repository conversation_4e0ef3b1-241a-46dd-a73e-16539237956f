"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/contexts/auth-context"
import { AlertCircle, CheckCircle2, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import LoadingSpinner from "@/components/loading-spinner"

export default function GstDetails() {
  const { user, verifyGST, isLoading } = useAuth()
  const [gstNumber, setGstNumber] = useState(user?.gstNumber || "")
  const [error, setError] = useState<string | null>(null)
  const [verificationResult, setVerificationResult] = useState<{
    success: boolean
    businessName?: string
    address?: string
  } | null>(null)
  const [isEditing, setIsEditing] = useState(false)

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setVerificationResult(null)

    // Basic GST validation
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
    if (!gstRegex.test(gstNumber)) {
      setError("Invalid GST format. GST should be 15 characters with format: 22AAAAA0000A1Z5")
      return
    }

    const result = await verifyGST(gstNumber)

    if (result.success) {
      setVerificationResult({
        success: true,
        businessName: result.data?.businessName,
        address: result.data?.address,
      })
      setIsEditing(false)
    } else {
      setError(result.error || "GST verification failed")
    }
  }

  return (
    <div className="bg-white border rounded-lg overflow-hidden">
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">GST Details</h2>
      </div>
      <div className="p-6">
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!user?.isGstVerified && !verificationResult?.success && (
          <Alert className="mb-6 bg-yellow-50 text-yellow-800 border-yellow-200">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Your GST number needs to be verified before you can make purchases. This helps us comply with Indian tax
              regulations.
            </AlertDescription>
          </Alert>
        )}

        {(user?.isGstVerified || verificationResult?.success) && (
          <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
            <CheckCircle2 className="h-4 w-4" />
            <AlertDescription>Your GST number has been verified successfully.</AlertDescription>
          </Alert>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-4">GST Information</h3>

              {isEditing ? (
                <form onSubmit={handleVerify} className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="gstNumber" className="text-sm font-medium">
                      GST Number <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="gstNumber"
                      value={gstNumber}
                      onChange={(e) => setGstNumber(e.target.value)}
                      placeholder="e.g. 27AADCB2230M1ZT"
                      required
                    />
                    <p className="text-xs text-neutral-500">
                      Enter your valid 15-digit GST number for verification
                    </p>
                  </div>

                  <div className="flex space-x-2">
                    <Button type="submit" className="bg-teal-600 hover:bg-teal-700" disabled={isLoading}>
                      {isLoading ? (
                        <span className="flex items-center">
                          <LoadingSpinner /> <span className="ml-2">Verifying...</span>
                        </span>
                      ) : (
                        "Verify GST"
                      )}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                      Cancel
                    </Button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">GST Number</label>
                    <div className="p-3 bg-neutral-50 rounded border flex justify-between items-center">
                      <span>{user?.gstNumber || "Not provided"}</span>
                      {user?.isGstVerified ? (
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Trade Name</label>
                    <div className="p-3 bg-neutral-50 rounded border">{user?.businessName || "Not available"}</div>
                  </div>

                  {!user?.isGstVerified && (
                    <Button onClick={() => setIsEditing(true)} className="bg-teal-600 hover:bg-teal-700">
                      Verify GST
                    </Button>
                  )}

                  {user?.isGstVerified && (
                    <Button onClick={() => setIsEditing(true)} variant="outline">
                      Update GST
                    </Button>
                  )}
                </div>
              )}
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">GST Verification Status</h3>
              <div className="border rounded-lg p-6 bg-neutral-50">
                {user?.isGstVerified ? (
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle2 className="h-8 w-8 text-green-600" />
                    </div>
                    <h4 className="text-lg font-medium text-green-800 mb-2">Verified</h4>
                    <p className="text-neutral-600">
                      Your GST number has been verified and your account is approved for purchasing.
                    </p>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Info className="h-8 w-8 text-yellow-600" />
                    </div>
                    <h4 className="text-lg font-medium text-yellow-800 mb-2">Verification Required</h4>
                    <p className="text-neutral-600 mb-4">
                      Your GST number needs to be verified before you can make purchases. Please verify your GST to
                      unlock all features.
                    </p>
                    <Button onClick={() => setIsEditing(true)} className="bg-teal-600 hover:bg-teal-700">
                      Verify Now
                    </Button>
                  </div>
                )}
              </div>

              <div className="mt-6">
                <h4 className="font-medium mb-2">Why is GST verification required?</h4>
                <ul className="space-y-2 text-neutral-600 text-sm">
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-teal-100 text-teal-600 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                      1
                    </span>
                    <span>GST verification is required for B2B transactions in India as per tax regulations.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-teal-100 text-teal-600 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                      2
                    </span>
                    <span>It helps us generate proper tax invoices with your GST details.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-teal-100 text-teal-600 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                      3
                    </span>
                    <span>Verified businesses can claim input tax credit on their purchases.</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}