import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const { email, firstName, quotationId, type } = await request.json()

    if (!email || !firstName || !quotationId || !type) {
      return NextResponse.json(
        { error: 'Email, firstName, quotationId, and type are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate type
    const validTypes = ['created', 'updated', 'approved', 'rejected']
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid type. Must be one of: created, updated, approved, rejected' },
        { status: 400 }
      )
    }

    // Send quotation email
    const emailResult = await emailService.sendQuotationEmail(email, firstName, quotationId, type)
    
    if (!emailResult.success) {
      console.error('Failed to send quotation email:', emailResult.error)
      return NextResponse.json(
        { error: 'Failed to send quotation email' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Quotation email sent successfully'
    })

  } catch (error) {
    console.error('Send quotation email error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}