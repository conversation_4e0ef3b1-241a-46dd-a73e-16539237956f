import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { Search, Filter, Grid, List, Star, Clock, Percent, Gift, Tag, Timer, Zap, Users, Flame } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export const metadata: Metadata = {
  title: "Special Offers | Benzochem Industries",
  description: "Exclusive deals and limited-time offers on premium chemical products. Save big on bulk orders and seasonal promotions.",
}

export default function SpecialOffersPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-red-100 rounded-full text-red-700 text-sm font-medium mb-6">
              <Percent className="w-4 h-4" />
              Limited Time Offers
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
              Special <span className="text-primary">Offers</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Exclusive deals and limited-time promotions on our premium chemical products. 
              Don't miss out on these incredible savings opportunities.
            </p>
          </div>

          {/* Featured Deal */}
          <div className="bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl p-8 text-white mb-12 relative overflow-hidden">
            <div className="absolute top-4 right-4">
              <Badge className="bg-white text-red-600 hover:bg-white">
                Flash Sale
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-3xl font-bold mb-4">
                  🔥 Flash Sale: Up to 40% Off
                </h2>
                <p className="text-red-100 mb-6">
                  Limited time offer on our premium catalyst collection. 
                  Perfect for industrial applications with guaranteed quality.
                </p>
                
                <div className="flex items-center gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold">23</div>
                    <div className="text-xs text-red-200">Hours</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">45</div>
                    <div className="text-xs text-red-200">Minutes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">12</div>
                    <div className="text-xs text-red-200">Seconds</div>
                  </div>
                </div>
                
                <Button className="bg-white text-red-600 hover:bg-red-50">
                  Shop Flash Sale
                </Button>
              </div>
              
              <div className="text-center">
                <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Gift className="w-16 h-16 text-white" />
                </div>
                <div className="text-4xl font-bold mb-2">40% OFF</div>
                <div className="text-red-200">Selected Products</div>
              </div>
            </div>
          </div>

          {/* Offer Categories */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-card border border-border rounded-xl p-6 text-center hover:shadow-lg transition-all duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Percent className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Percentage Deals</h3>
              <p className="text-sm text-muted-foreground">Up to 50% off</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center hover:shadow-lg transition-all duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Bulk Discounts</h3>
              <p className="text-sm text-muted-foreground">Volume pricing</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center hover:shadow-lg transition-all duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Timer className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Flash Sales</h3>
              <p className="text-sm text-muted-foreground">Limited time</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center hover:shadow-lg transition-all duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Bundle Offers</h3>
              <p className="text-sm text-muted-foreground">Buy more, save more</p>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search offers..."
                    className="pl-10 focus:border-primary focus:ring-primary/20"
                  />
                </div>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Offer Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Offers</SelectItem>
                    <SelectItem value="percentage">Percentage Off</SelectItem>
                    <SelectItem value="bulk">Bulk Discounts</SelectItem>
                    <SelectItem value="flash">Flash Sales</SelectItem>
                    <SelectItem value="bundle">Bundle Deals</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Discount Range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Discounts</SelectItem>
                    <SelectItem value="10-20">10% - 20%</SelectItem>
                    <SelectItem value="20-30">20% - 30%</SelectItem>
                    <SelectItem value="30-40">30% - 40%</SelectItem>
                    <SelectItem value="40+">40% and above</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Grid className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <List className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4" />
                  Filters
                </Button>
              </div>
            </div>
          </div>

          {/* Offers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {Array.from({ length: 9 }).map((_, index) => {
              const discounts = [25, 30, 15, 40, 20, 35, 45, 18, 28]
              const discount = discounts[index]
              const isFlash = index < 3
              const timeLeft = Math.floor(Math.random() * 48) + 1
              
              return (
                <div key={index} className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200">
                  <div className="aspect-square bg-muted flex items-center justify-center relative">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                      <Tag className="w-8 h-8 text-primary" />
                    </div>
                    
                    <div className="absolute top-3 left-3">
                      <Badge className={`${isFlash ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'} hover:bg-current`}>
                        {discount}% OFF
                      </Badge>
                    </div>
                    
                    {isFlash && (
                      <div className="absolute top-3 right-3">
                        <Badge className="bg-orange-100 text-orange-700 hover:bg-orange-100">
                          <Zap className="w-3 h-3 mr-1" />
                          Flash
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="flex items-center gap-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <span className="text-xs text-muted-foreground">(4.7)</span>
                    </div>
                    
                    <h3 className="font-semibold text-card-foreground mb-2 line-clamp-2">
                      Special Offer Product {index + 1}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      Premium chemical solution with exclusive discount pricing
                    </p>
                    
                    {isFlash && (
                      <div className="space-y-2 mb-3">
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">Time Left</span>
                          <span className="text-red-600 font-medium">{timeLeft}h remaining</span>
                        </div>
                        <Progress value={(48 - timeLeft) / 48 * 100} className="h-1" />
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-primary">₹{(1000 - discount * 10).toLocaleString()}</span>
                        <span className="text-sm text-muted-foreground line-through">₹1,000</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">CAS: 456-78-{index}</span>
                      <Button size="sm" className="h-8">
                        Claim Offer
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Load More */}
          <div className="text-center mb-16">
            <Button variant="outline" size="lg" className="px-8">
              Load More Offers
            </Button>
          </div>

          {/* Newsletter Signup */}
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Never Miss a Deal
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Subscribe to our newsletter and be the first to know about exclusive offers, 
              flash sales, and special promotions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1"
              />
              <Button className="px-6">
                Get Exclusive Offers
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Join 5,000+ subscribers and save up to 50% on your orders
            </p>
          </div>
        </div>
      </div>
    </main>
  )
}