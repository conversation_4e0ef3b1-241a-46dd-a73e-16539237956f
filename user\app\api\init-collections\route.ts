import { NextRequest, NextResponse } from 'next/server';

// Dynamic API route for real-time data processing
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const adminApiUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    const apiKey = process.env.NEXT_PUBLIC_API_KEY;

    if (!apiKey) {
      return NextResponse.json({
        success: false,
        error: 'API key not configured'
      }, { status: 500 });
    }

    // Call the admin API to initialize collections
    const response = await fetch(`${adminApiUrl}/api/v1/collections/init`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
      },
    });

    const data = await response.json();

    return NextResponse.json(data);
  } catch (error) {
    console.error('Init collections error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to initialize collections',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}