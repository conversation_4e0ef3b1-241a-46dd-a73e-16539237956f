import type { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import LoginForm from "@/components/login-form"
import { ArrowRight, Shield, Zap, Users } from "lucide-react"
import Image from "next/image"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Login | Benzochem Industries",
  description: "Login to your Benzochem Industries account",
}

export default function LoginPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[calc(100vh-8rem)]">
            
            {/* Left side - Branding and Features */}
            <div className="space-y-8 lg:pr-8">
              {/* Hero Section */}
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
                  <Shield className="w-4 h-4" />
                  Secure Business Portal
                </div>
                
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight">
                    Welcome back to
                    <span className="block text-primary">Benzochem Industries</span>
                  </h1>
                  <p className="text-lg text-muted-foreground max-w-lg">
                    Access your personalized dashboard to manage quotations, track orders, and explore our comprehensive chemical product catalog.
                  </p>
                </div>
              </div>

              {/* Features Grid */}
              <div className="grid sm:grid-cols-2 gap-4">
                <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border hover:shadow-md transition-all duration-200">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Zap className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground mb-1">Quick Access</h3>
                    <p className="text-sm text-muted-foreground">Instant access to your quotations and order history</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border hover:shadow-md transition-all duration-200">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Users className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-card-foreground mb-1">Dedicated Support</h3>
                    <p className="text-sm text-muted-foreground">Personal account manager for your business needs</p>
                  </div>
                </div>
              </div>

              {/* Visual Element */}
              <div className="hidden lg:block relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-3xl"></div>
                <div className="relative bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <Shield className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground">Enterprise Security</h4>
                      <p className="text-sm text-muted-foreground">Bank-level encryption & data protection</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      SSL Certificate Active
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Two-Factor Authentication
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      GDPR Compliant
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Login Form */}
            <div className="w-full max-w-md mx-auto lg:mx-0">
              <div className="bg-card border border-border rounded-2xl shadow-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-card-foreground mb-2">Sign In</h2>
                  <p className="text-muted-foreground">Enter your credentials to access your account</p>
                </div>

                <Suspense fallback={
                  <div className="space-y-4">
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                  </div>
                }>
                  <LoginForm />
                </Suspense>

                <div className="mt-8 text-center">
                  <p className="text-muted-foreground text-sm">
                    Don't have an account?{" "}
                    <Link
                      href="/register"
                      className="text-primary hover:text-primary/80 font-medium inline-flex items-center gap-1 transition-colors duration-200"
                    >
                      Create Account <ArrowRight className="w-3 h-3" />
                    </Link>
                  </p>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="mt-6 text-center">
                <p className="text-xs text-muted-foreground mb-3">Trusted by 500+ businesses</p>
                <div className="flex items-center justify-center gap-4 opacity-60">
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                  <div className="w-8 h-8 bg-muted rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}