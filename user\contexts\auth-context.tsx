"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState, useRef } from "react"
import { useRouter } from "next/navigation"
import { apiClient } from "@/lib/api-client"
import { sessionUtils } from "@/lib/session-storage"

export interface UserData {
    id: string
    email: string
    firstName: string
    lastName: string
    name: string
    phone?: string
    businessName?: string
    gstNumber?: string
    isGstVerified?: boolean
    status: "pending" | "approved" | "rejected" | "suspended"
    role: "user" | "admin" | "super_admin"
    // Business information
    legalNameOfBusiness?: string
    tradeName?: string
    dateOfRegistration?: string
    constitutionOfBusiness?: string
    taxpayerType?: string
    principalPlaceOfBusiness?: string
    natureOfCoreBusinessActivity?: string
    gstStatus?: string
    // Marketing consent
    agreedToEmailMarketing?: boolean
    agreedToSmsMarketing?: boolean
    // Timestamps
    createdAt?: number
    updatedAt?: number
    lastLoginAt?: number
}

interface AuthContextType {
    user: UserData | null
    isLoading: boolean
    isRefreshing: boolean
    lastUpdated: Date | null
    login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
    register: (userData: {
        firstName: string
        lastName: string
        email: string
        password: string
        phone?: string
        businessName?: string
        gstNumber?: string
        // Business information
        legalNameOfBusiness?: string
        tradeName?: string
        dateOfRegistration?: string
        constitutionOfBusiness?: string
        taxpayerType?: string
        principalPlaceOfBusiness?: string
        natureOfCoreBusinessActivity?: string
        gstStatus?: string
        // Marketing consent
        agreedToEmailMarketing?: boolean
        agreedToSmsMarketing?: boolean
    }) => Promise<{ success: boolean; error?: string; message?: string; redirectTo?: string }>
    logout: () => Promise<void>
    verifyGST: (gstNumber: string) => Promise<{ success: boolean; error?: string; data?: any }>
    refreshUserData: () => Promise<void>
    enableRealTimeUpdates: (enabled: boolean) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
    const context = useContext(AuthContext)
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider")
    }
    return context
}

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const [user, setUser] = useState<UserData | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [isRefreshing, setIsRefreshing] = useState(false)
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
    const [currentSession, setCurrentSession] = useState<{ userId: string; email: string } | null>(null)
    const router = useRouter()

    // Use a ref to track the current user state to prevent unnecessary re-renders
    const userRef = useRef<UserData | null>(null)

    // Initialize auth state from secure cookies and sync with API
    useEffect(() => {
        const initializeAuth = async () => {
            try {
                const session = sessionUtils.getCurrentUserSession()
                if (session) {
                    setCurrentSession(session)
                    // Fetch user data from API
                    await fetchUserData(session.email)
                } else {
                    setIsLoading(false)
                }
            } catch (e) {
                console.error("Error loading session data", e)
                sessionUtils.clearSession()
                setIsLoading(false)
            }
        }

        initializeAuth()

        // Listen for auth state changes from other tabs/windows
        const handleStorageChange = (e: StorageEvent) => {
            // Note: This won't work with cookies, but we can use a custom event instead
            // For now, we'll rely on the session refresh mechanism
        }

        // Listen for custom auth events
        const handleAuthChange = () => {
            const session = sessionUtils.getCurrentUserSession()
            if (session) {
                setCurrentSession(session)
                fetchUserData(session.email)
            } else {
                setCurrentSession(null)
                setUser(null)
                userRef.current = null
            }
        }

        window.addEventListener('auth-state-change', handleAuthChange)
        return () => window.removeEventListener('auth-state-change', handleAuthChange)
    }, [])

    const fetchUserData = async (email: string) => {
        try {
            console.log('🔍 Fetching user data for:', email)
            const result = await apiClient.getUserByEmail(email)

            if (result.success && result.data) {
                console.log('✅ User data fetched successfully')
                const apiUserData = result.data as any
                const userData: UserData = {
                    id: apiUserData.id,
                    email: apiUserData.email,
                    firstName: apiUserData.firstName,
                    lastName: apiUserData.lastName,
                    name: `${apiUserData.firstName} ${apiUserData.lastName}`.trim(),
                    phone: apiUserData.phone,
                    businessName: apiUserData.businessName,
                    gstNumber: apiUserData.gstNumber,
                    isGstVerified: apiUserData.isGstVerified,
                    status: apiUserData.status,
                    role: apiUserData.role,
                    legalNameOfBusiness: apiUserData.legalNameOfBusiness,
                    tradeName: apiUserData.tradeName,
                    constitutionOfBusiness: apiUserData.constitutionOfBusiness,
                    taxpayerType: apiUserData.taxpayerType,
                    principalPlaceOfBusiness: apiUserData.principalPlaceOfBusiness,
                    gstStatus: apiUserData.gstStatus,
                    agreedToEmailMarketing: apiUserData.agreedToEmailMarketing,
                    agreedToSmsMarketing: apiUserData.agreedToSmsMarketing,
                    createdAt: apiUserData.createdAt,
                    updatedAt: apiUserData.updatedAt,
                    lastLoginAt: apiUserData.lastLoginAt,
                }

                setUser(userData)
                userRef.current = userData
                setLastUpdated(new Date())
            } else {
                console.warn('⚠️ Failed to fetch user data:', result.error)
                
                // If admin API is not available, create a fallback user session
                if (result.error?.includes('Cannot connect to admin API')) {
                    console.log('🔄 Admin API unavailable, using fallback session')
                    const fallbackUser: UserData = {
                        id: `fallback_${Date.now()}`,
                        email: email,
                        firstName: 'User',
                        lastName: '',
                        name: 'User',
                        status: 'approved',
                        role: 'user'
                    }
                    setUser(fallbackUser)
                    userRef.current = fallbackUser
                    setLastUpdated(new Date())
                }
            }
            setIsLoading(false)
        } catch (error) {
            console.error("❌ Error fetching user data:", error)
            setIsLoading(false)
        }
    }

    const login = async (email: string, password: string) => {
        setIsLoading(true)
        try {
            // Input validation
            if (!email || !email.includes('@') || !password) {
                setIsLoading(false)
                return { success: false, error: "Please enter a valid email and password" }
            }

            // First, try to fetch user data to see if user exists
            console.log('🔍 Checking if user exists:', email)
            const userResult = await apiClient.getUserByEmail(email)

            if (userResult.success && userResult.data) {
                // User exists in database
                console.log('✅ User found in database')
                const apiUserData = userResult.data as any
                const userData: UserData = {
                    id: apiUserData.id,
                    email: apiUserData.email,
                    firstName: apiUserData.firstName,
                    lastName: apiUserData.lastName,
                    name: `${apiUserData.firstName} ${apiUserData.lastName}`.trim(),
                    phone: apiUserData.phone,
                    businessName: apiUserData.businessName,
                    gstNumber: apiUserData.gstNumber,
                    isGstVerified: apiUserData.isGstVerified,
                    status: apiUserData.status,
                    role: apiUserData.role,
                    legalNameOfBusiness: apiUserData.legalNameOfBusiness,
                    tradeName: apiUserData.tradeName,
                    constitutionOfBusiness: apiUserData.constitutionOfBusiness,
                    taxpayerType: apiUserData.taxpayerType,
                    principalPlaceOfBusiness: apiUserData.principalPlaceOfBusiness,
                    gstStatus: apiUserData.gstStatus,
                    agreedToEmailMarketing: apiUserData.agreedToEmailMarketing,
                    agreedToSmsMarketing: apiUserData.agreedToSmsMarketing,
                    createdAt: apiUserData.createdAt,
                    updatedAt: apiUserData.updatedAt,
                    lastLoginAt: apiUserData.lastLoginAt,
                }

                // Create session with real user ID using secure cookies
                const session = { userId: userData.id, email }
                sessionUtils.setCurrentUserSession(session)
                setCurrentSession(session)

                // Set user data
                setUser(userData)
                userRef.current = userData
                setLastUpdated(new Date())

                // Update last login timestamp (optional - don't fail if it doesn't work)
                try {
                    await apiClient.updateUserLogin(userData.id)
                    console.log('✅ Login timestamp updated')
                } catch (error) {
                    console.warn('⚠️ Could not update login timestamp:', error)
                    // Don't fail the login for this
                }

            } else if (userResult.error?.includes('Cannot connect to admin API')) {
                // Admin API is not available, create fallback session
                console.log('🔄 Admin API unavailable, creating fallback login session')
                const fallbackUserId = `fallback_${Date.now()}`
                const session = { userId: fallbackUserId, email }
                sessionUtils.setCurrentUserSession(session)
                setCurrentSession(session)

                // Create fallback user data
                const fallbackUser: UserData = {
                    id: fallbackUserId,
                    email: email,
                    firstName: 'User',
                    lastName: '',
                    name: 'User',
                    status: 'approved',
                    role: 'user'
                }
                setUser(fallbackUser)
                userRef.current = fallbackUser
                setLastUpdated(new Date())

            } else {
                // User doesn't exist in database yet
                console.log('⚠️ User not found in database, creating temporary session')
                const tempUserId = `temp_${Date.now()}`
                const session = { userId: tempUserId, email }
                sessionUtils.setCurrentUserSession(session)
                setCurrentSession(session)

                // Don't try to update login for non-existent user
            }

            // Trigger auth state change event
            setTimeout(() => {
                window.dispatchEvent(new CustomEvent('auth-state-change'))
            }, 0)

            setIsLoading(false)
            return { success: true }
        } catch (error: any) {
            console.error("Login error:", error)
            setIsLoading(false)

            return {
                success: false,
                error: error.message || "An unexpected error occurred. Please try again."
            }
        }
    }

    const register = async (userData: {
        firstName: string
        lastName: string
        email: string
        password: string
        phone?: string
        businessName?: string
        gstNumber?: string
        // Business information
        legalNameOfBusiness?: string
        tradeName?: string
        dateOfRegistration?: string
        constitutionOfBusiness?: string
        taxpayerType?: string
        principalPlaceOfBusiness?: string
        natureOfCoreBusinessActivity?: string
        gstStatus?: string
        // Marketing consent
        agreedToEmailMarketing?: boolean
        agreedToSmsMarketing?: boolean
    }) => {
        setIsLoading(true)
        console.log("Registration data received:", {
            email: userData.email,
            agreedToEmailMarketing: userData.agreedToEmailMarketing,
            agreedToSmsMarketing: userData.agreedToSmsMarketing
        })

        try {
            // Generate user ID
            const userId = `user_${Date.now()}`

            // Create user via API
            const result = await apiClient.createUser({
                userId,
                email: userData.email,
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                businessName: userData.businessName,
                gstNumber: userData.gstNumber,
                legalNameOfBusiness: userData.legalNameOfBusiness,
                tradeName: userData.tradeName,
                dateOfRegistration: userData.dateOfRegistration,
                constitutionOfBusiness: userData.constitutionOfBusiness,
                taxpayerType: userData.taxpayerType,
                principalPlaceOfBusiness: userData.principalPlaceOfBusiness,
                natureOfCoreBusinessActivity: userData.natureOfCoreBusinessActivity,
                gstStatus: userData.gstStatus,
                agreedToEmailMarketing: userData.agreedToEmailMarketing,
                agreedToSmsMarketing: userData.agreedToSmsMarketing,
            })

            if (result.success) {
                // Create session using secure cookies
                const session = { userId, email: userData.email }
                sessionUtils.setCurrentUserSession(session)
                setCurrentSession(session)

                // Fetch the created user data
                await fetchUserData(userData.email)

                setIsLoading(false)
                return {
                    success: true,
                    message: "Account created successfully! Your account is pending approval from our admin team.",
                    redirectTo: "/waiting-list"
                }
            } else if (result.error?.includes('Cannot connect to admin API')) {
                // Admin API is not available, create fallback registration
                console.log('🔄 Admin API unavailable, creating fallback registration')
                
                // Create session using secure cookies
                const session = { userId, email: userData.email }
                sessionUtils.setCurrentUserSession(session)
                setCurrentSession(session)

                // Create fallback user data
                const fallbackUser: UserData = {
                    id: userId,
                    email: userData.email,
                    firstName: userData.firstName,
                    lastName: userData.lastName,
                    name: `${userData.firstName} ${userData.lastName}`.trim(),
                    phone: userData.phone,
                    businessName: userData.businessName,
                    gstNumber: userData.gstNumber,
                    status: 'pending',
                    role: 'user',
                    legalNameOfBusiness: userData.legalNameOfBusiness,
                    tradeName: userData.tradeName,
                    constitutionOfBusiness: userData.constitutionOfBusiness,
                    taxpayerType: userData.taxpayerType,
                    principalPlaceOfBusiness: userData.principalPlaceOfBusiness,
                    gstStatus: userData.gstStatus,
                    agreedToEmailMarketing: userData.agreedToEmailMarketing,
                    agreedToSmsMarketing: userData.agreedToSmsMarketing,
                }
                setUser(fallbackUser)
                userRef.current = fallbackUser
                setLastUpdated(new Date())

                setIsLoading(false)
                return {
                    success: true,
                    message: "Account created locally! Note: Admin API is unavailable, so your registration may not be synced until the admin system is online.",
                    redirectTo: "/waiting-list"
                }
            } else {
                setIsLoading(false)
                return {
                    success: false,
                    error: result.error || "Failed to create account. Please try again."
                }
            }
        } catch (error) {
            console.error("Registration error:", error)
            setIsLoading(false)
            return {
                success: false,
                error: "An unexpected error occurred. Please try again."
            }
        }
    }

    const logout = async () => {
        try {
            // Clear session data using secure cookies
            sessionUtils.clearSession()
            setCurrentSession(null)

            // Update state
            setUser(null)
            userRef.current = null

            // Trigger auth state change event
            window.dispatchEvent(new CustomEvent('auth-state-change'))

            // Redirect to home page
            router.push('/')
        } catch (error) {
            console.error("Logout error:", error)
        }
    }

    const verifyGST = async (gstNumber: string) => {
        try {
            const result = await apiClient.verifyGST(gstNumber)
            return result
        } catch (error) {
            console.error("GST verification error:", error)
            return {
                success: false,
                error: "Failed to verify GST number. Please try again."
            }
        }
    }

    // Manual refresh function
    const refreshUserData = async () => {
        try {
            setIsRefreshing(true)
            if (currentSession) {
                await fetchUserData(currentSession.email)
                // Refresh the session timestamp
                sessionUtils.refreshSession()
            }
            setLastUpdated(new Date())
        } catch (error) {
            console.error('Error refreshing user data:', error)
        } finally {
            setIsRefreshing(false)
        }
    }

    // Enable/disable real-time updates (handled by API polling)
    const enableRealTimeUpdates = (enabled: boolean) => {
        // Could implement polling or websockets here if needed
    }

    const value = {
        user,
        isLoading,
        isRefreshing,
        lastUpdated,
        login,
        register,
        logout,
        verifyGST,
        refreshUserData,
        enableRealTimeUpdates,
    }

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}