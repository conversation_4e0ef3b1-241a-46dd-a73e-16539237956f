"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import AnimatedBackground from "@/components/animated-background";

// Enhanced skeleton with shimmer effect
function ShimmerSkeleton({ className = "", children, ...props }: React.ComponentProps<"div"> & { children?: React.ReactNode }) {
  return (
    <div
      className={`relative overflow-hidden bg-muted/50 rounded-md ${className}`}
      {...props}
    >
      <div 
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"
        style={{
          animation: 'shimmer 2s infinite',
          transform: 'translateX(-100%)',
        }}
      />
      {children}
      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
}

// Floating skeleton for interactive elements
function FloatingSkeleton({ className = "", delay = 0, ...props }: React.ComponentProps<"div"> & { delay?: number }) {
  return (
    <motion.div
      className={`bg-muted/60 rounded-md ${className}`}
      animate={{
        y: [0, -2, 0],
        opacity: [0.6, 0.8, 0.6],
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        delay,
        ease: "easeInOut",
      }}
      {...props}
    />
  );
}

// Breathing skeleton for content areas
function BreathingSkeleton({ className = "", ...props }: React.ComponentProps<"div">) {
  return (
    <motion.div
      className={`bg-muted/50 rounded-md ${className}`}
      animate={{
        scale: [1, 1.02, 1],
        opacity: [0.5, 0.7, 0.5],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      {...props}
    />
  );
}

export default function EnhancedProductDetailSkeleton() {
  return (
    <div className="relative">
      {/* Background Elements */}
      <AnimatedBackground />
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10" />
      
      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Product Hero Section Skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <FloatingSkeleton className="h-6 w-24 rounded-full" delay={0} />
            <FloatingSkeleton className="h-6 w-20 rounded-full" delay={0.2} />
          </div>
          
          <ShimmerSkeleton className="h-16 md:h-20 lg:h-24 w-3/4 mx-auto mb-6 rounded-lg" />
          
          <div className="max-w-3xl mx-auto mb-8 space-y-3">
            <BreathingSkeleton className="h-6 w-full rounded" />
            <BreathingSkeleton className="h-6 w-5/6 mx-auto rounded" />
            <BreathingSkeleton className="h-6 w-4/5 mx-auto rounded" />
          </div>

          <FloatingSkeleton className="h-10 w-64 mx-auto rounded-full" delay={0.5} />
        </motion.div>

        {/* Product Stats Skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {[...Array(4)].map((_, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <FloatingSkeleton className="w-12 h-12 rounded-full mx-auto mb-4" delay={index * 0.1} />
                <ShimmerSkeleton className="h-8 w-16 mx-auto mb-1 rounded" />
                <BreathingSkeleton className="h-4 w-20 mx-auto mb-1 rounded" />
                <BreathingSkeleton className="h-3 w-24 mx-auto rounded" />
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Main Product Content Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start mb-20">
          {/* Image Gallery Side Skeleton */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-8"
          >
            {/* Main Image Skeleton */}
            <Card className="overflow-hidden border-0 shadow-xl">
              <div className="relative aspect-square bg-gradient-to-br from-muted/30 to-muted/10">
                <ShimmerSkeleton className="absolute inset-0" />
                
                {/* Quality Badge Skeleton */}
                <div className="absolute top-4 left-4 z-10">
                  <FloatingSkeleton className="h-6 w-32 rounded-full" delay={0.3} />
                </div>

                {/* Zoom Button Skeleton */}
                <FloatingSkeleton className="absolute top-4 right-4 w-10 h-10 rounded-full" delay={0.4} />

                {/* Navigation Arrows Skeleton */}
                <FloatingSkeleton className="absolute left-3 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full" delay={0.5} />
                <FloatingSkeleton className="absolute right-3 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full" delay={0.6} />

                {/* Image Indicator Dots Skeleton */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10">
                  <div className="flex space-x-2 bg-background/90 backdrop-blur-sm rounded-full px-3 py-2">
                    {[...Array(4)].map((_, index) => (
                      <FloatingSkeleton key={index} className="w-2 h-2 rounded-full" delay={0.7 + index * 0.1} />
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            {/* Thumbnail Grid Skeleton */}
            <div className="grid grid-cols-4 gap-3">
              {[...Array(4)].map((_, index) => (
                <ShimmerSkeleton key={index} className="aspect-square rounded-xl" />
              ))}
            </div>

            {/* Product Features Skeleton */}
            <Card className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-6">
                  <FloatingSkeleton className="h-5 w-5 rounded" delay={0.2} />
                  <ShimmerSkeleton className="h-6 w-32 rounded" />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[...Array(4)].map((_, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                      className="flex items-start space-x-3 p-4 rounded-xl bg-muted/30"
                    >
                      <FloatingSkeleton className="w-8 h-8 rounded-xl flex-shrink-0" delay={index * 0.1} />
                      <div className="flex-1 space-y-2">
                        <BreathingSkeleton className="h-4 w-20 rounded" />
                        <BreathingSkeleton className="h-3 w-full rounded" />
                        <BreathingSkeleton className="h-3 w-3/4 rounded" />
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Product Info Side Skeleton */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-8"
          >
            {/* Specifications Skeleton */}
            <Card className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-6">
                  <FloatingSkeleton className="h-5 w-5 rounded" delay={0.1} />
                  <ShimmerSkeleton className="h-6 w-48 rounded" />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[...Array(4)].map((_, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-xl bg-muted/30">
                      <div className="flex items-center space-x-3">
                        <FloatingSkeleton className="w-8 h-8 rounded-xl" delay={index * 0.1} />
                        <div className="space-y-1">
                          <BreathingSkeleton className="h-3 w-16 rounded" />
                          <BreathingSkeleton className="h-4 w-20 rounded" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Purchase Section Skeleton */}
            <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
              <CardContent className="p-6 space-y-6">
                <div className="flex items-center gap-2">
                  <FloatingSkeleton className="h-5 w-5 rounded" delay={0.1} />
                  <ShimmerSkeleton className="h-6 w-28 rounded" />
                </div>
                
                {/* Variant Selection Skeleton */}
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <FloatingSkeleton className="h-4 w-4 rounded" delay={0.2} />
                    <BreathingSkeleton className="h-4 w-24 rounded" />
                  </div>
                  <ShimmerSkeleton className="w-full h-12 rounded-xl" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <FloatingSkeleton className="h-4 w-4 rounded" delay={0.3} />
                      <BreathingSkeleton className="h-4 w-16 rounded" />
                      <BreathingSkeleton className="h-3 w-12 rounded" />
                    </div>
                    <div className="flex items-center border rounded-xl bg-background/50 backdrop-blur-sm">
                      <FloatingSkeleton className="h-12 w-12 rounded-l-xl" delay={0.4} />
                      <ShimmerSkeleton className="flex-1 h-12" />
                      <FloatingSkeleton className="h-12 w-12 rounded-r-xl" delay={0.5} />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3">
                      <FloatingSkeleton className="h-4 w-4 rounded" delay={0.6} />
                      <BreathingSkeleton className="h-4 w-20 rounded" />
                    </div>
                    <ShimmerSkeleton className="w-full h-12 rounded-xl" />
                  </div>
                </div>

                <ShimmerSkeleton className="w-full h-14 rounded-xl" />
                
                <div className="grid grid-cols-2 gap-3">
                  <FloatingSkeleton className="h-12 rounded-xl" delay={0.7} />
                  <FloatingSkeleton className="h-12 rounded-xl" delay={0.8} />
                </div>
              </CardContent>
            </Card>

            {/* Trust Indicators Skeleton */}
            <div className="grid grid-cols-3 gap-4">
              {[...Array(3)].map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                  className="flex flex-col items-center text-center p-4 rounded-xl bg-muted/30"
                >
                  <FloatingSkeleton className="h-6 w-6 mb-2 rounded" delay={index * 0.1} />
                  <BreathingSkeleton className="h-3 w-16 rounded" />
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Download Section Skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 text-center space-y-6">
              <ShimmerSkeleton className="h-8 w-64 mx-auto rounded" />
              <div className="max-w-2xl mx-auto space-y-3">
                <BreathingSkeleton className="h-5 w-full rounded" />
                <BreathingSkeleton className="h-5 w-3/4 mx-auto rounded" />
              </div>
              <FloatingSkeleton className="h-12 w-48 mx-auto rounded-xl" delay={0.5} />
            </CardContent>
          </Card>
        </motion.div>

        {/* Additional Sections Skeleton */}
        <div className="mt-20 space-y-16">
          {/* Product Specifications Section Skeleton */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <ShimmerSkeleton className="h-8 w-48 mb-8 rounded" />
            <Card className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="space-y-6">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="grid grid-cols-3 gap-4 py-4 border-b border-border/20 last:border-b-0">
                      <BreathingSkeleton className="h-4 w-full rounded" />
                      <BreathingSkeleton className="h-4 w-full rounded" />
                      <BreathingSkeleton className="h-4 w-3/4 rounded" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Related Products Section Skeleton */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <ShimmerSkeleton className="h-8 w-40 mb-8 rounded" />
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, index) => (
                <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm">
                  <div className="relative aspect-square overflow-hidden">
                    <ShimmerSkeleton className="h-full w-full" />
                    <FloatingSkeleton className="absolute top-2 right-2 h-5 w-20 rounded-full" delay={index * 0.1} />
                  </div>
                  <CardContent className="p-4 space-y-3">
                    <BreathingSkeleton className="h-6 w-3/4 rounded" />
                    <BreathingSkeleton className="h-4 w-full rounded" />
                    <BreathingSkeleton className="h-4 w-2/3 rounded" />
                    <div className="flex items-center gap-2 pt-2">
                      <FloatingSkeleton className="h-5 w-16 rounded" delay={index * 0.1} />
                      <FloatingSkeleton className="h-5 w-12 rounded" delay={index * 0.1 + 0.1} />
                    </div>
                    <FloatingSkeleton className="h-9 w-full rounded-xl" delay={index * 0.1 + 0.2} />
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}