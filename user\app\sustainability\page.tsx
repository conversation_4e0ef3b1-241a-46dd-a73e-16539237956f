import type { Metada<PERSON> } from "next"
import { Leaf, Recycle, Droplets, Wind, Sun, Target, Award, Users, TrendingUp, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Sustainability | Benzochem Industries",
  description: "Our commitment to environmental responsibility and sustainable chemical manufacturing. Learn about our green initiatives and eco-friendly practices.",
}

export default function SustainabilityPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 rounded-full text-green-700 text-sm font-medium mb-6">
              <Leaf className="w-4 h-4" />
              Environmental Commitment
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
              Building a <span className="text-green-600">Sustainable</span> Future
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8">
              At Benzochem Industries, we're committed to environmental stewardship and sustainable practices. 
              Our comprehensive approach to sustainability ensures we protect our planet while delivering 
              exceptional chemical solutions.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-green-600 hover:bg-green-700">
                View Our Impact Report
              </Button>
              <Button size="lg" variant="outline">
                Sustainability Initiatives
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-16">
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Leaf className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-green-600 mb-2">45%</h3>
              <p className="text-sm text-muted-foreground">Carbon Footprint Reduction</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Droplets className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-2xl font-bold text-blue-600 mb-2">60%</h3>
              <p className="text-sm text-muted-foreground">Water Usage Reduction</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Recycle className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-2xl font-bold text-orange-600 mb-2">85%</h3>
              <p className="text-sm text-muted-foreground">Waste Recycling Rate</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sun className="w-8 h-8 text-yellow-600" />
              </div>
              <h3 className="text-2xl font-bold text-yellow-600 mb-2">100%</h3>
              <p className="text-sm text-muted-foreground">Renewable Energy</p>
            </div>
          </div>

          {/* Our Commitments */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-4">Our Environmental Commitments</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                We've set ambitious goals to minimize our environmental impact and contribute to a sustainable future.
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="bg-card border border-border rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Target className="w-6 h-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-card-foreground mb-2">Carbon Neutral by 2030</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Achieving net-zero carbon emissions across all our operations through renewable energy and carbon offset programs.
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="text-green-600 font-medium">65%</span>
                        </div>
                        <Progress value={65} className="h-2" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-card border border-border rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Droplets className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-card-foreground mb-2">Water Conservation</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Implementing advanced water recycling systems and reducing consumption by 70% by 2025.
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="text-blue-600 font-medium">78%</span>
                        </div>
                        <Progress value={78} className="h-2" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-card border border-border rounded-xl p-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Recycle className="w-6 h-6 text-orange-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-card-foreground mb-2">Zero Waste to Landfill</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Achieving 100% waste diversion from landfills through recycling and circular economy practices.
                      </p>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Progress</span>
                          <span className="text-orange-600 font-medium">92%</span>
                        </div>
                        <Progress value={92} className="h-2" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-2xl p-8">
                <h3 className="text-2xl font-bold text-foreground mb-6">Sustainability Timeline</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">2023 - Renewable Energy Transition</h4>
                      <p className="text-sm text-muted-foreground">Completed transition to 100% renewable energy sources</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">2024 - Water Recycling Systems</h4>
                      <p className="text-sm text-muted-foreground">Implemented advanced water treatment and recycling facilities</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">2025 - Circular Economy</h4>
                      <p className="text-sm text-muted-foreground">Launch comprehensive circular economy initiatives</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">2030 - Carbon Neutral</h4>
                      <p className="text-sm text-muted-foreground">Achieve net-zero carbon emissions across all operations</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Green Initiatives */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-4">Green Initiatives</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Our comprehensive approach to sustainability includes innovative technologies and practices 
                that minimize environmental impact.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <Wind className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Clean Air Technology</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Advanced air filtration and emission control systems that exceed industry standards.
                </p>
                <Badge variant="secondary" className="text-xs">
                  99.8% Emission Reduction
                </Badge>
              </div>
              
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Droplets className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Water Stewardship</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Closed-loop water systems and advanced treatment technologies for minimal water impact.
                </p>
                <Badge variant="secondary" className="text-xs">
                  60% Water Savings
                </Badge>
              </div>
              
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                  <Recycle className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Circular Economy</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Waste-to-resource programs that transform byproducts into valuable materials.
                </p>
                <Badge variant="secondary" className="text-xs">
                  85% Waste Diverted
                </Badge>
              </div>
              
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                  <Sun className="w-6 h-6 text-yellow-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Renewable Energy</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Solar and wind power installations providing 100% renewable energy for operations.
                </p>
                <Badge variant="secondary" className="text-xs">
                  100% Renewable
                </Badge>
              </div>
              
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <Award className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Green Chemistry</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Developing safer, more sustainable chemical processes and products.
                </p>
                <Badge variant="secondary" className="text-xs">
                  50+ Green Products
                </Badge>
              </div>
              
              <div className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold text-card-foreground mb-3">Community Impact</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Environmental education and community sustainability programs.
                </p>
                <Badge variant="secondary" className="text-xs">
                  10,000+ People Reached
                </Badge>
              </div>
            </div>
          </div>

          {/* Certifications */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-foreground mb-4">Certifications & Recognition</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Our commitment to sustainability is recognized by leading environmental organizations and industry bodies.
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                "ISO 14001",
                "LEED Platinum",
                "Carbon Trust",
                "Green Chemistry Award",
                "EPA Green Partner",
                "Sustainable Business Award",
                "Zero Waste Certified",
                "Renewable Energy Leader"
              ].map((cert, index) => (
                <div key={index} className="bg-card border border-border rounded-xl p-4 text-center hover:shadow-md transition-all duration-200">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-card-foreground text-sm">{cert}</h3>
                </div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">Join Our Sustainability Journey</h3>
            <p className="text-green-100 mb-6 max-w-2xl mx-auto">
              Partner with us to create a more sustainable future. Together, we can make a positive impact 
              on our planet while achieving your business goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-green-600 hover:bg-green-50">
                Download Sustainability Report
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white/10">
                Contact Our Sustainability Team
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}