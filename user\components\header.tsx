"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { AnimatePresence, motion } from "framer-motion"
import { Search, ShoppingBag, Menu, X, User, ChevronDown, Sun, Moon } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Badge } from "@/components/ui/badge"
import { useTheme } from "next-themes"
import { usePathname } from "next/navigation"
import SearchWithSuggestions from "./search-with-suggestions"

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const { user, logout } = useAuth()
  const { currentQuotation } = useSecureQuotation()
  const { theme, setTheme } = useTheme()
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const quotationItemCount = currentQuotation?.items?.length || 0

  // Handle search from header
  const handleSearch = (query: string) => {
    if (query.trim()) {
      setIsSearchOpen(false)
      router.push(`/search?q=${encodeURIComponent(query.trim())}`)
    }
  }

  // Hide header on waiting-list page
  if (pathname === '/waiting-list' || pathname?.startsWith('/waiting-list')) {
    return null
  }

  return (
    <>
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, type: "spring", stiffness: 100 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-background/80 backdrop-blur-lg border-b border-border shadow-warm"
            : "bg-transparent"
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16 md:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="relative">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-primary to-teal-600 rounded-lg flex items-center justify-center transition-transform group-hover:scale-105">
                  <span className="text-white font-bold text-sm md:text-base">B</span>
                </div>
              </div>
              <span className="font-bold text-lg md:text-xl text-foreground hidden sm:block">
                Benzochem
              </span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center space-x-1 text-foreground hover:text-primary transition-colors">
                  <span>Products</span>
                  <ChevronDown className="w-4 h-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="vanilla-card">
                  <DropdownMenuItem asChild>
                    <Link href="/categories/powder" className="w-full">Powder Products</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/categories/liquid" className="w-full">Liquid Products</Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Link 
                href="/about" 
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                About
              </Link>
              <Link 
                href="/contact" 
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Contact
              </Link>
            </nav>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="hover:bg-accent/50 transition-colors"
              >
                <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>

              {/* Search */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSearchOpen(true)}
                className="hover:bg-accent/50 transition-colors hidden sm:flex"
              >
                <Search className="w-4 h-4" />
              </Button>

              {/* Quotation */}
              <Link href="/quotation">
                <Button variant="ghost" size="sm" className="relative hover:bg-accent/50 transition-colors">
                  <ShoppingBag className="w-4 h-4" />
                  {quotationItemCount > 0 && (
                    <Badge 
                      variant="default" 
                      className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary hover:bg-primary"
                    >
                      {quotationItemCount}
                    </Badge>
                  )}
                </Button>
              </Link>

              {/* User Menu */}
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="hover:bg-accent/50 transition-colors">
                      <User className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="vanilla-card">
                    <DropdownMenuItem asChild>
                      <Link href="/account" className="w-full">Account</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/quotation" className="w-full">Current Quotation</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={logout} className="text-destructive">
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="hidden sm:flex items-center space-x-2">
                  <Link href="/login">
                    <Button variant="ghost" size="sm" className="hover:bg-accent/50 transition-colors">
                      Login
                    </Button>
                  </Link>
                  <Link href="/register">
                    <Button size="sm" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                      Register
                    </Button>
                  </Link>
                </div>
              )}

              {/* Mobile Menu */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden hover:bg-accent/50 transition-colors"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-background/95 backdrop-blur-lg border-t border-border"
            >
              <div className="container mx-auto px-4 py-4">
                <nav className="flex flex-col space-y-4">
                  <Link href="/categories/powder" className="text-foreground hover:text-primary transition-colors">
                    Powder Products
                  </Link>
                  <Link href="/categories/liquid" className="text-foreground hover:text-primary transition-colors">
                    Liquid Products
                  </Link>
                  <Link href="/about" className="text-foreground hover:text-primary transition-colors">
                    About
                  </Link>
                  <Link href="/contact" className="text-foreground hover:text-primary transition-colors">
                    Contact
                  </Link>
                  
                  {!user && (
                    <div className="flex flex-col space-y-2 pt-4 border-t border-border">
                      <Link href="/login">
                        <Button variant="ghost" className="w-full justify-start">
                          Login
                        </Button>
                      </Link>
                      <Link href="/register">
                        <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                          Register
                        </Button>
                      </Link>
                    </div>
                  )}
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>

      {/* Search Modal */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
            onClick={() => setIsSearchOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              className="container mx-auto px-4 pt-20"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="max-w-2xl mx-auto">
                <div className="vanilla-card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold">Search Products</h2>
                    <Button variant="ghost" size="sm" onClick={() => setIsSearchOpen(false)}>
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  <SearchWithSuggestions
                    placeholder="Search by product name, CAS number, molecular formula..."
                    showRecentSearches={true}
                    autoFocus={true}
                    onSearch={handleSearch}
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}