# Cookie Consent Optimization

## Problem
The cookie consent system was creating new rows in the Convex database every time a user updated their cookie preferences from the CookieSettingsPage, leading to database clutter and multiple entries for the same user.

## Solution
Implemented an intelligent update system that:

1. **Updates existing records instead of creating new ones** when preferences are changed within a 5-minute window
2. **Prevents unnecessary API calls** when preferences haven't actually changed
3. **Maintains audit trail** for older consent changes (beyond the 5-minute window)
4. **Provides cleanup utilities** to remove old inactive consent records

## Changes Made

### 1. Convex Database Function (`admin/convex/cookieConsents.ts`)
- Modified `saveConsent` mutation to check if existing consent was created recently (within 5 minutes)
- If recent and preferences changed: updates existing record instead of creating new one
- If no changes: returns existing consent without any database operation
- If older than 5 minutes: creates new record for audit trail (existing behavior)

### 2. Frontend Optimization (`user/components/cookie-preferences-manager.tsx`)
- Added check to prevent saving when preferences haven't changed
- Passes `settings_page` as consent method for better tracking

### 3. Cookie Storage Utility (`user/lib/cookie-consent-storage.ts`)
- Ensures consent method is always provided (defaults to 'settings_page')

### 4. Cleanup Utility (`admin/convex/cookieConsents.ts`)
- Added `cleanupOldConsents` mutation to remove old inactive consent records
- Keeps only the latest 3 records per user (configurable)
- Never deletes active consent records
- Supports dry-run mode for testing

### 5. Admin API Endpoint (`admin/src/app/api/admin/cookie-consent/cleanup/route.ts`)
- Provides REST API to run cleanup operations
- Supports both dry-run preview and actual cleanup
- Configurable number of records to keep per user

## Benefits

1. **Reduced Database Clutter**: No more multiple rows for quick preference changes
2. **Better Performance**: Fewer database operations for unchanged preferences
3. **Maintained Audit Trail**: Still tracks consent changes over time
4. **Easy Cleanup**: Admin tools to manage old records
5. **Real-time Updates**: Users see immediate updates without creating unnecessary records

## Usage

### For Users
- Cookie preferences are now updated in-place when changed quickly
- No visible changes to user experience
- Faster response times for preference updates

### For Administrators
- Use the cleanup API to manage old consent records:
  ```bash
  # Preview what would be deleted (dry run)
  GET /api/admin/cookie-consent/cleanup?keepPerUser=3
  
  # Actually delete old records
  POST /api/admin/cookie-consent/cleanup
  {
    "dryRun": false,
    "keepPerUser": 3
  }
  ```

## Configuration

### Update Window
The 5-minute update window can be adjusted in `cookieConsents.ts`:
```typescript
const updateWindow = 5 * 60 * 1000; // 5 minutes in milliseconds
```

### Records to Keep
The cleanup function keeps the latest 3 records per user by default. This can be changed:
```typescript
const keepPerUser = args.keepPerUser || 3; // Keep latest 3 records per user
```

## Technical Details

### Database Schema
The consent records maintain the same schema but now include:
- `updatedAt`: Timestamp of last update
- `changeReason`: Reason for consent change ("updated", "expired", "user_revoked")
- `previousConsentId`: Reference to previous consent record

### Audit Trail
- Recent changes (< 5 minutes): Update existing record
- Older changes (> 5 minutes): Create new record, mark old as inactive
- All changes are logged with timestamps and reasons

### Security
- No sensitive information stored in localStorage
- All consent data managed server-side
- Secure cookie storage with proper expiration
- GDPR compliant audit trail maintained