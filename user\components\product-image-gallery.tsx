"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight, Maximize2, X, <PERSON>rk<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Trigger, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { getProductImageUrls } from "@/lib/image-utils"
import type { Product } from "@/lib/types"

interface ProductImageGalleryProps {
  product: Product
}

export default function ProductImageGallery({ product }: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [imageError, setImageError] = useState(false)

  // Get all available images from the product using utility function
  const images = getProductImageUrls(product)

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const setImage = (index: number) => {
    setCurrentImageIndex(index)
  }

  const getImageUrl = (url: string) => {
    if (imageError) return "/placeholder-chemical.jpg"
    return url || "/placeholder-chemical.jpg"
  }

  return (
    <div className="space-y-4">
      {/* Main Image Display */}
      <div className="relative aspect-square bg-gradient-to-br from-muted/30 to-muted/10 overflow-hidden group">
        {/* Quality Badge */}
        <div className="absolute top-4 left-4 z-10">
          <Badge className="bg-background/95 text-foreground backdrop-blur-sm shadow-sm border border-border/50">
            <Sparkles className="h-3 w-3 mr-1" />
            Premium Quality
          </Badge>
        </div>

        {/* Zoom Button */}
        <Dialog open={isZoomed} onOpenChange={setIsZoomed}>
          <DialogTrigger asChild>
            <Button
              variant="secondary"
              size="icon"
              className="absolute top-4 right-4 bg-background/95 backdrop-blur-sm z-10 rounded-full shadow-sm hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl w-full h-[90vh] p-0 bg-background/95 backdrop-blur-xl border-border">
            <DialogHeader className="absolute top-4 left-4 z-50">
              <DialogTitle className="sr-only">Product Image - {product.title}</DialogTitle>
            </DialogHeader>
            
            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 z-50 text-foreground hover:bg-muted rounded-full"
              onClick={() => setIsZoomed(false)}
            >
              <X className="h-5 w-5" />
            </Button>

            {/* Navigation in Modal */}
            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 text-foreground hover:bg-muted rounded-full"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 text-foreground hover:bg-muted rounded-full"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}

            {/* Full Size Image */}
            <div className="relative w-full h-full flex items-center justify-center p-8">
              <div className="relative max-w-full max-h-full">
                <Image
                  src={getImageUrl(images[currentImageIndex])}
                  alt={product.title}
                  width={800}
                  height={800}
                  className="object-contain max-w-full max-h-full"
                  onError={() => setImageError(true)}
                />
              </div>
            </div>

            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-50">
                <Badge className="bg-background/90 text-foreground backdrop-blur-sm border border-border/50">
                  {currentImageIndex + 1} / {images.length}
                </Badge>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Main Image with Animation */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentImageIndex}
            initial={{ opacity: 0, scale: 1.05 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="relative aspect-square"
          >
            <Image
              src={getImageUrl(images[currentImageIndex])}
              alt={product.title}
              fill
              className="object-contain p-8 group-hover:scale-105 transition-transform duration-500"
              priority
              onError={() => setImageError(true)}
            />
          </motion.div>
        </AnimatePresence>

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <Button
              variant="secondary"
              size="icon"
              className="absolute left-3 top-1/2 -translate-y-1/2 bg-background/90 backdrop-blur-sm z-10 rounded-full shadow-sm opacity-0 group-hover:opacity-100 hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50"
              onClick={prevImage}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="absolute right-3 top-1/2 -translate-y-1/2 bg-background/90 backdrop-blur-sm z-10 rounded-full shadow-sm opacity-0 group-hover:opacity-100 hover:bg-background hover:scale-105 transition-all duration-200 border border-border/50"
              onClick={nextImage}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </>
        )}

        {/* Image Indicator Dots */}
        {images.length > 1 && (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10">
            <div className="flex space-x-2 bg-background/90 backdrop-blur-sm rounded-full px-3 py-2 border border-border/50">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setImage(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentImageIndex 
                      ? 'bg-primary shadow-sm scale-125' 
                      : 'bg-muted-foreground/50 hover:bg-muted-foreground/75'
                  }`}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Thumbnail Grid */}
      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-3">
          {images.map((image, index) => (
            <motion.button
              key={index}
              onClick={() => setImage(index)}
              className={`relative aspect-square rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                index === currentImageIndex 
                  ? 'border-primary shadow-lg scale-105 ring-2 ring-primary/20' 
                  : 'border-border hover:border-primary/50 hover:shadow-md'
              }`}
              whileHover={{ scale: index === currentImageIndex ? 1.05 : 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Image 
                src={getImageUrl(image)} 
                alt={`${product.title} - View ${index + 1}`} 
                fill 
                className="object-cover"
                onError={() => setImageError(true)}
              />
              
              {/* Overlay for non-active thumbnails */}
              {index !== currentImageIndex && (
                <div className="absolute inset-0 bg-foreground/10 hover:bg-foreground/5 transition-colors duration-200" />
              )}
              
              {/* Active indicator */}
              {index === currentImageIndex && (
                <div className="absolute inset-0 ring-2 ring-primary ring-inset rounded-xl" />
              )}
            </motion.button>
          ))}
        </div>
      )}

      {/* Image Info */}
      <div className="text-center space-y-1">
        <p className="text-xs text-muted-foreground">
          Click to zoom 
        </p>
      </div>
    </div>
  )
}