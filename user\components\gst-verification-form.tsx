"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/contexts/auth-context"
import { AlertCircle, CheckCircle2, Info } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import LoadingSpinner from "@/components/loading-spinner"

export default function GstVerificationForm() {
  const { user, verifyGST, isLoading } = useAuth()
  const [gstNumber, setGstNumber] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [verificationResult, setVerificationResult] = useState<{
    success: boolean
    businessName?: string
    address?: string
  } | null>(null)
  const router = useRouter()

  useEffect(() => {
    if (user) {
      setGstNumber(user.gstNumber || "")
    }
  }, [user])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setVerificationResult(null)

    // Basic GST validation
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
    if (!gstRegex.test(gstNumber)) {
      setError("Invalid GST format. GST should be 15 characters with format: 22AAAAA0000A1Z5")
      return
    }

    const result = await verifyGST(gstNumber)

    if (result.success) {
      setVerificationResult({
        success: true,
        businessName: result.data?.businessName,
        address: result.data?.address,
      })

      // Redirect after successful verification
      setTimeout(() => {
        router.push("/account")
      }, 3000)
    } else {
      setError(result.error || "GST verification failed")
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white border rounded-lg overflow-hidden"
    >
      <div className="p-6 border-b">
        <h2 className="text-xl font-medium">GST Verification</h2>
      </div>

      <div className="p-6">
        {!verificationResult ? (
          <>
            <Alert className="mb-6 bg-blue-50 text-blue-800 border-blue-200">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Your GST number must be verified before you can make purchases. This helps us comply with Indian tax
                regulations.
              </AlertDescription>
            </Alert>

            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="gstNumber" className="text-sm font-medium">
                  GST Number <span className="text-red-500">*</span>
                </label>
                <Input
                  id="gstNumber"
                  value={gstNumber}
                  onChange={(e) => setGstNumber(e.target.value)}
                  placeholder="e.g. 27AADCB2230M1ZT"
                  required
                />
                <p className="text-xs text-neutral-500">Enter your valid 15-digit GST number for verification</p>
              </div>

              <Button type="submit" className="w-full bg-teal-600 hover:bg-teal-700" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center">
                    <LoadingSpinner /> <span className="ml-2">Verifying...</span>
                  </span>
                ) : (
                  "Verify GST"
                )}
              </Button>
            </form>
          </>
        ) : (
          <motion.div initial={{ opacity: 0, scale: 0.9 }} animate={{ opacity: 1, scale: 1 }} className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-medium text-green-800 mb-2">GST Verified Successfully!</h3>
            <div className="bg-green-50 rounded-lg p-4 mb-6 text-left">
              <p className="mb-2">
                <strong>Trade Name:</strong> {verificationResult.businessName}
              </p>
              <p>
                <strong>Address:</strong> {verificationResult.address}
              </p>
            </div>
            <p className="text-neutral-600 mb-6">
              Your account is now fully verified. You will be redirected to your account dashboard in a moment.
            </p>
            <Button onClick={() => router.push("/account")} className="bg-teal-600 hover:bg-teal-700">
              Go to Dashboard
            </Button>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}