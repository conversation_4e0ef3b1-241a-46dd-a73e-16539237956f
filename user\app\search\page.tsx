import type { Metadata } from "next"
import { Suspense } from 'react'
import SearchProducts from "@/components/search-products"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Search Products | Benzochem Industries",
  description: "Search through our extensive catalog of high-quality chemical products for industrial and pharmaceutical applications.",
}

function SearchPageSkeleton() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Search Header Skeleton */}
        <div className="mb-8">
          <Skeleton className="h-10 w-64 mb-4" />
          <Skeleton className="h-6 w-96 mb-6" />
          <Skeleton className="h-12 w-full max-w-2xl" />
        </div>

        {/* Filters Skeleton */}
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-64 space-y-4">
            <Skeleton className="h-8 w-32" />
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-6 w-full" />
              ))}
            </div>
          </div>

          {/* Products Grid Skeleton */}
          <div className="flex-1">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(12)].map((_, i) => (
                <div key={i} className="border border-border rounded-lg p-4">
                  <Skeleton className="h-48 w-full rounded-md mb-4" />
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3 mb-4" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={<SearchPageSkeleton />}>
      <SearchProducts />
    </Suspense>
  )
}