// Enhanced security middleware for user application
// Provides comprehensive security features and monitoring

export interface SecurityConfig {
  enableRateLimiting: boolean;
  enableCSRFProtection: boolean;
  enableSecurityHeaders: boolean;
  enableInputValidation: boolean;
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  blockDuration: number; // in milliseconds
}

export interface SecurityEvent {
  type: 'rate_limit_exceeded' | 'invalid_input' | 'suspicious_activity' | 'csrf_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: number;
  details?: any;
}

// Default security configuration
const DEFAULT_CONFIG: SecurityConfig = {
  enableRateLimiting: true,
  enableCSRFProtection: true,
  enableSecurityHeaders: true,
  enableInputValidation: true,
  maxRequestsPerMinute: 60,
  maxRequestsPerHour: 1000,
  blockDuration: 15 * 60 * 1000, // 15 minutes
};

// In-memory rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, {
  requests: number[];
  blocked: boolean;
  blockExpiry: number;
}>();

// Security event store (use database in production)
const securityEvents: SecurityEvent[] = [];

export class SecurityMiddleware {
  private config: SecurityConfig;

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Rate limiting implementation
  checkRateLimit(clientIP: string): { allowed: boolean; retryAfter?: number } {
    if (!this.config.enableRateLimiting) {
      return { allowed: true };
    }

    const now = Date.now();
    const clientData = rateLimitStore.get(clientIP) || {
      requests: [],
      blocked: false,
      blockExpiry: 0
    };

    // Check if client is currently blocked
    if (clientData.blocked && now < clientData.blockExpiry) {
      return {
        allowed: false,
        retryAfter: Math.ceil((clientData.blockExpiry - now) / 1000)
      };
    }

    // Reset block if expired
    if (clientData.blocked && now >= clientData.blockExpiry) {
      clientData.blocked = false;
      clientData.requests = [];
    }

    // Clean old requests (older than 1 hour)
    const oneHourAgo = now - (60 * 60 * 1000);
    clientData.requests = clientData.requests.filter(timestamp => timestamp > oneHourAgo);

    // Check hourly limit
    if (clientData.requests.length >= this.config.maxRequestsPerHour) {
      clientData.blocked = true;
      clientData.blockExpiry = now + this.config.blockDuration;
      
      this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'high',
        description: `Hourly rate limit exceeded for IP: ${clientIP}`,
        ipAddress: clientIP,
        timestamp: now,
        details: { requestCount: clientData.requests.length, limit: this.config.maxRequestsPerHour }
      });

      rateLimitStore.set(clientIP, clientData);
      return {
        allowed: false,
        retryAfter: Math.ceil(this.config.blockDuration / 1000)
      };
    }

    // Check per-minute limit
    const oneMinuteAgo = now - (60 * 1000);
    const recentRequests = clientData.requests.filter(timestamp => timestamp > oneMinuteAgo);
    
    if (recentRequests.length >= this.config.maxRequestsPerMinute) {
      this.logSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'medium',
        description: `Per-minute rate limit exceeded for IP: ${clientIP}`,
        ipAddress: clientIP,
        timestamp: now,
        details: { requestCount: recentRequests.length, limit: this.config.maxRequestsPerMinute }
      });

      return {
        allowed: false,
        retryAfter: 60
      };
    }

    // Record this request
    clientData.requests.push(now);
    rateLimitStore.set(clientIP, clientData);

    return { allowed: true };
  }

  // Input validation and sanitization
  validateInput(input: any, rules: ValidationRules): ValidationResult {
    if (!this.config.enableInputValidation) {
      return { valid: true, sanitized: input };
    }

    const errors: string[] = [];
    let sanitized = { ...input };

    // Email validation
    if (rules.email && input.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(input.email)) {
        errors.push('Invalid email format');
      } else {
        sanitized.email = input.email.toLowerCase().trim();
      }
    }

    // Password validation
    if (rules.password && input.password) {
      if (input.password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(input.password)) {
        errors.push('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      }
    }

    // String sanitization
    if (rules.sanitizeStrings) {
      Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string') {
          // Remove potentially dangerous characters
          sanitized[key] = sanitized[key]
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim();
        }
      });
    }

    // Phone number validation
    if (rules.phone && input.phone) {
      const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
      if (!phoneRegex.test(input.phone)) {
        errors.push('Invalid phone number format');
      } else {
        sanitized.phone = input.phone.replace(/\D/g, ''); // Keep only digits
      }
    }

    // GST number validation (Indian format)
    if (rules.gstNumber && input.gstNumber) {
      const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
      if (!gstRegex.test(input.gstNumber)) {
        errors.push('Invalid GST number format');
      } else {
        sanitized.gstNumber = input.gstNumber.toUpperCase().trim();
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      sanitized
    };
  }

  // Generate security headers
  getSecurityHeaders(): Record<string, string> {
    if (!this.config.enableSecurityHeaders) {
      return {};
    }

    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';",
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
    };
  }

  // CSRF token generation and validation
  generateCSRFToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  validateCSRFToken(sessionToken: string, providedToken: string): boolean {
    if (!this.config.enableCSRFProtection) {
      return true;
    }

    // In a real implementation, you would validate against a stored token
    // For now, we'll do a basic validation
    return sessionToken === providedToken && sessionToken.length === 64;
  }

  // Security event logging
  logSecurityEvent(event: SecurityEvent): void {
    securityEvents.push(event);
    
    // Log to console for development
    console.warn(`🚨 Security Event [${event.severity.toUpperCase()}]: ${event.description}`, {
      type: event.type,
      timestamp: new Date(event.timestamp).toISOString(),
      details: event.details
    });

    // In production, send to monitoring service
    if (event.severity === 'critical' || event.severity === 'high') {
      this.alertSecurityTeam(event);
    }

    // Keep only last 1000 events in memory
    if (securityEvents.length > 1000) {
      securityEvents.splice(0, securityEvents.length - 1000);
    }
  }

  // Alert security team for critical events
  private alertSecurityTeam(event: SecurityEvent): void {
    // In production, this would send alerts via email, Slack, etc.
    console.error('🚨 CRITICAL SECURITY EVENT:', event);
  }

  // Get security statistics
  getSecurityStats(): SecurityStats {
    const now = Date.now();
    const lastHour = now - (60 * 60 * 1000);
    const last24Hours = now - (24 * 60 * 60 * 1000);

    const recentEvents = securityEvents.filter(event => event.timestamp > lastHour);
    const dailyEvents = securityEvents.filter(event => event.timestamp > last24Hours);

    return {
      totalEvents: securityEvents.length,
      eventsLastHour: recentEvents.length,
      eventsLast24Hours: dailyEvents.length,
      blockedIPs: Array.from(rateLimitStore.entries())
        .filter(([_, data]) => data.blocked)
        .map(([ip]) => ip),
      eventsByType: this.groupEventsByType(dailyEvents),
      eventsBySeverity: this.groupEventsBySeverity(dailyEvents)
    };
  }

  private groupEventsByType(events: SecurityEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private groupEventsBySeverity(events: SecurityEvent[]): Record<string, number> {
    return events.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  // Clean up old data
  cleanup(): void {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    // Clean rate limit store
    for (const [ip, data] of rateLimitStore.entries()) {
      if (!data.blocked && data.requests.every(timestamp => timestamp < oneHourAgo)) {
        rateLimitStore.delete(ip);
      }
    }

    // Clean old security events (keep last 24 hours)
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    const recentEvents = securityEvents.filter(event => event.timestamp > oneDayAgo);
    securityEvents.splice(0, securityEvents.length, ...recentEvents);
  }
}

// Interfaces
export interface ValidationRules {
  email?: boolean;
  password?: boolean;
  phone?: boolean;
  gstNumber?: boolean;
  sanitizeStrings?: boolean;
}

export interface ValidationResult {
  valid: boolean;
  errors?: string[];
  sanitized: any;
}

export interface SecurityStats {
  totalEvents: number;
  eventsLastHour: number;
  eventsLast24Hours: number;
  blockedIPs: string[];
  eventsByType: Record<string, number>;
  eventsBySeverity: Record<string, number>;
}

// Export singleton instance
export const securityMiddleware = new SecurityMiddleware();

// Utility functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[!@#$%^&*])/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

export const sanitizeInput = (input: string): string => {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
};

// Auto-cleanup every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    securityMiddleware.cleanup();
  }, 60 * 60 * 1000); // 1 hour
}