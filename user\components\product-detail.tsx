"use client";

import { useState, useEffect } from "react";
import { useSecureQuotation } from "@/hooks/use-secure-quotation";
import { motion } from "framer-motion";
import {
  ShoppingBag,
  Heart,
  Share2,
  Download,
  ChevronDown,
  ChevronUp,
  Award,
  Shield,
  Truck,
  CheckCircle,
  Info,
  Package,
  Sparkles,
  Target,
  Beaker,
  Factory,
  BookmarkPlus,
  ExternalLink
} from "lucide-react";
import { Button } from "@/components/ui/button"
import AddToQuotationButton from "@/components/add-to-quotation-button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { getProductImageUrl, getProductCategory } from "@/lib/image-utils";
import type { Product, ProductVariant } from "@/lib/types";
import ProductImageGallery from "@/components/product-image-gallery";
import AnimatedBackground from "@/components/animated-background";

interface ProductDetailProps {
  product: Product;
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | undefined>(undefined);
  const [packageSize, setPackageSize] = useState("1 KG");
  const [isSaved, setIsSaved] = useState(false);
  const { addToQuotation, isLoading: isQuotationLoading } = useSecureQuotation();
  // const { toast } = useToast();

  
  useEffect(() => {
    // Initialize selectedVariant with the first available variant
    if (product.variants?.edges && product.variants.edges.length > 0) {
      const firstAvailableVariant = product.variants.edges.find(edge => edge.node.availableForSale)?.node;
      setSelectedVariant(firstAvailableVariant || product.variants.edges[0].node);
    }
  }, [product.variants]);

  const handleVariantChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const variantId = event.target.value;
    const newSelectedVariant = product.variants?.edges?.find(
      (edge) => edge.node.id === variantId
    )?.node;
    setSelectedVariant(newSelectedVariant);
  };

  // Handle package size change
  const handlePackageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setPackageSize(event.target.value);
  };

  // Get available package sizes from product metafields or use defaults
  const getAvailablePackageSizes = () => {
    const packageSizesMetafield = getMetafieldValue("package_sizes");
    if (packageSizesMetafield) {
      try {
        const sizes = JSON.parse(packageSizesMetafield);
        if (Array.isArray(sizes) && sizes.length > 0) {
          return sizes;
        }
      } catch (error) {
        console.warn("Failed to parse package sizes metafield:", error);
      }
    }
    // Default package sizes for chemical products
    return ["1 KG", "2 KG", "5 KG", "10 KG", "25 KG"];
  };

  // Handle save/bookmark functionality
  const handleSaveProduct = () => {
    setIsSaved(!isSaved);
    console.log(isSaved ? "Removed from saved products" : "Added to saved products");
  };

  // Handle share functionality
  const handleShareProduct = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.title,
          text: `Check out this product: ${product.title}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  // Fallback copy link functionality
  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href).then(() => {
      console.log("Link copied to clipboard");
    }).catch(() => {
      console.error("Failed to copy link");
    });
  };

  // Handle document download
  const handleDownloadSpecs = () => {
    const specs = {
      productName: product.title,
      casNumber: getMetafieldValue("cas_number"),
      molecularFormula: getMolecularFormula(),
      purity: getMetafieldValue("purity"),
      hsnNumber: getMetafieldValue("hsn_number"),
      description: product.description,
      specifications: keySpecs.map(spec => ({ [spec.label]: spec.value }))
    };
    
    const dataStr = JSON.stringify(specs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${product.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_specifications.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // Helper function to check if product is available for sale
  const isProductAvailable = () => {
    // If product has variants, check variant availability
    if (product.variants?.edges && product.variants.edges.length > 0) {
      // Check variant availability first
      if (!selectedVariant?.availableForSale) {
        return false;
      }
      
      // Check variant quantity (from Shopify/external system)
      if (selectedVariant.quantityAvailable !== null && 
          selectedVariant.quantityAvailable !== undefined && 
          selectedVariant.quantityAvailable <= 0) {
        return false;
      }
    }
    
    // Check product quantity (from admin-managed inventory)
    if (product.quantity !== undefined && product.quantity <= 0) {
      return false;
    }
    
    return true;
  };

  // Helper function to get available stock count
  const getAvailableStock = () => {
    // Prioritize product quantity (admin-managed) over variant quantity
    if (product.quantity !== undefined && product.quantity !== null) {
      return product.quantity;
    }
    
    // Fallback to variant quantity
    if (selectedVariant?.quantityAvailable !== null && 
        selectedVariant?.quantityAvailable !== undefined) {
      return selectedVariant.quantityAvailable;
    }
    
    // Unknown stock level
    return null;
  };

  // Helper function to check if stock is low
  const isLowStock = () => {
    const stock = getAvailableStock();
    return stock !== null && stock > 0 && stock < 10;
  };

  // Helper function to get stock status message
  const getStockStatus = () => {
    if (!isProductAvailable()) {
      return "Out of Stock";
    }
    
    const stock = getAvailableStock();
    if (isLowStock() && stock !== null) {
      return `Low in stock`;
    }
    
    return "In Stock";
  };

  // Helper function to get stock badge color
  const getStockBadgeColor = () => {
    if (!isProductAvailable()) {
      return "bg-red-50 text-red-700 border-red-200";
    }
    
    if (isLowStock()) {
      return "bg-yellow-50 text-yellow-700 border-yellow-200";
    }
    
    return "bg-green-50 text-green-700 border-green-200";
  };

  
  const handleAddToQuotation = async () => {
    // Check if product has variants and if so, ensure one is selected
    if (product.variants?.edges && product.variants.edges.length > 0 && !selectedVariant) {
      console.error("No variant selected or available");
      return;
    }
    
    if (!isProductAvailable()) {
      console.error("Product is not available for sale");
      return;
    }

    // Check if requested quantity exceeds available stock
    const availableStock = getAvailableStock();
    if (availableStock !== null && quantity > availableStock) {
      console.error(`Requested quantity (${quantity}) exceeds available stock (${availableStock})`);
      return;
    }

    const itemToAdd = {
      productId: product.id,
      variantId: selectedVariant?.id || `${product.id}-default`,
      name: product.title,
      category: getCategory(),
      image: getProductImageUrl(product),
      price: parseFloat(selectedVariant?.priceV2?.amount || product.priceRange?.minVariantPrice?.amount || "0"),
      quantity: quantity,
      unit: packageSize,
      specifications: `Package Size: ${packageSize}`,
    };

    console.log("Attempting to add to quotation:", itemToAdd);

    try {
      await addToQuotation(itemToAdd);
      console.log(`${product.title} ${selectedVariant ? `(${selectedVariant.title})` : ''} added to quotation`);
    } catch (error) {
      console.error("Failed to add item to quotation:", error);
    }
  };

  const incrementQuantity = () => {
    const availableStock = getAvailableStock();
    setQuantity((prev) => {
      // If we know the stock level, don't exceed it
      if (availableStock !== null && prev >= availableStock) {
        return prev; // Don't increment if at stock limit
      }
      return prev + 1;
    });
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity((prev) => prev - 1);
    }
  };

  const getCategory = () => {
    return getProductCategory(product);
  };

  // Helper function to get metafield value
  const getMetafieldValue = (key: string) => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return "";
    }
    return (
      product.metafields.find((field) => field && field.key === key)?.value ||
      ""
    );
  };

  // Helper function to safely parse molecular formula
  const getMolecularFormula = () => {
    try {
      const formula = getMetafieldValue("molecular_formula");
      if (!formula) return "N/A";
      
      // Try to parse as JSON (Shopify format)
      const parsed = JSON.parse(formula);
      if (parsed.children && parsed.children[0] && parsed.children[0].children && parsed.children[0].children[0]) {
        return parsed.children[0].children[0].value;
      }
      return formula; // Return as-is if not in expected format
    } catch (error) {
      // If JSON parsing fails, return the raw value or a default
      const formula = getMetafieldValue("molecular_formula");
      return formula || "N/A";
    }
  };

  // Product stats for the hero section
  const productStats = [
    {
      icon: Award,
      value: `${getMetafieldValue("purity") || "99.9"}%`,
      label: "Purity",
      description: "Guaranteed quality"
    },
    {
      icon: Shield,
      value: getMetafieldValue("cas_number") ? "CAS" : "N/A",
      label: "CAS Verified", 
      description: "Certified standard"
    },
    {
      icon: Factory,
      value: "ISO",
      label: "Certified",
      description: "Quality assured"
    },
    {
      icon: Truck,
      value: getStockStatus(),
      label: "Availability",
      description: "Ready to ship"
    }
  ];

  // Product features/benefits
  const productFeatures = [
    {
      icon: Beaker,
      title: "High Purity",
      description: `${getMetafieldValue("purity") || "99.9"}% pure chemical composition with rigorous quality control.`
    },
    {
      icon: Shield,
      title: "Quality Assured",
      description: "All products meet international standards and undergo comprehensive testing."
    },
    {
      icon: Package,
      title: "Secure Packaging",
      description: "Professional-grade packaging ensures product integrity during shipping."
    },
    {
      icon: Target,
      title: "Application Ready",
      description: "Optimized for industrial, pharmaceutical, and research applications."
    }
  ];

  // Key specifications for cards
  const keySpecs = [
    {
      label: "Molecular Formula",
      value: getMolecularFormula(),
      icon: Beaker
    },
    {
      label: "CAS Number", 
      value: getMetafieldValue("cas_number") || "N/A",
      icon: Info
    },
    {
      label: "HSN Number",
      value: getMetafieldValue("hsn_number"),
      icon: Package
    },
    {
      label: "Purity Level",
      value: `${getMetafieldValue("purity") || "99.9"}%`,
      icon: Award
    }
  ];

  return (
    <div className="relative">
      {/* Background Elements */}
      <AnimatedBackground />
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10" />
      
      <div className="container mx-auto px-4 py-8 relative z-10">
        {/* Product Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <Badge className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors">
              <Package className="h-3 w-3 mr-1" />
              {getCategory()}
            </Badge>
            <Badge 
              className={`${getStockBadgeColor()} border transition-colors`}
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              {getStockStatus()}
            </Badge>
          </div>
          
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-tight">
            {product.title}
          </h1>
          
          <div className="max-w-3xl mx-auto mb-8">
            <p 
              className="text-lg md:text-xl text-muted-foreground leading-relaxed" 
              dangerouslySetInnerHTML={{ __html: product.descriptionHtml || product.description }}
            />
          </div>

          {isProductAvailable() && isLowStock() && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center gap-2 bg-amber-50 text-amber-700 px-4 py-2 rounded-full text-sm font-medium border border-amber-200"
            >
              <Sparkles className="h-4 w-4" />
              Hurry! Only {getAvailableStock()} units left
            </motion.div>
          )}
        </motion.div>

        {/* Product Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {productStats.map((stat, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Main Product Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start mb-20">
          {/* Image Gallery Side */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-8"
          >
            <Card className="overflow-hidden border-0">
              <ProductImageGallery product={product} />
            </Card>

            {/* Product Features */}
            <Card className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  Product Features
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {productFeatures.map((feature, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                      className="flex items-start space-x-3 p-4 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors"
                    >
                      <div className="w-8 h-8 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                        <feature.icon className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-foreground mb-1">{feature.title}</h4>
                        <p className="text-xs text-muted-foreground leading-relaxed">{feature.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Product Info Side */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-8"
          >
            {/* Specifications */}
            <Card className="border-0 bg-card/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                  <Info className="h-5 w-5 text-primary" />
                  Technical Specifications
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {keySpecs.map((spec, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-xl bg-muted/30">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-xl flex items-center justify-center">
                          <spec.icon className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">{spec.label}</p>
                          <p className="font-medium text-foreground">{spec.value}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Purchase Section */}
            <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
              <CardContent className="p-6 space-y-6">
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  <ShoppingBag className="h-5 w-5 text-primary" />
                  Order Details
                </h3>
                
                {/* Variant Selection */}
                {product.variants?.edges && product.variants.edges.length > 1 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Product Variant
                    </label>
                    <select 
                      className="w-full h-12 px-4 border rounded-xl bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all"
                      value={selectedVariant?.id || ""}
                      onChange={handleVariantChange}
                    >
                      {product.variants.edges.map((edge) => (
                        <option key={edge.node.id} value={edge.node.id}>
                          {edge.node.title} - ${edge.node.priceV2.amount} {edge.node.priceV2.currencyCode}
                          {!edge.node.availableForSale && " (Out of Stock)"}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Quantity
                      {getAvailableStock() !== null && (
                        <span className="text-xs text-muted-foreground">
                          (Max: {getAvailableStock()})
                        </span>
                      )}
                    </label>
                    <div className="flex items-center border rounded-xl bg-background/50 backdrop-blur-sm">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-12 w-12 rounded-l-xl rounded-r-none"
                        onClick={decrementQuantity}
                        disabled={quantity <= 1}
                      >
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                      <span className="flex-1 text-center font-medium py-3">{quantity}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-12 w-12 rounded-r-xl rounded-l-none"
                        onClick={incrementQuantity}
                        disabled={getAvailableStock() !== null && quantity >= getAvailableStock()!}
                      >
                        <ChevronUp className="h-4 w-4" />
                      </Button>
                    </div>
                    {getAvailableStock() !== null && quantity > getAvailableStock()! && (
                      <p className="text-xs text-red-600 mt-2">
                        Quantity exceeds available stock
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Package Size
                    </label>
                    <select 
                      className="w-full h-12 px-4 border rounded-xl bg-background/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all"
                      value={packageSize}
                      onChange={handlePackageSizeChange}
                    >
                      {getAvailablePackageSizes().map((size) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <AddToQuotationButton
                  productId={product.id}
                  variantId={selectedVariant?.id || `${product.id}-default`}
                  name={product.title}
                  price={parseFloat(selectedVariant?.priceV2?.amount || product.priceRange?.minVariantPrice?.amount || "0")}
                  quantity={quantity}
                  image={(() => {
                    // Handle new admin API format
                    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
                      const firstImage = product.images[0]
                      return typeof firstImage === 'string' ? firstImage : firstImage.url
                    }
                    // Handle legacy Shopify format
                    return product.images?.edges?.[0]?.node?.url || 
                           product.media?.edges?.[0]?.node?.image?.url || 
                           product.featuredMedia?.preview?.image?.url || ""
                  })()}
                  category={getCategory()}
                  packageSize={packageSize}
                  disabled={!isProductAvailable() || (product.variants?.edges && product.variants.edges.length > 0 && !selectedVariant)}
                  className="w-full h-14 text-base font-medium"
                />
                
                <div className="grid grid-cols-2 gap-3">
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="flex-1 h-12"
                    onClick={handleSaveProduct}
                  >
                    {isSaved ? (
                      <BookmarkPlus className="h-4 w-4 mr-2 fill-current" />
                    ) : (
                      <Heart className="h-4 w-4 mr-2" />
                    )}
                    {isSaved ? "Saved" : "Save"}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="flex-1 h-12"
                    onClick={handleShareProduct}
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Trust Indicators */}
            <div className="grid grid-cols-3 gap-4">
              {[
                { icon: Shield, text: "Quality Guaranteed" },
                { icon: Truck, text: "Fast Shipping" },
                { icon: Award, text: "Certified Pure" }
              ].map((indicator, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                  className="flex flex-col items-center text-center p-4 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors"
                >
                  <indicator.icon className="h-6 w-6 text-primary mb-2" />
                  <span className="text-xs text-muted-foreground">{indicator.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Download Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-semibold mb-4 text-foreground">Technical Documentation</h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Access detailed specifications, safety data sheets, and technical documents for this product.
              </p>
              <Button className="group" onClick={handleDownloadSpecs}>
                <Download className="mr-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                Download Complete Specs
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}