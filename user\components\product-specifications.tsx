"use client"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import type { Product } from "@/lib/types"

interface ProductSpecificationsProps {
  product: Product
}

export default function ProductSpecifications({ product }: ProductSpecificationsProps) {

  const getMetafieldValue = (key: string) => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return ''
    }
    return product.metafields.find(field => field && field.key === key)?.value || ''
  }

  // Helper function to safely parse molecular formula
  const getMolecularFormula = () => {
    try {
      const formula = getMetafieldValue("molecular_formula");
      if (!formula) return "N/A";
      
      // Try to parse as JSON (Shopify format)
      const parsed = JSON.parse(formula);
      if (parsed.children && parsed.children[0] && parsed.children[0].children && parsed.children[0].children[0]) {
        return parsed.children[0].children[0].value;
      }
      return formula; // Return as-is if not in expected format
    } catch (error) {
      // If JSON parsing fails, return the raw value or a default
      const formula = getMetafieldValue("molecular_formula");
      return formula || "N/A";
    }
  };

  // Helper function to safely parse molecular weight
  const getMolecularWeight = () => {
    try {
      const weight = getMetafieldValue("molecular_weight");
      if (!weight) return "N/A";
      
      // Try to parse as JSON (Shopify format)
      const parsed = JSON.parse(weight);
      if (parsed.value) {
        return `${parsed.value} g/mol`;
      }
      return `${weight} g/mol`; // Return as-is if not in expected format
    } catch (error) {
      // If JSON parsing fails, return the raw value or a default
      const weight = getMetafieldValue("molecular_weight");
      return weight ? `${weight} g/mol` : "N/A";
    }
  };

  // Helper function to safely parse applications
  const getApplications = () => {
    try {
      const apps = getMetafieldValue("recommended_applications");
      if (!apps) return [];
      
      // Try to parse as JSON (Shopify format)
      const parsed = JSON.parse(apps);
      if (parsed.children) {
        return parsed.children.filter((app: { level: number }) => app.level === 3);
      }
      return []; // Return empty array if not in expected format
    } catch (error) {
      // If JSON parsing fails, return empty array
      return [];
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-medium pt-10 mb-6 text-gray-900 dark:text-gray-100">Product Specifications</h2>

      <Tabs defaultValue="technical" className="w-full">
        <TabsList className={`grid w-full ${getMetafieldValue("recommended_applications") ? "grid-cols-4" : "grid-cols-3"} mb-8`}>
          <TabsTrigger value="technical">Technical Data</TabsTrigger>
          {getMetafieldValue("recommended_applications") && (
            <TabsTrigger value="applications">Applications</TabsTrigger>
          )}
          <TabsTrigger value="safety">Safety & Handling</TabsTrigger>
          <TabsTrigger value="shipping">Shipping & Storage</TabsTrigger>
        </TabsList>

        <TabsContent value="technical" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <table className="w-full">
                <tbody>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium w-1/3">
                      Chemical Name
                    </th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{product.title || ""}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">CAS Number</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMetafieldValue("cas_number") || "N/A"}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">
                      Molecular Formula
                    </th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMolecularFormula()}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">Molecular Weight</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMolecularWeight()}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">Appearance</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMetafieldValue("appearance") || "N/A"}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">Purity</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMetafieldValue("purity") ? `${getMetafieldValue("purity")}%` : "N/A"}</td>
                  </tr>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">Solubility</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMetafieldValue("solubility") || "N/A"}</td>
                  </tr>
                  <tr>
                    <th className="text-left py-3 px-4 bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-medium">pH Value</th>
                    <td className="py-3 px-4 text-gray-700 dark:text-gray-300">{getMetafieldValue("ph_value") || "N/A"}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="applications" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-gray-100">Recommended Applications</h3>
              <ul className="space-y-4">
                {getApplications().length > 0 ? (
                  getApplications().map((application: any, index: any) => (
                    <li key={index} className="flex items-start">
                      <span className="h-5 w-5 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        {index + 1}
                      </span>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">{application.children?.[0]?.value || `Application ${index + 1}`}</p>
                        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">Provides excellent results with standard processing parameters.</p>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="flex items-start">
                    <span className="h-5 w-5 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                      1
                    </span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Industrial Manufacturing</p>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">Suitable for various industrial applications and manufacturing processes.</p>
                    </div>
                  </li>
                )}
              </ul>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="safety" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-gray-100">Safety Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Handling Precautions</h4>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                    <li>• Wear appropriate protective equipment</li>
                    <li>• Use in well-ventilated areas</li>
                    <li>• Avoid contact with skin and eyes</li>
                    <li>• Wash hands thoroughly after handling</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Storage Recommendations</h4>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                    <li>• Store in a cool, dry place</li>
                    <li>• Keep container tightly closed</li>
                    <li>• Protect from direct sunlight</li>
                    <li>• Keep away from incompatible materials</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">First Aid Measures</h4>
                <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                  <p className="mb-2 text-gray-600 dark:text-gray-400">
                    <strong className="text-gray-900 dark:text-gray-100">Eye Contact:</strong> Rinse cautiously with water for several minutes. Remove contact lenses
                    if present and easy to do. Continue rinsing.
                  </p>
                  <p className="mb-2 text-gray-600 dark:text-gray-400">
                    <strong className="text-gray-900 dark:text-gray-100">Skin Contact:</strong> Wash with plenty of soap and water. If skin irritation occurs, get
                    medical advice/attention.
                  </p>
                  <p className="mb-2 text-gray-600 dark:text-gray-400">
                    <strong className="text-gray-900 dark:text-gray-100">Inhalation:</strong> Remove person to fresh air and keep comfortable for breathing.
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">
                    <strong className="text-gray-900 dark:text-gray-100">Ingestion:</strong> Rinse mouth. Do NOT induce vomiting. Seek immediate medical attention.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-6">
          <motion.div initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-gray-100">Shipping & Storage Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="border border-gray-200 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-900">
                  <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Packaging Options</h4>
                  <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                    <li>• Standard packaging</li>
                    <li>• Bulk packaging (100kg+)</li>
                    <li>• Industrial packaging (1000kg+)</li>
                    <li>• Custom packaging available upon request</li>
                  </ul>
                </div>
                <div className="border border-gray-200 dark:border-gray-600 rounded-md p-4 bg-gray-50 dark:bg-gray-900">
                  <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Shipping Classification</h4>
                  <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                    <li>• Hazard Class: Non-hazardous</li>
                    <li>• UN Number: Not applicable</li>
                    <li>• Proper Shipping Name: Chemical product</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2 text-gray-900 dark:text-gray-100">Storage Conditions</h4>
                <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-md p-4">
                  <p className="mb-2 text-gray-700 dark:text-gray-300">
                    <strong className="text-gray-900 dark:text-gray-100">Temperature:</strong> Store at room temperature (15-25°C)
                  </p>
                  <p className="mb-2 text-gray-700 dark:text-gray-300">
                    <strong className="text-gray-900 dark:text-gray-100">Humidity:</strong> Keep in a dry environment (&lt;60% relative humidity)
                  </p>
                  <p className="text-gray-700 dark:text-gray-300">
                    <strong className="text-gray-900 dark:text-gray-100">Light Sensitivity:</strong> Protect from direct sunlight and UV light
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
