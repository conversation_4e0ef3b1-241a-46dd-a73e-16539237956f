"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import { AnimatePresence, motion } from "framer-motion"
import { Search, ShoppingBag, Menu, X, User, Loader2, ChevronDown, Sun, Moon, Eye, ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useAuth } from "@/contexts/auth-context"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Product } from "@/lib/types"
import { searchProducts } from "@/lib/api-client"
import { useTheme } from "next-themes"
import { useRouter } from "next/navigation"
import { Separator } from "@/components/ui/separator"
import { getProductImageUrl, getProductCategory } from "@/lib/image-utils"

export default function HeaderEnhanced() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [isProductsDropdownOpen, setIsProductsDropdownOpen] = useState(false)
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const { user, logout } = useAuth()
  const { currentQuotation } = useSecureQuotation()
  const { theme, setTheme } = useTheme()
  const router = useRouter()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Handle search functionality
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current)
    }

    if (searchQuery.trim().length > 2) {
      setIsLoading(true)
      searchTimeoutRef.current = setTimeout(async () => {
        try {
          const results = await searchProducts(searchQuery)
          setSearchResults(results.slice(0, 5)) // Show top 5 results
        } catch (error) {
          console.error('Search error:', error)
          setSearchResults([])
        } finally {
          setIsLoading(false)
        }
      }, 300)
    } else {
      setSearchResults([])
      setIsLoading(false)
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current)
      }
    }
  }, [searchQuery])

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setIsSearchOpen(false)
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
    }
  }

  // Handle clicking outside search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false)
      }
    }

    if (isSearchOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isSearchOpen])

  // Handle product click
  const handleProductClick = (productId: string) => {
    setIsSearchOpen(false)
    setSearchQuery("")
    router.push(`/products/${productId.split('/').pop()}`)
  }

  // Get metafield value helper
  const getMetafieldValue = (product: Product, key: string, namespace: string = 'chemical') => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return ''
    }
    const metafield = product.metafields.find(m => m?.namespace === namespace && m?.key === key)
    return metafield?.value || ''
  }

  // Get product image helper
  const getProductImage = (product: Product) => {
    return getProductImageUrl(product)
  }

  const quotationItemCount = currentQuotation?.items?.length || 0

  return (
    <>
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, type: "spring", stiffness: 100 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-background/80 backdrop-blur-lg border-b border-border shadow-warm"
            : "bg-transparent"
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16 md:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="relative">
                <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-primary to-teal-600 rounded-lg flex items-center justify-center transition-transform group-hover:scale-105">
                  <span className="text-white font-bold text-sm md:text-base">B</span>
                </div>
              </div>
              <span className="font-bold text-lg md:text-xl text-foreground hidden sm:block">
                Benzochem
              </span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center space-x-1 text-foreground hover:text-primary transition-colors">
                  <span>Products</span>
                  <ChevronDown className="w-4 h-4" />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="vanilla-card">
                  <DropdownMenuItem asChild>
                    <Link href="/categories/powder" className="w-full">Powder Products</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/categories/liquid" className="w-full">Liquid Products</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/search" className="w-full">Search All Products</Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Link 
                href="/about" 
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                About
              </Link>
              <Link 
                href="/contact" 
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Contact
              </Link>
            </nav>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="hover:bg-accent/50 transition-colors"
              >
                <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                <span className="sr-only">Toggle theme</span>
              </Button>

              {/* Search */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSearchOpen(true)}
                className="hover:bg-accent/50 transition-colors hidden sm:flex"
              >
                <Search className="w-4 h-4" />
              </Button>

              {/* Quotation */}
              <Link href="/quotation">
                <Button variant="ghost" size="sm" className="relative hover:bg-accent/50 transition-colors">
                  <ShoppingBag className="w-4 h-4" />
                  {quotationItemCount > 0 && (
                    <Badge 
                      variant="default" 
                      className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary hover:bg-primary"
                    >
                      {quotationItemCount}
                    </Badge>
                  )}
                </Button>
              </Link>

              {/* User Menu */}
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="hover:bg-accent/50 transition-colors">
                      <User className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="vanilla-card">
                    <DropdownMenuItem asChild>
                      <Link href="/account" className="w-full">Account</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/quotation" className="w-full">Current Quotation</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={logout} className="text-destructive">
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="hidden sm:flex items-center space-x-2">
                  <Link href="/login">
                    <Button variant="ghost" size="sm" className="hover:bg-accent/50 transition-colors">
                      Login
                    </Button>
                  </Link>
                  <Link href="/register">
                    <Button size="sm" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                      Register
                    </Button>
                  </Link>
                </div>
              )}

              {/* Mobile Menu */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden hover:bg-accent/50 transition-colors"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden bg-background/95 backdrop-blur-lg border-t border-border"
            >
              <div className="container mx-auto px-4 py-4">
                <nav className="flex flex-col space-y-4">
                  {/* Mobile Search */}
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsMobileMenuOpen(false)
                      setIsSearchOpen(true)
                    }}
                    className="w-full justify-start"
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Search Products
                  </Button>
                  
                  <Link href="/categories/powder" className="text-foreground hover:text-primary transition-colors">
                    Powder Products
                  </Link>
                  <Link href="/categories/liquid" className="text-foreground hover:text-primary transition-colors">
                    Liquid Products
                  </Link>
                  <Link href="/search" className="text-foreground hover:text-primary transition-colors">
                    All Products
                  </Link>
                  <Link href="/about" className="text-foreground hover:text-primary transition-colors">
                    About
                  </Link>
                  <Link href="/contact" className="text-foreground hover:text-primary transition-colors">
                    Contact
                  </Link>
                  
                  {!user && (
                    <div className="flex flex-col space-y-2 pt-4 border-t border-border">
                      <Link href="/login">
                        <Button variant="ghost" className="w-full justify-start">
                          Login
                        </Button>
                      </Link>
                      <Link href="/register">
                        <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground">
                          Register
                        </Button>
                      </Link>
                    </div>
                  )}
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>

      {/* Enhanced Search Modal */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
            onClick={() => setIsSearchOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -20 }}
              className="container mx-auto px-4 pt-20"
              onClick={(e) => e.stopPropagation()}
              ref={searchRef}
            >
              <div className="max-w-2xl mx-auto">
                <div className="vanilla-card p-6">
                  {/* Search Input */}
                  <form onSubmit={handleSearchSubmit}>
                    <div className="flex items-center space-x-4 mb-4">
                      <Search className="w-5 h-5 text-muted-foreground" />
                      <Input
                        id="search-input"
                        placeholder="Search by product name, CAS number, molecular formula..."
                        className="border-0 bg-transparent text-lg focus:ring-0"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        autoFocus
                      />
                      <Button variant="ghost" size="sm" onClick={() => setIsSearchOpen(false)}>
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </form>

                  {/* Search Results */}
                  <div className="min-h-[200px]">
                    {isLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="w-6 h-6 animate-spin text-primary" />
                        <span className="ml-2 text-muted-foreground">Searching...</span>
                      </div>
                    ) : searchQuery.trim().length > 2 ? (
                      searchResults.length > 0 ? (
                        <div className="space-y-2">
                          {searchResults.map((product) => (
                            <motion.div
                              key={product.id}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 cursor-pointer transition-colors"
                              onClick={() => handleProductClick(product.id)}
                            >
                              <div className="w-12 h-12 relative flex-shrink-0">
                                <Image
                                  src={getProductImage(product)}
                                  alt={product.title}
                                  fill
                                  className="object-cover rounded-md"
                                />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-sm text-foreground truncate">
                                  {product.title}
                                </h4>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {getProductCategory(product)}
                                  </span>
                                  {getMetafieldValue(product, 'cas_number') && (
                                    <>
                                      <span className="text-xs text-muted-foreground">•</span>
                                      <span className="text-xs text-muted-foreground">
                                        CAS: {getMetafieldValue(product, 'cas_number')}
                                      </span>
                                    </>
                                  )}
                                </div>
                              </div>
                              
                              <Eye className="w-4 h-4 text-muted-foreground" />
                            </motion.div>
                          ))}
                          
                          <Separator className="my-3" />
                          
                          <Button
                            variant="ghost"
                            className="w-full justify-between"
                            onClick={() => {
                              setIsSearchOpen(false)
                              router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
                            }}
                          >
                            <span>View all results for "{searchQuery}"</span>
                            <ArrowRight className="w-4 h-4" />
                          </Button>
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <Search className="w-8 h-8 text-muted-foreground" />
                          </div>
                          <h3 className="font-medium text-foreground mb-2">No products found</h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Try searching with different keywords or browse our categories.
                          </p>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setIsSearchOpen(false)
                              router.push('/search')
                            }}
                          >
                            Browse All Products
                          </Button>
                        </div>
                      )
                    ) : searchQuery.trim().length > 0 ? (
                      <div className="text-center py-8">
                        <p className="text-sm text-muted-foreground">
                          Type at least 3 characters to search...
                        </p>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                          <Search className="w-8 h-8 text-primary" />
                        </div>
                        <h3 className="font-medium text-foreground mb-2">Search our products</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Find chemicals by name, CAS number, molecular formula, or category.
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => setSearchQuery("sodium")}>
                            sodium
                          </Badge>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => setSearchQuery("acid")}>
                            acid
                          </Badge>
                          <Badge variant="secondary" className="cursor-pointer" onClick={() => setSearchQuery("oxide")}>
                            oxide
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}