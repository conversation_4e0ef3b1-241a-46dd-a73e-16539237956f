"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Target, Award, Users, TrendingUp, CheckCircle, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import AnimatedBackground from "@/components/animated-background"

const missionStats = [
  {
    icon: Target,
    value: "99.9%",
    label: "Product Purity",
    description: "Highest quality standards"
  },
  {
    icon: Award,
    value: "ISO",
    label: "Certified Quality",
    description: "International compliance"
  },
  {
    icon: Users,
    value: "500+",
    label: "Satisfied Clients",
    description: "Trusted partnerships"
  },
  {
    icon: TrendingUp,
    value: "28+",
    label: "Years Experience",
    description: "Industry expertise"
  }
]

const missionPillars = [
  {
    icon: CheckCircle,
    title: "Quality Assurance",
    description: "Every product undergoes rigorous testing to ensure the highest purity and consistency standards."
  },
  {
    icon: CheckCircle,
    title: "Technical Expertise",
    description: "Our team of experts provides comprehensive support and guidance for all your chemical needs."
  },
  {
    icon: CheckCircle,
    title: "Customer Partnership",
    description: "We build long-term relationships based on trust, reliability, and exceptional service delivery."
  },
  {
    icon: CheckCircle,
    title: "Industry Standards",
    description: "All products meet or exceed the most stringent international quality and safety standards."
  }
]

export default function MissionSection() {
  return (
    <section className="py-20 md:py-28 relative overflow-hidden">
      {/* Animated Background */}
      <AnimatedBackground />
      
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/10" />
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Target className="h-4 w-4" />
            Our Mission
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Delivering Excellence
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            At Benzochem Industries, we are dedicated to providing the highest quality chemical products for
            industrial, pharmaceutical, and research applications.
          </p>
        </motion.div>

        {/* Mission Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {missionStats.map((stat, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Content Side */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl md:text-3xl font-semibold mb-6 text-foreground">
                Our Commitment to Excellence
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                Our commitment to excellence, innovation, and sustainability drives everything we do. We believe in 
                fostering long-term partnerships with our clients by delivering consistent quality, technical expertise, 
                and exceptional service.
              </p>
              <p className="text-muted-foreground leading-relaxed mb-8">
                Our team of experts works tirelessly to ensure that every product meets the most stringent industry 
                standards, providing you with the confidence and reliability you need for your critical applications.
              </p>
            </div>

            {/* Mission Pillars */}
            <div className="space-y-4">
              {missionPillars.map((pillar, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.4 + index * 0.1 }}
                  className="flex items-start gap-4 p-4 rounded-lg hover:bg-accent/50 transition-colors duration-300"
                >
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <pillar.icon className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">{pillar.title}</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">{pillar.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 pt-4"
            >
              <Button className="group">
                Our Certifications
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button variant="outline">
                Quality Policy
              </Button>
            </motion.div>
          </motion.div>

          {/* Image Side */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="relative"
          >
            <Card className="overflow-hidden border-0 shadow-xl">
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image 
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158" 
                  alt="Benzochem Laboratory" 
                  fill 
                  className="object-cover transition-transform duration-700 hover:scale-105" 
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                
                {/* Floating Badge */}
                <div className="absolute top-6 left-6">
                  <Badge className="bg-background/90 backdrop-blur-sm text-foreground border-0">
                    State-of-the-art Facility
                  </Badge>
                </div>
              </div>
            </Card>

            {/* Floating Stats Card */}
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              whileInView={{ opacity: 1, y: 0, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="absolute -bottom-6 -right-6 bg-card/90 backdrop-blur-sm border rounded-lg p-6 shadow-lg"
            >
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">2005</div>
                <div className="text-sm text-muted-foreground">Established</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Mission Statement Card */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-semibold mb-6 text-foreground">
                Our Mission Statement
              </h3>
              <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed italic">
                "To be the leading provider of premium chemical solutions, delivering uncompromising quality, 
                innovative products, and exceptional service that empowers our clients to achieve their goals 
                while contributing to a sustainable future."
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}