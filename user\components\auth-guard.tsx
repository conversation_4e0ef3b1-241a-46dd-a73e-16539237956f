"use client"

import { useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useRouter, usePathname } from 'next/navigation'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  allowedStatuses?: Array<'pending' | 'approved' | 'rejected' | 'suspended'>
  redirectTo?: string
}

export default function AuthGuard({ 
  children, 
  requireAuth = false, 
  allowedStatuses = ['approved'],
  redirectTo 
}: AuthGuardProps) {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (isLoading) return

    // If auth is required but user is not logged in
    if (requireAuth && !user) {
      router.push('/login')
      return
    }

    // If user is logged in, check their status
    if (user) {
      const userStatus = user.status

      // Handle different user statuses
      if (userStatus === 'pending') {
        // Redirect pending users to waiting list (unless they're already there)
        if (pathname !== '/waiting-list') {
          router.push('/waiting-list')
          return
        }
      } else if (userStatus === 'approved') {
        // Approved users can access most pages
        if (pathname === '/waiting-list') {
          router.push('/account')
          return
        }
      } else if (userStatus === 'rejected' || userStatus === 'suspended') {
        // Rejected/suspended users should stay on waiting list or go to contact
        if (pathname !== '/waiting-list' && pathname !== '/contact') {
          router.push('/waiting-list')
          return
        }
      }

      // Check if user's status is allowed for this component
      if (!allowedStatuses.includes(userStatus)) {
        if (redirectTo) {
          router.push(redirectTo)
        } else if (userStatus === 'pending') {
          router.push('/waiting-list')
        } else if (userStatus === 'rejected' || userStatus === 'suspended') {
          router.push('/waiting-list')
        }
        return
      }
    }
  }, [user, isLoading, requireAuth, allowedStatuses, redirectTo, router, pathname])

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // If auth is required but user is not logged in, don't render children
  if (requireAuth && !user) {
    return null
  }

  // If user status is not allowed, don't render children
  if (user && !allowedStatuses.includes(user.status)) {
    return null
  }

  return <>{children}</>
}