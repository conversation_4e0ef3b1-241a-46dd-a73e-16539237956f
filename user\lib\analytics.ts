// Analytics tracking service for real-time visitor data
// Uses secure session management without localStorage/sessionStorage
import { isCookieAllowed } from '@/hooks/use-cookie-consent';

interface VisitorData {
  sessionId: string;
  page: string;
  referrer?: string;
  country?: string;
  city?: string;
  latitude?: number;
  longitude?: number;
}

interface LocationData {
  country: string;
  city: string;
  latitude: number;
  longitude: number;
}

// Secure session ID storage using HTTP cookies
class AnalyticsSessionStorage {
  private static SESSION_COOKIE_NAME = 'analytics_session_id'
  private static SESSION_MAX_AGE = 24 * 60 * 60 // 24 hours in seconds

  static getSessionId(): string | null {
    if (typeof window === 'undefined') return null
    
    try {
      const nameEQ = this.SESSION_COOKIE_NAME + "="
      const ca = document.cookie.split(';')
      
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i]
        while (c.charAt(0) === ' ') c = c.substring(1, c.length)
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
      }
      
      return null
    } catch (error) {
      console.error('Error getting analytics session ID:', error)
      return null
    }
  }

  static setSessionId(sessionId: string): void {
    if (typeof window === 'undefined') return
    
    try {
      const expires = new Date(Date.now() + this.SESSION_MAX_AGE * 1000).toUTCString()
      // Set cookie with proper security attributes
      document.cookie = `${this.SESSION_COOKIE_NAME}=${sessionId}; expires=${expires}; path=/; SameSite=Strict; Secure=${location.protocol === 'https:'}`
    } catch (error) {
      console.error('Error setting analytics session ID:', error)
    }
  }

  static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  static clearSessionId(): void {
    if (typeof window === 'undefined') return
    
    try {
      document.cookie = `${this.SESSION_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict`
    } catch (error) {
      console.error('Error clearing analytics session ID:', error)
    }
  }
}

class AnalyticsService {
  private sessionId: string | null = null;
  private isInitialized = false;
  private adminApiUrl: string;
  private apiKey: string;
  private locationData: LocationData | null = null;

  constructor() {
    this.adminApiUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
  }

  // Initialize analytics service
  async initialize(): Promise<void> {
    if (this.isInitialized || typeof window === 'undefined') return;

    try {
      // Generate or retrieve session ID from secure cookies
      this.sessionId = this.getOrCreateSessionId();
      
      // Get user location (with consent)
      if (isCookieAllowed('analytics')) {
        await this.getUserLocation();
      }

      this.isInitialized = true;
      console.log('🔍 Analytics service initialized');
    } catch (error) {
      console.error('Failed to initialize analytics:', error);
    }
  }

  // Generate or retrieve session ID from secure cookies (not sessionStorage)
  private getOrCreateSessionId(): string {
    if (typeof window === 'undefined') return '';

    try {
      let sessionId = AnalyticsSessionStorage.getSessionId();
      if (!sessionId) {
        sessionId = AnalyticsSessionStorage.generateSessionId();
        AnalyticsSessionStorage.setSessionId(sessionId);
      }
      return sessionId;
    } catch {
      // Fallback if cookies are not available
      return AnalyticsSessionStorage.generateSessionId();
    }
  }

  // Get user location using browser geolocation API
  private async getUserLocation(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      // Try to get precise location with user permission
      if (navigator.geolocation) {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            timeout: 5000,
            enableHighAccuracy: false,
          });
        });

        // Get location details from coordinates
        const { latitude, longitude } = position.coords;
        const locationDetails = await this.getLocationFromCoordinates(latitude, longitude);
        
        this.locationData = {
          latitude,
          longitude,
          country: locationDetails.country,
          city: locationDetails.city,
        };
      }
    } catch (error) {
      // Fallback to IP-based location (handled by admin API)
      console.log('Geolocation not available, using IP-based location');
    }
  }

  // Get location details from coordinates using reverse geocoding
  private async getLocationFromCoordinates(lat: number, lng: number): Promise<{ country: string; city: string }> {
    try {
      // Use a free geocoding service
      const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`);
      const data = await response.json();
      
      return {
        country: data.countryName || 'Unknown',
        city: data.city || data.locality || 'Unknown',
      };
    } catch (error) {
      console.warn('Failed to get location from coordinates:', error);
      return { country: 'Unknown', city: 'Unknown' };
    }
  }

  // Track page view
  async trackPageView(page: string, referrer?: string): Promise<void> {
    if (!this.isInitialized || !this.sessionId) {
      await this.initialize();
    }

    if (!this.sessionId) {
      console.warn('No session ID available for tracking');
      return;
    }

    // Basic visitor counting is allowed without explicit consent (essential functionality)
    // Only detailed analytics require consent
    const hasAnalyticsConsent = isCookieAllowed('analytics');

    try {
      const visitorData: VisitorData = {
        sessionId: this.sessionId,
        page,
        referrer: referrer || document.referrer || undefined,
        ...this.locationData,
      };

      const response = await fetch(`${this.adminApiUrl}/api/v1/analytics/track-visitor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(visitorData),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        console.log('📊 Page view tracked:', page);
      } else {
        console.warn('Failed to track page view:', result.error);
      }
    } catch (error) {
      console.error('Analytics tracking error:', error);
      // Don't throw error to prevent breaking the user experience
    }
  }

  // Track custom events
  async trackEvent(eventName: string, properties?: Record<string, any>): Promise<void> {
    if (!isCookieAllowed('analytics')) {
      console.log('🍪 Event tracking blocked by cookie consent');
      return;
    }

    console.log('📊 Event tracked:', eventName, properties);
    // For now, just log events. Can be extended to send to admin API
  }

  // Get session ID for other services
  getSessionId(): string | null {
    return this.sessionId;
  }

  // Check if analytics is enabled
  isEnabled(): boolean {
    return isCookieAllowed('analytics') && this.isInitialized;
  }

  // Clear analytics session (for privacy compliance)
  clearSession(): void {
    AnalyticsSessionStorage.clearSessionId();
    this.sessionId = null;
    this.isInitialized = false;
    this.locationData = null;
  }
}

// Export singleton instance
export const analytics = new AnalyticsService();

// Convenience functions
export const trackPageView = (page: string, referrer?: string) => analytics.trackPageView(page, referrer);
export const trackEvent = (eventName: string, properties?: Record<string, any>) => analytics.trackEvent(eventName, properties);
export const initializeAnalytics = () => analytics.initialize();
export const getSessionId = () => analytics.getSessionId();
export const isAnalyticsEnabled = () => analytics.isEnabled();
export const clearAnalyticsSession = () => analytics.clearSession();