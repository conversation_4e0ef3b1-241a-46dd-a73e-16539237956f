// Enhanced search client with real-time data only
import type { Product } from './types';

export interface SearchResult {
  products: Product[];
  error?: string;
  isFromCache?: boolean;
}

class EnhancedSearchClient {
  private baseUrl: string;
  private apiKey: string;
  private cache: Map<string, { products: Product[]; timestamp: number }> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
  }

  private isApiConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_api_key_here');
  }

  private getCachedResults(query: string): Product[] | null {
    const cached = this.cache.get(query.toLowerCase());
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.products;
    }
    return null;
  }

  private setCachedResults(query: string, products: Product[]): void {
    this.cache.set(query.toLowerCase(), {
      products,
      timestamp: Date.now()
    });
  }

  async searchProducts(query: string): Promise<SearchResult> {
    if (!query || query.trim().length === 0) {
      return { products: [] };
    }

    const trimmedQuery = query.trim();
    
    // Check cache first
    const cachedResults = this.getCachedResults(trimmedQuery);
    if (cachedResults) {
      console.log('Returning cached search results for:', trimmedQuery);
      return { products: cachedResults, isFromCache: true };
    }

    // If API is not configured, return empty results
    if (!this.isApiConfigured()) {
      console.warn('API not configured, cannot search products');
      return { 
        products: [], 
        error: 'Search service not configured. Please contact support.' 
      };
    }

    try {
      console.log('Searching API for:', trimmedQuery);
      
      // Try to search via user API endpoint first
      const userApiResponse = await fetch(`/api/products?search=${encodeURIComponent(trimmedQuery)}&limit=20`);
      
      if (userApiResponse.ok) {
        const result = await userApiResponse.json();
        if (result.success && result.data && Array.isArray(result.data)) {
          const products = result.data.map(this.transformApiProduct);
          this.setCachedResults(trimmedQuery, products);
          console.log('Search successful via user API:', products.length, 'results');
          return { products };
        }
      }

      // If user API fails, try direct admin API
      console.log('User API failed, trying direct admin API...');
      const directResponse = await fetch(`${this.baseUrl}/api/v1/products?search=${encodeURIComponent(trimmedQuery)}&limit=20`, {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
        },
      });

      if (directResponse.ok) {
        const result = await directResponse.json();
        if (result.success && result.data && Array.isArray(result.data)) {
          const products = result.data.map(this.transformApiProduct);
          this.setCachedResults(trimmedQuery, products);
          console.log('Search successful via direct API:', products.length, 'results');
          return { products };
        }
      }

      // If both APIs fail, return empty results with error
      console.warn('Both APIs failed, no search results available');
      return { 
        products: [], 
        error: 'Search service temporarily unavailable. Please try again later.'
      };

    } catch (error) {
      console.error('Search error:', error);
      
      // Return empty results with error message
      return { 
        products: [], 
        error: error instanceof Error ? error.message : 'Search failed. Please try again.'
      };
    }
  }

  private transformApiProduct(apiProduct: any): Product {
    return {
      id: apiProduct.id,
      title: apiProduct.title,
      description: apiProduct.description,
      descriptionHtml: apiProduct.description,
      tags: apiProduct.tags || [],
      quantity: apiProduct.quantity,
      collections: {
        edges: (apiProduct.collections || []).map((collection: string) => ({
          node: { title: collection }
        }))
      },
      images: {
        edges: (apiProduct.images || []).map((image: any) => ({
          node: { url: image.url || image }
        }))
      },
      media: {
        edges: (apiProduct.images || []).map((image: any, index: number) => ({
          node: {
            id: `media_${index}`,
            image: { url: image.url || image }
          }
        }))
      },
      priceRange: {
        minVariantPrice: {
          amount: apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        },
        maxVariantPrice: {
          amount: apiProduct.priceRange?.maxVariantPrice?.amount || apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
          currencyCode: apiProduct.priceRange?.maxVariantPrice?.currencyCode || apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
        }
      },
      compareAtPriceRange: {
        minVariantPrice: { amount: '0.00', currencyCode: 'USD' },
        maxVariantPrice: { amount: '0.00', currencyCode: 'USD' }
      },
      metafields: [
        ...(apiProduct.purity ? [{
          id: 'purity',
          key: 'purity',
          namespace: 'chemical',
          value: apiProduct.purity,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.casNumber ? [{
          id: 'cas_number',
          key: 'cas_number',
          namespace: 'chemical',
          value: apiProduct.casNumber,
          type: 'single_line_text_field'
        }] : []),
        ...(apiProduct.packaging ? [{
          id: 'package_size',
          key: 'package_size',
          namespace: 'chemical',
          value: apiProduct.packaging,
          type: 'single_line_text_field'
        }] : [])
      ]
    };
  }
}

// Export singleton instance
export const enhancedSearchClient = new EnhancedSearchClient();

// Export convenience function
export async function searchProductsEnhanced(query: string): Promise<Product[]> {
  const result = await enhancedSearchClient.searchProducts(query);
  return result.products;
}