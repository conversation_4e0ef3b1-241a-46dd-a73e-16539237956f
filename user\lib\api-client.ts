// Import the Product type from types.ts to maintain consistency
import type { Product } from './types';

// API client for communicating with the admin backend
class ApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
    
    // Validate configuration
    if (!this.apiKey || this.apiKey === 'your_api_key_here') {
      console.warn('⚠️ API key not configured. Please set NEXT_PUBLIC_API_KEY in .env.local');
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      // Check if API key is configured
      if (!this.apiKey || this.apiKey === 'your_api_key_here') {
        return {
          success: false,
          error: 'API key not configured. Please set NEXT_PUBLIC_API_KEY in .env.local',
        };
      }

      const url = `${this.baseUrl}/api/v1${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          ...options.headers,
        },
      });

      // Check if response is ok first
      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorResult = await response.json();
          errorMessage = errorResult.error || errorMessage;
        } catch {
          // If JSON parsing fails, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      // Try to parse JSON response
      let result;
      try {
        const responseText = await response.text();
        if (!responseText.trim()) {
          throw new Error('Empty response from server');
        }
        
        // Try to fix common JSON formatting issues
        let fixedJson = responseText;
        
        // Fix missing commas between JSON properties (common issue we're seeing)
        fixedJson = fixedJson.replace(/(":\s*(?:true|false|null|\d+|"[^"]*"))\s*(")/g, '$1,$2');
        fixedJson = fixedJson.replace(/(":\s*\{[^}]*\})\s*(")/g, '$1,$2');
        fixedJson = fixedJson.replace(/(":\s*\[[^\]]*\])\s*(")/g, '$1,$2');
        
        try {
          result = JSON.parse(fixedJson);
        } catch (secondParseError) {
          // If fixing didn't work, try the original
          result = JSON.parse(responseText);
        }
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Response text preview:', responseText?.substring(0, 200) + '...');
        throw new Error('Invalid JSON response from server');
      }

      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      
      // Provide more helpful error messages
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch') || error.name === 'TypeError') {
          errorMessage = 'Cannot connect to admin API. Make sure admin project is running on port 3001';
        } else if (error.message.includes('JSON')) {
          errorMessage = 'Server returned invalid response format';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  // Products API
  async getProducts(params: {
    limit?: number;
    offset?: number;
    search?: string;
    collection?: string;
    featured?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.collection) searchParams.set('collection', params.collection);
    if (params.featured) searchParams.set('featured', 'true');
    
    const query = searchParams.toString();
    const endpoint = `/products${query ? `?${query}` : ''}`;
    
    return this.makeRequest(endpoint);
  }

  async getProductById(productId: string) {
    return this.makeRequest(`/products/${productId}`);
  }

  // Collections API
  async getCollections(params: {
    limit?: number;
    offset?: number;
    search?: string;
    visible?: boolean;
  } = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.search) searchParams.set('search', params.search);
    if (params.visible !== undefined) searchParams.set('visible', params.visible.toString());
    
    const query = searchParams.toString();
    const endpoint = `/collections${query ? `?${query}` : ''}`;
    
    return this.makeRequest(endpoint);
  }

  async getCollectionById(collectionId: string) {
    return this.makeRequest(`/collections/${collectionId}`);
  }

  // Users API (we'll need to create this endpoint in admin)
  async createUser(userData: {
    userId: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    businessName?: string;
    gstNumber?: string;
    legalNameOfBusiness?: string;
    tradeName?: string;
    dateOfRegistration?: string;
    constitutionOfBusiness?: string;
    taxpayerType?: string;
    principalPlaceOfBusiness?: string;
    natureOfCoreBusinessActivity?: string;
    gstStatus?: string;
    agreedToEmailMarketing?: boolean;
    agreedToSmsMarketing?: boolean;
  }) {
    return this.makeRequest('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getUserByEmail(email: string) {
    return this.makeRequest(`/users?email=${encodeURIComponent(email)}`);
  }

  async updateUserLogin(userId: string) {
    return this.makeRequest(`/users/${userId}/login`, {
      method: 'PATCH',
    });
  }

  // Quotations API (we'll need to create this endpoint in admin)
  async createQuotation(quotationData: {
    userId: string;
    userEmail: string;
    userName: string;
    userPhone?: string;
    businessName?: string;
    products: Array<{
      productId: string;
      productName: string;
      quantity: string;
      unit: string;
      specifications?: string;
    }>;
    additionalRequirements?: string;
    deliveryLocation?: string;
    urgency?: 'standard' | 'urgent' | 'asap';
  }) {
    return this.makeRequest('/quotations', {
      method: 'POST',
      body: JSON.stringify(quotationData),
    });
  }

  async getUserQuotations(userId: string, params: {
    limit?: number;
    offset?: number;
    status?: string;
  } = {}) {
    const searchParams = new URLSearchParams();
    searchParams.set('userId', userId);
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    if (params.status) searchParams.set('status', params.status);
    
    const query = searchParams.toString();
    return this.makeRequest(`/quotations?${query}`);
  }

  async getQuotation(quotationId: string) {
    return this.makeRequest(`/quotations/${quotationId}`);
  }

  async updateQuotationStatus(quotationId: string, data: {
    status: string;
    adminResponse?: {
      quotedBy?: string;
      totalAmount?: string;
      validUntil?: string;
      terms?: string;
      notes?: string;
    };
  }) {
    return this.makeRequest(`/quotations/${quotationId}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async acceptQuotation(quotationId: string) {
    return this.updateQuotationStatus(quotationId, { status: 'accepted' });
  }

  async rejectQuotation(quotationId: string, reason?: string) {
    return this.updateQuotationStatus(quotationId, { 
      status: 'rejected',
      adminResponse: {
        notes: reason
      }
    });
  }

  // GST Verification (production implementation)
  async verifyGST(gstNumber: string): Promise<{ success: boolean; error?: string; data?: any }> {
    // Simple validation pattern for Indian GST numbers
    const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

    if (!gstPattern.test(gstNumber)) {
      return {
        success: false,
        error: "Invalid GST number format",
      };
    }

    try {
      // TODO: Implement real GST verification API
      // This should connect to actual GST verification service
      // For now, return basic validation
      return {
        success: true,
        data: {
          valid: true,
          businessName: "Trade Name (To be fetched from GST API)",
          address: "Business Address (To be fetched from GST API)",
        }
      };
    } catch (error) {
      return {
        success: false,
        error: "GST verification service unavailable",
      };
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Transform API product data to match the expected Product interface
export function transformApiProductToProduct(apiProduct: any): Product {
  return {
    id: apiProduct.id,
    title: apiProduct.title,
    description: apiProduct.description,
    descriptionHtml: apiProduct.description, // Use description as HTML for now
    tags: apiProduct.tags || [],
    quantity: apiProduct.quantity, // Include the quantity field from admin
    collections: {
      edges: (apiProduct.collections || []).map((collection: string) => ({
        node: { title: collection }
      }))
    },
    images: {
      edges: (apiProduct.images || []).map((image: any) => ({
        node: { url: image.url || image }
      }))
    },
    media: {
      edges: (apiProduct.images || []).map((image: any, index: number) => ({
        node: {
          id: `media_${index}`,
          image: { url: image.url || image }
        }
      }))
    },
    priceRange: {
      minVariantPrice: {
        amount: apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
        currencyCode: apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
      },
      maxVariantPrice: {
        amount: apiProduct.priceRange?.maxVariantPrice?.amount || apiProduct.priceRange?.minVariantPrice?.amount || '0.00',
        currencyCode: apiProduct.priceRange?.maxVariantPrice?.currencyCode || apiProduct.priceRange?.minVariantPrice?.currencyCode || 'USD'
      }
    },
    compareAtPriceRange: {
      minVariantPrice: {
        amount: '0.00',
        currencyCode: 'USD'
      },
      maxVariantPrice: {
        amount: '0.00',
        currencyCode: 'USD'
      }
    },
    metafields: [
      // Transform chemical-specific fields to metafields
      ...(apiProduct.purity ? [{
        id: 'purity',
        key: 'purity',
        namespace: 'chemical',
        value: apiProduct.purity,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.casNumber ? [{
        id: 'cas_number',
        key: 'cas_number',
        namespace: 'chemical',
        value: apiProduct.casNumber,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.molecularFormula ? [{
        id: 'molecular_formula',
        key: 'molecular_formula',
        namespace: 'chemical',
        value: apiProduct.molecularFormula,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.molecularWeight ? [{
        id: 'molecular_weight',
        key: 'molecular_weight',
        namespace: 'chemical',
        value: apiProduct.molecularWeight,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.appearance ? [{
        id: 'appearance',
        key: 'appearance',
        namespace: 'chemical',
        value: apiProduct.appearance,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.solubility ? [{
        id: 'solubility',
        key: 'solubility',
        namespace: 'chemical',
        value: apiProduct.solubility,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.phValue ? [{
        id: 'ph_value',
        key: 'ph_value',
        namespace: 'chemical',
        value: apiProduct.phValue,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.hsnNumber ? [{
        id: 'hsn_number',
        key: 'hsn_number',
        namespace: 'chemical',
        value: apiProduct.hsnNumber,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.chemicalName ? [{
        id: 'chemical_name',
        key: 'chemical_name',
        namespace: 'chemical',
        value: apiProduct.chemicalName,
        type: 'single_line_text_field'
      }] : []),
      ...(apiProduct.packaging ? [{
        id: 'packaging',
        key: 'packaging',
        namespace: 'chemical',
        value: JSON.stringify(apiProduct.packaging),
        type: 'json'
      }] : []),
      ...(apiProduct.features ? [{
        id: 'features',
        key: 'features',
        namespace: 'chemical',
        value: JSON.stringify(apiProduct.features),
        type: 'json'
      }] : []),
      ...(apiProduct.applications ? [{
        id: 'applications',
        key: 'applications',
        namespace: 'chemical',
        value: JSON.stringify(apiProduct.applications),
        type: 'json'
      }] : []),
      ...(apiProduct.applicationDetails ? [{
        id: 'application_details',
        key: 'application_details',
        namespace: 'chemical',
        value: JSON.stringify(apiProduct.applicationDetails),
        type: 'json'
      }] : [])
    ]
  }
}

// Export convenience functions that match the api.ts interface
export async function getFeaturedProducts(): Promise<Product[]> {
  try {
    const response = await apiClient.getProducts({ featured: true, limit: 8 });
    if (response.success && response.data && Array.isArray(response.data)) {
      return (response.data as ApiProduct[]).map(transformApiProductToProduct);
    }
    
    // If API fails, log error and return empty array
    if (!response.success) {
      console.warn('API request failed:', response.error);
    }
    
    return [];
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

export async function searchProducts(query: string): Promise<Product[]> {
  try {
    const response = await apiClient.getProducts({ search: query, limit: 20 });
    if (response.success && response.data && Array.isArray(response.data)) {
      return (response.data as ApiProduct[]).map(transformApiProductToProduct);
    }
    return [];
  } catch (error) {
    console.error('Error searching products:', error);
    return [];
  }
}

export async function getProductsByCollection(collectionTitle: string): Promise<Product[]> {
  try {
    console.log(`Fetching products for collection: "${collectionTitle}"`);
    const response = await apiClient.getProducts({ collection: collectionTitle, limit: 100 });
    console.log(`API response for collection "${collectionTitle}":`, {
      success: response.success,
      dataLength: response.data ? (Array.isArray(response.data) ? response.data.length : 'not array') : 'no data',
      error: response.error
    });
    
    if (response.success && response.data && Array.isArray(response.data)) {
      const products = (response.data as ApiProduct[]).map(transformApiProductToProduct);
      console.log(`Transformed ${products.length} products for collection "${collectionTitle}"`);
      return products;
    }
    
    console.warn(`No products found for collection "${collectionTitle}"`);
    return [];
  } catch (error) {
    console.error(`Error fetching products for collection ${collectionTitle}:`, error);
    return [];
  }
}

export async function getProductById(id: string): Promise<Product | null> {
  try {
    const response = await apiClient.getProductById(id);
    if (response.success && response.data) {
      return transformApiProductToProduct(response.data as ApiProduct);
    }
    return null;
  } catch (error) {
    console.error(`Error fetching product ${id}:`, error);
    return null;
  }
}

// Export types for better TypeScript support
export interface ApiProduct {
  id: string;
  title: string;
  description: string;
  tags: string[];
  quantity?: number; // Available quantity for sale
  collections: string[]; // Now contains collection titles, not IDs
  images: Array<{
    url: string;
    altText?: string;
  }>;
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: string;
    };
    maxVariantPrice: {
      amount: string;
      currencyCode: string;
    };
  };
  // Chemical-specific fields
  purity?: string;
  packaging?: string;
  casNumber?: string;
  hsnNumber?: string;
  molecularFormula?: string;
  molecularWeight?: string;
  appearance?: string;
  solubility?: string;
  phValue?: string;
  chemicalName?: string;
  features?: string[];
  applications?: string[];
  applicationDetails?: string[];
  status: string;
  featured: boolean;
  totalInventory?: number;
  createdAt: number;
  updatedAt: number;
}

export interface Collection {
  id: string;
  title: string;
  description?: string;
  handle: string;
  image?: {
    url: string;
    altText?: string;
  };
  seoTitle?: string;
  seoDescription?: string;
  status: string;
  sortOrder?: number;
  isVisible: boolean;
  productCount?: number;
  createdAt: number;
  updatedAt: number;
}

export interface Quotation {
  _id: string;
  userId: string;
  userEmail: string;
  userName: string;
  userPhone?: string;
  businessName?: string;
  products: Array<{
    productId: string;
    productName: string;
    quantity: string;
    unit: string;
    specifications?: string;
  }>;
  additionalRequirements?: string;
  deliveryLocation?: string;
  urgency: 'standard' | 'urgent' | 'asap';
  status: 'pending' | 'processing' | 'quoted' | 'accepted' | 'rejected' | 'expired';
  adminResponse?: {
    quotedBy: string;
    quotedAt: number;
    totalAmount?: string;
    validUntil?: number;
    terms?: string;
    notes?: string;
  };
  createdAt: number;
  updatedAt: number;
}