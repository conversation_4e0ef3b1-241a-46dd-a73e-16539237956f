import * as React from "react"
import { cn } from "@/lib/utils"

export interface ScrollableProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "thin" | "none"
  orientation?: "vertical" | "horizontal" | "both"
}

const Scrollable = React.forwardRef<HTMLDivElement, ScrollableProps>(
  ({ className, variant = "default", orientation = "both", ...props }, ref) => {
    const scrollbarClass = variant === "thin" ? "thin-scrollbar" : variant === "none" ? "" : "custom-scrollbar"
    
    const overflowClass = 
      orientation === "vertical" ? "overflow-y-auto overflow-x-hidden" :
      orientation === "horizontal" ? "overflow-x-auto overflow-y-hidden" :
      "overflow-auto"

    return (
      <div
        ref={ref}
        className={cn(overflowClass, scrollbarClass, className)}
        {...props}
      />
    )
  }
)
Scrollable.displayName = "Scrollable"

export { Scrollable }