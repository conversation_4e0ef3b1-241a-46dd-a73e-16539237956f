"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { analytics, trackPageView } from '@/lib/analytics';
import { useCookieConsent } from '@/hooks/use-cookie-consent';
import '@/lib/cookie-cleanup'; // Import cleanup utility

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  const pathname = usePathname();
  const { canUseAnalytics, hasConsent } = useCookieConsent();

  // Initialize analytics when consent is available
  useEffect(() => {
    if (hasConsent) {
      analytics.initialize();
    }
  }, [hasConsent]);

  // Track page views when pathname changes and analytics is enabled
  useEffect(() => {
    if (canUseAnalytics && pathname) {
      // Small delay to ensure page is loaded
      const timer = setTimeout(() => {
        trackPageView(pathname);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [pathname, canUseAnalytics]);

  // Track page visibility changes for session management
  useEffect(() => {
    if (!canUseAnalytics) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && pathname) {
        // User returned to the page, track as page view
        trackPageView(pathname);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [canUseAnalytics, pathname]);

  // Track user interactions for session activity
  useEffect(() => {
    if (!canUseAnalytics) return;

    let lastActivity = Date.now();
    
    const updateActivity = () => {
      const now = Date.now();
      // Only track if it's been more than 30 seconds since last activity
      if (now - lastActivity > 30000 && pathname) {
        trackPageView(pathname);
        lastActivity = now;
      }
    };

    const events = ['click', 'scroll', 'keypress', 'mousemove'];
    const throttledUpdate = throttle(updateActivity, 30000); // Throttle to once per 30 seconds

    events.forEach(event => {
      document.addEventListener(event, throttledUpdate, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, throttledUpdate);
      });
    };
  }, [canUseAnalytics, pathname]);

  return <>{children}</>;
}

// Throttle function to limit how often we track activity
function throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
  let inThrottle: boolean;
  return ((...args: any[]) => {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }) as T;
}