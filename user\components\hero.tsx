"use client"

import { useRef } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { Button } from "@/components/ui/button"
import { ChevronRight, Sparkles } from "lucide-react"
import MolecularBackground from "@/components/molecular-background"
import { useRouter } from "next/navigation"

export default function Hero() {
  const ref = useRef(null)
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  })

  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0])
  const scale = useTransform(scrollYProgress, [0, 1], [1, 1.1])

  const router = useRouter();

  return (
    <motion.div
      ref={ref}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-vanilla-gradient dark:bg-dark-gradient"
      style={{ opacity, scale }}
    >
      <div className="absolute inset-0 z-0">
        <MolecularBackground />
      </div>
      <div className="absolute inset-0 bg-gradient-to-b from-foreground/5 via-transparent to-background/20 z-10"></div>
      
      {/* Decorative elements */}
      <div className="absolute top-10 sm:top-20 left-4 sm:left-10 opacity-20">
        <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-primary animate-pulse" />
      </div>
      <div className="absolute bottom-20 sm:bottom-32 right-8 sm:right-16 opacity-20">
        <Sparkles className="w-4 h-4 sm:w-6 sm:h-6 text-primary animate-pulse delay-1000" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 z-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary text-sm font-medium mb-6"
          >
            <Sparkles className="w-4 h-4" />
            Trusted Chemical Trading Partner
          </motion.div>

          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-4 sm:mb-6 leading-tight">
            <span className="bg-gradient-to-r from-primary via-teal-600 to-primary bg-clip-text text-transparent">
              Premium Chemical Solutions
            </span>
            <br />
            <span className="text-foreground">for Modern Industry</span>
          </h1>
          
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-2 sm:px-0">
            Benzochem Industries delivers high-quality powder and liquid chemical products with precision, purity, and
            performance. Your trusted partner in chemical excellence.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 px-4 sm:px-0">
            <Button 
              onClick={() => router.push("/categories/powder")} 
              size="lg" 
              className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground shadow-warm hover:shadow-warm-lg transition-all duration-300 group"
            >
              Explore Products
              <ChevronRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
            <Button 
              onClick={() => router.push("/about")} 
              variant="outline" 
              size="lg" 
              className="w-full sm:w-auto group border-border hover:border-primary/40 hover:bg-accent/50 transition-all duration-300"
            >
              Learn More
              <ChevronRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>                                                         
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-12 sm:mt-16 grid grid-cols-3 gap-4 sm:gap-8 max-w-2xl mx-auto px-4 sm:px-0"
          >
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-primary">500+</div>
              <div className="text-xs sm:text-sm text-muted-foreground">Products</div>
            </div>
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-primary">99.9%</div>
              <div className="text-xs sm:text-sm text-muted-foreground">Purity</div>
            </div>
            <div className="text-center">
              <div className="text-xl sm:text-2xl font-bold text-primary">1000+</div>
              <div className="text-xs sm:text-sm text-muted-foreground">Satisfied Clients</div>
            </div>
          </motion.div>
        </motion.div>                                                                           
      </div>
    </motion.div>
  )
}