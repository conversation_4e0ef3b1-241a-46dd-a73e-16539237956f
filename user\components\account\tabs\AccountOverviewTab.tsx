"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
// import { Badge } from "@/components/ui/badge" // Not used
import { Loader2, Edit, Save, CheckCircle2, AlertCircle, EyeClosed, DoorClosedIcon, X } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton" // Added
import { Textarea } from "@/components/ui/textarea"
import { AddressSearchInput } from "@/components/ui/address-search-input"

interface AccountOverviewTabProps {
  isLoading: boolean;
  shopifyData: any; // Consider defining a more specific type
  user: any; // From useAuth, consider defining a more specific type
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
  formData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    phoneCountryCode: string;
    company: string;
    address1: string;
    address2: string;
    city: string;
    province: string;
    country: string;
    zip: string;
    shippingFirstName: string;
    shippingLastName: string;
    shippingPhone: string;
  };
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  handleAddressSelect?: (address: { address1: string; address2?: string; city: string; province: string; country: string; zip: string }) => void;
  setActiveTab: (tab: string) => void; // Not used in this component directly, but good to keep if parent uses it
  formatDate: (dateString: string) => string; // Not used in this component
  formatCurrency: (amount: string, currency: string) => string; // Not used in this component
  // New props for update functionality
  isUpdating?: boolean;
  updateStatus?: 'success' | 'error' | null;
  updateMessage?: string;
  modifiedFields?: Set<string>;
}

// Skeleton for the Account Overview form
function AccountOverviewSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => ( // Personal info fields
          <div key={`personal-skel-${i}`} className="space-y-2">
            <Skeleton className="h-4 w-1/3 rounded" /> {/* Label */}
            <Skeleton className="h-10 w-full rounded-md" /> {/* Input */}
          </div>
        ))}
      </div>
      <Separator />
      <div>
        <Skeleton className="h-6 w-1/2 mb-4 rounded" /> {/* "Default Shipping Address" title */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(10)].map((_, i) => ( // Address fields (increased for new fields)
            <div key={`address-skel-${i}`} className="space-y-2">
              <Skeleton className="h-4 w-1/3 rounded" /> {/* Label */}
              <Skeleton className="h-10 w-full rounded-md" /> {/* Input */}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}


export default function AccountOverviewTab({
  isLoading,
  // shopifyData, // Not directly used for rendering structure, formData is used
  // user, // Not directly used for rendering structure
  isEditing,
  setIsEditing,
  formData,
  handleInputChange,
  handleSubmit,
  handleAddressSelect,
  // setActiveTab, // Not used
  // formatDate, // Not used
  // formatCurrency, // Not used
  isUpdating = false,
  updateStatus = null,
  updateMessage = '',
  modifiedFields = new Set(),
}: AccountOverviewTabProps) {

  
  // Handle address selection from search
  const onAddressSelect = (address: { address1: string; address2?: string; city: string; province: string; country: string; zip: string }) => {
    if (handleAddressSelect) {
      handleAddressSelect(address);
    }
  };

  // Validation helper for required fields
  const getFieldValidationClass = (fieldName: string, value: string) => {
    const requiredFields = ['address1', 'city', 'province', 'country', 'zip'];
    if (requiredFields.includes(fieldName) && isEditing && !value.trim()) {
      return 'border-red-300 focus:border-red-500 focus:ring-red-500';
    }
    return '';
  };
  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Personal Information</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="flex items-center"
              disabled={isLoading} // Disable edit button while loading
            >
              {isEditing ? (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </>
              )}
            </Button>
          </div>
          <CardDescription>
            Manage your personal information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <AccountOverviewSkeleton />
          ) : (
            <>
              {/* Status Messages */}
              {updateStatus && (
                <Alert className={`mb-4 ${updateStatus === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                  {updateStatus === 'success' ? (
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className={updateStatus === 'success' ? 'text-green-800' : 'text-red-800'}>
                    {updateMessage || (updateStatus === 'success' ? 'Information updated successfully!' : 'Failed to update information.')}
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="text-sm font-medium">
                    First Name
                  </label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="lastName" className="text-sm font-medium">
                    Last Name
                  </label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={true} // Always read-only as per requirements
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="phone" className="text-sm font-medium">
                    Phone Number
                  </label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    disabled={true} // Always read-only as per requirements
                  />
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">Default Shipping Address</h3>



                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Address Lines with Search */}
                  <div className="space-y-2 md:col-span-2">
                    <label htmlFor="address1" className="text-sm font-medium">
                      Address <span className="text-red-500">*</span>
                      {isEditing && <span className="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded">🔍 Search enabled</span>}
                    </label>
                    {isEditing ? (
                      <div className="space-y-2">
                        <AddressSearchInput
                          value={formData.address1}
                          onChange={(value) => {
                            const event = { target: { id: 'address1', value } } as React.ChangeEvent<HTMLInputElement>
                            handleInputChange(event)
                          }}
                          onAddressSelect={onAddressSelect}
                          placeholder="Type to search for an address (e.g., 'Andheri', 'Koramangala') or enter manually..."
                          disabled={!isEditing}
                          className={getFieldValidationClass('address1', formData.address1)}
                        />
                        <p className="text-xs text-green-600 flex items-center">
                          <span className="mr-1">💡</span>
                          Search and select to auto-fill City, State, Country, and PIN Code below
                        </p>
                      </div>
                    ) : (
                      <Textarea
                        id="address1"
                        value={formData.address1}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        placeholder="Enter street address, building name, etc."
                        rows={2}
                        className={getFieldValidationClass('address1', formData.address1)}
                      />
                    )}
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <label htmlFor="address2" className="text-sm font-medium">
                      Apartment, suite, etc
                    </label>
                    <Textarea
                      id="address2"
                      value={formData.address2}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Apartment, suite, unit, floor, etc."
                      rows={2}
                    />
                  </div>

                  {/* City, State, Country, ZIP - Auto-filled by search */}
                  <div className="space-y-2">
                    <label htmlFor="city" className="text-sm font-medium flex items-center">
                      City <span className="text-red-500">*</span>
                      {isEditing && <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Auto-filled by search</span>}
                    </label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Enter city or use address search above"
                      className={getFieldValidationClass('city', formData.city)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="province" className="text-sm font-medium flex items-center">
                      State/Province <span className="text-red-500">*</span>
                      {isEditing && <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Auto-filled by search</span>}
                    </label>
                    <Input
                      id="province"
                      value={formData.province}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Enter state or use address search above"
                      className={getFieldValidationClass('province', formData.province)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="country" className="text-sm font-medium flex items-center">
                      Country <span className="text-red-500">*</span>
                      {isEditing && <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Auto-filled by search</span>}
                    </label>
                    <Input
                      id="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Enter country or use address search above"
                      className={getFieldValidationClass('country', formData.country)}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="zip" className="text-sm font-medium flex items-center">
                      Postal/ZIP Code <span className="text-red-500">*</span>
                      {isEditing && <span className="ml-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Auto-filled by search</span>}
                    </label>
                    <Input
                      id="zip"
                      value={formData.zip}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      placeholder="Enter PIN code or use address search above"
                      className={getFieldValidationClass('zip', formData.zip)}
                    />
                  </div>
                </div>
              </div>

              {isEditing && (
                <div className="flex justify-end pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="mr-2"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="bg-teal-600 hover:bg-teal-700 text-white"
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </Button>
                </div>
              )}
            </form>
            </>
          )}
        </CardContent>
      </Card>
    </>
  )
}