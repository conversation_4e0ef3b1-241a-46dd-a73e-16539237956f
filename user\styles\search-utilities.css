/* Search-specific utility classes */

.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) transparent;
}

.thin-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--foreground));
}

/* Search suggestion animations */
.search-suggestion-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.search-suggestion-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 200ms, transform 200ms;
}

.search-suggestion-exit {
  opacity: 1;
  transform: translateY(0);
}

.search-suggestion-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 200ms, transform 200ms;
}

/* Search input focus styles */
.search-input-focus {
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

/* Search dropdown shadow */
.search-dropdown-shadow {
  box-shadow: 
    0 10px 38px -10px rgba(22, 23, 24, 0.35),
    0 10px 20px -15px rgba(22, 23, 24, 0.2);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .search-dropdown-shadow {
    box-shadow: 
      0 10px 38px -10px rgba(0, 0, 0, 0.5),
      0 10px 20px -15px rgba(0, 0, 0, 0.3);
  }
}

/* Search result highlighting */
.search-highlight {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary-foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

/* Search loading animation */
.search-loading-pulse {
  animation: search-pulse 1.5s ease-in-out infinite;
}

@keyframes search-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Search no results state */
.search-no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: hsl(var(--muted-foreground));
}

.search-no-results-icon {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Search keyboard navigation */
.search-item-selected {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.search-item-selected .search-item-icon {
  color: hsl(var(--primary-foreground));
}

/* Search category badges */
.search-category-badge {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 0.375rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.search-category-product {
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
}

.search-category-cas {
  background-color: hsl(var(--destructive) / 0.1);
  color: hsl(var(--destructive));
}

.search-category-formula {
  background-color: hsl(var(--warning) / 0.1);
  color: hsl(var(--warning));
}

.search-category-chemical {
  background-color: hsl(var(--success) / 0.1);
  color: hsl(var(--success));
}

.search-category-tag {
  background-color: hsl(var(--info) / 0.1);
  color: hsl(var(--info));
}

.search-category-category {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
}

/* Responsive search adjustments */
@media (max-width: 640px) {
  .search-dropdown-shadow {
    box-shadow: 
      0 4px 16px -4px rgba(22, 23, 24, 0.25),
      0 4px 8px -8px rgba(22, 23, 24, 0.15);
  }
  
  .search-suggestion-item {
    padding: 0.75rem;
  }
  
  .search-suggestion-text {
    font-size: 0.875rem;
  }
  
  .search-suggestion-meta {
    font-size: 0.75rem;
  }
}