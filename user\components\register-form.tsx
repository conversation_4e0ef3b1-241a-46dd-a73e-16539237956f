"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Loader2, User, Building, Mail, Lock, Phone, FileText, AlertCircle, CheckCircle, Shield, ArrowRight } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function RegisterForm() {
  const { register, isLoading, verifyGST } = useAuth()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phone: "",
    countryCode: "+91",
    gstNumber: "",
    legalNameOfBusiness: "",
    tradeName: "",
    dateOfRegistration: "",
    constitutionOfBusiness: "",
    taxpayerType: "",
    principalPlaceOfBusiness: "",
    natureOfCoreBusinessActivity: "",
    agreedToEmailMarketing: false,
    agreedToSmsMarketing: false,
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string | null>>({})
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [currentTab, setCurrentTab] = useState("personal")
  const [personalInfoCompleted, setPersonalInfoCompleted] = useState(false)
  const [isVerifyingGST, setIsVerifyingGST] = useState(false)
  const [gstVerified, setGstVerified] = useState(false)
  const [gstVerificationMessage, setGstVerificationMessage] = useState("")

  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address"
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return "Please enter a valid email address"
    return null
  }

  const validatePassword = (password: string) => {
    if (!password) return "Please enter a password"
    if (password.length < 5) return "Password must be at least 5 characters long"
    return null
  }

  const validatePhone = (phone: string, countryCode: string) => {
    if (!phone) return "Please enter your phone number"
    if (!countryCode) return "Please select a country code"
    const fullPhone = `${countryCode}${phone}`
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    if (!phoneRegex.test(fullPhone)) {
      return "Please enter a valid phone number including country code"
    }
    return null
  }

  const validateName = (name: string, field: "firstName" | "lastName") => {
    if (!name) return `Please enter your ${field === "firstName" ? "first" : "last"} name`
    if (name.length < 2) return `${field === "firstName" ? "First" : "Last"} name must be at least 2 characters long`
    return null
  }

  const validatePersonalInfo = () => {
    const errors: Record<string, string | null> = {}
    errors.firstName = validateName(formData.firstName, "firstName")
    errors.lastName = validateName(formData.lastName, "lastName")
    errors.email = validateEmail(formData.email)
    errors.password = validatePassword(formData.password)
    errors.phone = validatePhone(formData.phone, formData.countryCode)

    setFieldErrors(errors)
    return Object.values(errors).every((err) => err === null)
  }

  const validateBusinessInfo = () => {
    const errors: Record<string, string | null> = {}
    
    // GST Number is required
    if (!formData.gstNumber) {
      errors.gstNumber = "GST Number is required"
    } else if (formData.gstNumber.length !== 15) {
      errors.gstNumber = "GST Number must be 15 characters long"
    }

    // Business details are required
    if (!formData.legalNameOfBusiness) {
      errors.legalNameOfBusiness = "Legal Name of Business is required"
    }
    
    if (!formData.tradeName) {
      errors.tradeName = "Trade Name is required"
    }
    
    if (!formData.principalPlaceOfBusiness) {
      errors.principalPlaceOfBusiness = "Business Address is required"
    }

    setFieldErrors(prev => ({ ...prev, ...errors }))
    return Object.values(errors).every((err) => err === null)
  }

  const validateAllFields = () => {
    const personalValid = validatePersonalInfo()
    const businessValid = validateBusinessInfo()
    return personalValid && businessValid
  }

  const handleVerifyGST = async () => {
    if (!formData.gstNumber) {
      setGstVerificationMessage("Please enter a GST number")
      return
    }

    setIsVerifyingGST(true)
    setGstVerificationMessage("")

    try {
      const response = await fetch('/api/verify-gst-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gstNumber: formData.gstNumber }),
      })

      const result = await response.json()

      if (result.isValid) {
        // Populate form fields with verified GST data
        setFormData(prev => ({
          ...prev,
          legalNameOfBusiness: result.legalNameOfBusiness || "",
          tradeName: result.tradeName || "",
          dateOfRegistration: result.dateOfRegistration || "",
          constitutionOfBusiness: result.constitutionOfBusiness || "",
          taxpayerType: result.taxpayerType || "",
          principalPlaceOfBusiness: result.principalPlaceOfBusiness || "",
          natureOfCoreBusinessActivity: result.natureOfCoreBusinessActivity || "",
        }))

        setGstVerified(true)
        setGstVerificationMessage("GST verified successfully! Business details have been populated automatically.")
      } else {
        setGstVerified(false)
        setGstVerificationMessage(result.message || "GST verification failed")
      }
    } catch (error) {
      console.error("GST verification failed:", error)
      setGstVerified(false)
      setGstVerificationMessage("Failed to verify GST. Please try again.")
    } finally {
      setIsVerifyingGST(false)
    }
  }

  const handleContinueToBusinessInfo = () => {
    if (validatePersonalInfo()) {
      setPersonalInfoCompleted(true)
      setCurrentTab("business")
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }))
    }
    // Clear general error when user starts typing
    if (error) {
      setError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    if (!validateAllFields()) {
      return
    }

    try {
      const result = await register({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        phone: `${formData.countryCode}${formData.phone}`,
        businessName: formData.tradeName,
        gstNumber: formData.gstNumber,
        // Business information
        legalNameOfBusiness: formData.legalNameOfBusiness,
        tradeName: formData.tradeName,
        dateOfRegistration: formData.dateOfRegistration,
        constitutionOfBusiness: formData.constitutionOfBusiness,
        taxpayerType: formData.taxpayerType,
        principalPlaceOfBusiness: formData.principalPlaceOfBusiness,
        natureOfCoreBusinessActivity: formData.natureOfCoreBusinessActivity,
        // Marketing consent
        agreedToEmailMarketing: formData.agreedToEmailMarketing,
        agreedToSmsMarketing: formData.agreedToSmsMarketing,
      })

      if (result.success) {
        setSuccess(result.message || "🎉 Account created successfully! Your account is pending approval.")
        // Clear form data
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          phone: "",
          countryCode: "+91",
          legalNameOfBusiness: "",
          tradeName: "",
          gstNumber: "",
          dateOfRegistration: "",
          constitutionOfBusiness: "",
          taxpayerType: "",
          principalPlaceOfBusiness: "",
          natureOfCoreBusinessActivity: "",
          agreedToEmailMarketing: false,
          agreedToSmsMarketing: false,
        })
        // Reset verification states
        setGstVerified(false)
        setGstVerificationMessage("")
        setPersonalInfoCompleted(false)
        setCurrentTab("personal")
        // Redirect to waiting list or specified redirect
        setTimeout(() => {
          router.push(result.redirectTo || '/waiting-list')
        }, 3000)
      } else {
        setError(result.error || "Registration failed. Please try again.")
      }
    } catch (error) {
      console.error("Registration error:", error)
      setError("An unexpected error occurred. Please try again.")
    }
  }

  return (
    <div className="w-full space-y-6">
      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            {success}
          </AlertDescription>
        </Alert>
      )}

      {/* Registration Info */}
      <Alert className="border-primary/20 bg-primary/5">
        <Shield className="h-4 w-4 text-primary" />
        <AlertDescription className="text-primary">
          <span className="font-medium">Registration Process:</span> Complete all required fields including GST verification and business details. 
          Your account will be reviewed by our admin team before activation.
        </AlertDescription>
      </Alert>

      <Tabs value={currentTab} onValueChange={(value) => {
        // Only allow switching to business tab if personal info is completed
        if (value === "business" && !personalInfoCompleted) {
          return
        }
        setCurrentTab(value)
      }} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6 h-12">
          <TabsTrigger value="personal" className="flex items-center gap-2 text-sm font-medium">
            <User className="w-4 h-4" />
            Personal Info
          </TabsTrigger>
          <TabsTrigger 
            value="business" 
            disabled={!personalInfoCompleted}
            className={`flex items-center gap-2 text-sm font-medium ${
              !personalInfoCompleted ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <Building className="w-4 h-4" />
            Business Info
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="space-y-6">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-foreground mb-2">Personal Information</h3>
              <p className="text-sm text-muted-foreground">Tell us about yourself to get started</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium flex items-center gap-2">
                  <User className="w-4 h-4 text-primary" />
                  First Name
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  className={`transition-all duration-200 ${
                    fieldErrors.firstName 
                      ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                      : "focus:border-primary focus:ring-primary/20"
                  }`}
                  placeholder="Enter your first name"
                />
                {fieldErrors.firstName && (
                  <p className="text-destructive text-sm flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {fieldErrors.firstName}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  className={`transition-all duration-200 ${
                    fieldErrors.lastName 
                      ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                      : "focus:border-primary focus:ring-primary/20"
                  }`}
                  placeholder="Enter your last name"
                />
                {fieldErrors.lastName && (
                  <p className="text-destructive text-sm flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {fieldErrors.lastName}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
                <Mail className="w-4 h-4 text-primary" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`transition-all duration-200 ${
                  fieldErrors.email 
                    ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                    : "focus:border-primary focus:ring-primary/20"
                }`}
                placeholder="Enter your email address"
              />
              {fieldErrors.email && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {fieldErrors.email}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium flex items-center gap-2">
                <Lock className="w-4 h-4 text-primary" />
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                  className={`pr-10 transition-all duration-200 ${
                    fieldErrors.password 
                      ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                      : "focus:border-primary focus:ring-primary/20"
                  }`}
                  placeholder="Create a secure password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors duration-200"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              {fieldErrors.password && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {fieldErrors.password}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm font-medium flex items-center gap-2">
                <Phone className="w-4 h-4 text-primary" />
                Phone Number
              </Label>
              <div className="flex gap-2">
                <Select value={formData.countryCode} onValueChange={(value) => handleInputChange("countryCode", value)}>
                  <SelectTrigger className="w-24 focus:border-primary focus:ring-primary/20 transition-all duration-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="+91">🇮🇳 +91</SelectItem>
                    <SelectItem value="+1">🇺🇸 +1</SelectItem>
                    <SelectItem value="+44">🇬🇧 +44</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className={`flex-1 transition-all duration-200 ${
                    fieldErrors.phone 
                      ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                      : "focus:border-primary focus:ring-primary/20"
                  }`}
                  placeholder="Enter your phone number"
                />
              </div>
              {fieldErrors.phone && (
                <p className="text-destructive text-sm flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {fieldErrors.phone}
                </p>
              )}
            </div>

            <Button
              type="button"
              onClick={handleContinueToBusinessInfo}
              className="w-full h-11 font-medium transition-all duration-200 hover:shadow-md"
            >
              Continue to Business Info
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link href="/login" className="text-primary hover:text-primary/80 font-medium transition-colors duration-200">
                  Sign in here
                </Link>
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="business" className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-foreground mb-2">Business Information</h3>
              <p className="text-sm text-muted-foreground">Complete your business details to finish registration</p>
            </div>

            {/* Back Button */}
            <div className="flex justify-start">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentTab("personal")}
                className="flex items-center gap-2"
              >
                <ArrowRight className="w-4 h-4 rotate-180" />
                Back to Personal Info
              </Button>
            </div>

            {/* GST Verification */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <FileText className="w-5 h-5 text-primary" />
                <h4 className="font-semibold text-foreground">GST Verification</h4>
              </div>

              <div className="space-y-3">
                <Label htmlFor="gstNumber" className="text-sm font-medium flex items-center gap-1">
                  GST Number <span className="text-destructive">*</span>
                </Label>
                <div className="flex gap-2">
                  <Input
                    id="gstNumber"
                    type="text"
                    value={formData.gstNumber}
                    onChange={(e) => {
                      handleInputChange("gstNumber", e.target.value)
                      // Reset verification status when GST number changes
                      if (gstVerified) {
                        setGstVerified(false)
                        setGstVerificationMessage("")
                      }
                    }}
                    className={`flex-1 transition-all duration-200 ${
                      fieldErrors.gstNumber 
                        ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                        : "focus:border-primary focus:ring-primary/20"
                    }`}
                    placeholder="Enter your 15-digit GST number"
                    disabled={gstVerified}
                  />
                  <Button
                    type="button"
                    onClick={handleVerifyGST}
                    disabled={!formData.gstNumber || isVerifyingGST}
                    variant={gstVerified ? "secondary" : "outline"}
                    className={`px-4 transition-all duration-200 ${
                      gstVerified 
                        ? 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100 dark:bg-green-950/50 dark:text-green-300 dark:border-green-800 dark:hover:bg-green-900/50' 
                        : 'hover:bg-primary/5 hover:border-primary dark:hover:bg-primary/10 dark:hover:border-primary'
                    }`}
                  >
                    {isVerifyingGST ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : gstVerified ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Verified
                      </>
                    ) : (
                      "Verify GST"
                    )}
                  </Button>
                </div>

                {fieldErrors.gstNumber && (
                  <p className="text-destructive text-sm flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {fieldErrors.gstNumber}
                  </p>
                )}

                {gstVerificationMessage && (
                  <Alert className={gstVerified 
                    ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50" 
                    : "border-destructive bg-destructive/5 dark:border-destructive dark:bg-destructive/10"
                  }>
                    {gstVerified ? (
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-destructive" />
                    )}
                    <AlertDescription className={gstVerified 
                      ? "text-green-700 dark:text-green-300" 
                      : "text-destructive"
                    }>
                      {gstVerificationMessage}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>

            {/* Business Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Building className="w-5 h-5 text-primary" />
                <h4 className="font-semibold text-foreground">Business Details</h4>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="legalNameOfBusiness" className="text-sm font-medium flex items-center gap-1">
                    Legal Name of Business <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="legalNameOfBusiness"
                    type="text"
                    value={formData.legalNameOfBusiness}
                    onChange={(e) => handleInputChange("legalNameOfBusiness", e.target.value)}
                    className={`transition-all duration-200 ${
                      fieldErrors.legalNameOfBusiness 
                        ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                        : "focus:border-primary focus:ring-primary/20"
                    } ${gstVerified ? 'bg-muted/50' : ''}`}
                    placeholder="As per GST registration"
                    disabled={gstVerified}
                  />
                  {fieldErrors.legalNameOfBusiness && (
                    <p className="text-destructive text-sm flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {fieldErrors.legalNameOfBusiness}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tradeName" className="text-sm font-medium flex items-center gap-1">
                    Trade Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="tradeName"
                    type="text"
                    value={formData.tradeName}
                    onChange={(e) => handleInputChange("tradeName", e.target.value)}
                    className={`transition-all duration-200 ${
                      fieldErrors.tradeName 
                        ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                        : "focus:border-primary focus:ring-primary/20"
                    }`}
                    placeholder="Your business brand name"
                    disabled={gstVerified}
                  />
                  {fieldErrors.tradeName && (
                    <p className="text-destructive text-sm flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {fieldErrors.tradeName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="principalPlaceOfBusiness" className="text-sm font-medium flex items-center gap-1">
                    Business Address <span className="text-destructive">*</span>
                  </Label>
                  <Textarea
                    id="principalPlaceOfBusiness"
                    value={formData.principalPlaceOfBusiness}
                    onChange={(e) => handleInputChange("principalPlaceOfBusiness", e.target.value)}
                    className={`min-h-[80px] transition-all duration-200 ${
                      fieldErrors.principalPlaceOfBusiness 
                        ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                        : "focus:border-primary focus:ring-primary/20"
                    } ${gstVerified ? 'bg-muted/50' : ''}`}
                    placeholder="Enter the complete business address"
                    disabled={gstVerified}
                  />
                  {fieldErrors.principalPlaceOfBusiness && (
                    <p className="text-destructive text-sm flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {fieldErrors.principalPlaceOfBusiness}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Marketing Preferences */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Marketing Preferences</h4>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 p-3 rounded-lg border border-border bg-card/50">
                  <Checkbox
                    id="emailMarketing"
                    checked={formData.agreedToEmailMarketing}
                    onCheckedChange={(checked) =>
                      handleInputChange("agreedToEmailMarketing", checked as boolean)
                    }
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="emailMarketing" className="text-sm font-medium cursor-pointer">
                      Email Marketing
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Receive product updates, special offers, and industry insights via email
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 rounded-lg border border-border bg-card/50">
                  <Checkbox
                    id="smsMarketing"
                    checked={formData.agreedToSmsMarketing}
                    onCheckedChange={(checked) =>
                      handleInputChange("agreedToSmsMarketing", checked as boolean)
                    }
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="smsMarketing" className="text-sm font-medium cursor-pointer">
                      SMS Marketing
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Get important notifications and time-sensitive offers via SMS
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full h-11 font-medium transition-all duration-200 hover:shadow-md"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                "Complete Registration"
              )}
            </Button>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link href="/login" className="text-primary hover:text-primary/80 font-medium transition-colors duration-200">
                  Sign in here
                </Link>
              </p>
            </div>
          </form>
        </TabsContent>
      </Tabs>
    </div>
  )
}