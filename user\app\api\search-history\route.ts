import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

const SEARCH_HISTORY_COOKIE = 'search_history'
const MAX_SEARCH_HISTORY = 10

interface SearchHistoryItem {
  query: string
  timestamp: number
  resultCount?: number
}

export async function GET() {
  try {
    const cookieStore = await cookies()
    const searchHistoryCookie = cookieStore.get(SEARCH_HISTORY_COOKIE)
    
    if (!searchHistoryCookie) {
      return NextResponse.json({ success: true, data: [] })
    }

    const searchHistory: SearchHistoryItem[] = JSON.parse(searchHistoryCookie.value)
    
    // Sort by timestamp (most recent first) and return
    const sortedHistory = searchHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, MAX_SEARCH_HISTORY)

    return NextResponse.json({ success: true, data: sortedHistory })
  } catch (error) {
    console.error('Error getting search history:', error)
    return NextResponse.json({ success: false, error: 'Failed to get search history' })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { query, resultCount } = await request.json()
    
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return NextResponse.json({ success: false, error: 'Invalid query' })
    }

    const cookieStore = await cookies()
    const searchHistoryCookie = cookieStore.get(SEARCH_HISTORY_COOKIE)
    
    let searchHistory: SearchHistoryItem[] = []
    
    if (searchHistoryCookie) {
      try {
        searchHistory = JSON.parse(searchHistoryCookie.value)
      } catch (error) {
        console.error('Error parsing search history cookie:', error)
        searchHistory = []
      }
    }

    const trimmedQuery = query.trim().toLowerCase()
    
    // Remove existing entry with same query (case-insensitive)
    searchHistory = searchHistory.filter(item => 
      item.query.toLowerCase() !== trimmedQuery
    )
    
    // Add new search to the beginning
    const newSearchItem: SearchHistoryItem = {
      query: query.trim(),
      timestamp: Date.now(),
      resultCount: resultCount || 0
    }
    
    searchHistory.unshift(newSearchItem)
    
    // Keep only the most recent searches
    searchHistory = searchHistory.slice(0, MAX_SEARCH_HISTORY)
    
    // Create response
    const response = NextResponse.json({ success: true, data: searchHistory })
    
    // Set cookie with search history (expires in 30 days)
    response.cookies.set(SEARCH_HISTORY_COOKIE, JSON.stringify(searchHistory), {
      maxAge: 30 * 24 * 60 * 60, // 30 days
      httpOnly: false, // Allow client-side access for immediate UI updates
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
    
    return response
  } catch (error) {
    console.error('Error saving search history:', error)
    return NextResponse.json({ success: false, error: 'Failed to save search history' })
  }
}

export async function DELETE() {
  try {
    const response = NextResponse.json({ success: true, message: 'Search history cleared' })
    
    // Clear the search history cookie
    response.cookies.set(SEARCH_HISTORY_COOKIE, '', {
      maxAge: 0,
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
    
    return response
  } catch (error) {
    console.error('Error clearing search history:', error)
    return NextResponse.json({ success: false, error: 'Failed to clear search history' })
  }
}