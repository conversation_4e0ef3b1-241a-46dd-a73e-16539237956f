import type { <PERSON>ada<PERSON> } from "next"
import { Shield, Eye, Lock, Database, Users, Globe, FileText, Calendar, AlertCircle, CheckCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

export const metadata: Metadata = {
  title: "Privacy Policy | Benzochem Industries",
  description: "Learn how Benzochem Industries protects your privacy and handles your personal data. Our comprehensive privacy policy and data protection practices.",
}

export default function PrivacyPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium mb-6">
              <Shield className="w-4 h-4" />
              Data Protection
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
              Privacy <span className="text-primary">Policy</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-6">
              Your privacy is important to us. This policy explains how we collect, use, and protect 
              your personal information when you use our services.
            </p>
            
            <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                Last Updated: December 15, 2024
              </div>
              <div className="flex items-center gap-1">
                <FileText className="w-4 h-4" />
                Version 3.0
              </div>
            </div>
          </div>

          {/* Privacy Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lock className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Secure Storage</h3>
              <p className="text-sm text-muted-foreground">Your data is encrypted and stored securely</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Eye className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Transparency</h3>
              <p className="text-sm text-muted-foreground">Clear information about data usage</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Your Control</h3>
              <p className="text-sm text-muted-foreground">Manage your privacy preferences</p>
            </div>
          </div>

          {/* Quick Navigation */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <h2 className="font-semibold text-card-foreground mb-4">Quick Navigation</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {[
                "1. Information We Collect",
                "2. How We Use Information",
                "3. Information Sharing",
                "4. Data Security",
                "5. Cookies and Tracking",
                "6. Your Rights",
                "7. Data Retention",
                "8. International Transfers",
                "9. Children's Privacy",
                "10. Updates to Policy",
                "11. Contact Information",
                "12. Compliance"
              ].map((item) => (
                <a
                  key={item}
                  href={`#section-${item.split('.')[0]}`}
                  className="text-sm text-primary hover:text-primary/80 transition-colors duration-200 p-2 rounded hover:bg-primary/5"
                >
                  {item}
                </a>
              ))}
            </div>
          </div>

          {/* Privacy Content */}
          <div className="bg-card border border-border rounded-xl p-8 space-y-8">
            
            {/* Section 1 */}
            <section id="section-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Database className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">1. Information We Collect</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  We collect information you provide directly to us, such as when you create an account, 
                  request a quotation, or contact us for support.
                </p>
                <div className="space-y-3">
                  <h4 className="font-semibold text-card-foreground">Personal Information:</h4>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Name, email address, and phone number</li>
                    <li>Business information and GST details</li>
                    <li>Billing and shipping addresses</li>
                    <li>Communication preferences</li>
                  </ul>
                  
                  <h4 className="font-semibold text-card-foreground">Automatically Collected Information:</h4>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>IP address and device information</li>
                    <li>Browser type and operating system</li>
                    <li>Pages visited and time spent on our site</li>
                    <li>Referral sources and search terms</li>
                  </ul>
                </div>
              </div>
            </section>

            <Separator />

            {/* Section 2 */}
            <section id="section-2">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Eye className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">2. How We Use Information</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  We use the information we collect to provide, maintain, and improve our services:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Process orders and provide customer support</li>
                  <li>Send important updates about your orders and account</li>
                  <li>Improve our website and services</li>
                  <li>Comply with legal and regulatory requirements</li>
                  <li>Prevent fraud and ensure security</li>
                </ul>
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <p className="text-blue-800 dark:text-blue-200 text-sm">
                    <strong>Marketing Communications:</strong> We only send marketing emails to users who have explicitly opted in. 
                    You can unsubscribe at any time.
                  </p>
                </div>
              </div>
            </section>

            <Separator />

            {/* Section 3 */}
            <section id="section-3">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Users className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">3. Information Sharing</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  We do not sell, trade, or rent your personal information to third parties. We may share information in these limited circumstances:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>With service providers who help us operate our business</li>
                  <li>When required by law or to protect our rights</li>
                  <li>In connection with a business transfer or merger</li>
                  <li>With your explicit consent</li>
                </ul>
                <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <p className="text-green-800 dark:text-green-200 text-sm">
                      <strong>Our Commitment:</strong> All third-party service providers are contractually bound to protect your information 
                      and use it only for the specified purposes.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <Separator />

            {/* Section 4 */}
            <section id="section-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Lock className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">4. Data Security</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  We implement appropriate technical and organizational measures to protect your personal information:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>SSL encryption for data transmission</li>
                  <li>Encrypted storage of sensitive information</li>
                  <li>Regular security audits and updates</li>
                  <li>Access controls and employee training</li>
                  <li>Incident response procedures</li>
                </ul>
                <div className="bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                      <strong>Important:</strong> While we implement strong security measures, no method of transmission 
                      over the internet is 100% secure. Please help protect your account by using a strong password.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <Separator />

            {/* Section 5 */}
            <section id="section-5">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Globe className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">5. Cookies and Tracking</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  We use cookies and similar technologies to enhance your experience on our website:
                </p>
                <div className="space-y-3">
                  <h4 className="font-semibold text-card-foreground">Essential Cookies:</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Required for basic website functionality</li>
                    <li>Remember your login status and preferences</li>
                  </ul>
                  
                  <h4 className="font-semibold text-card-foreground">Analytics Cookies:</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Help us understand how visitors use our site</li>
                    <li>Allow us to improve our services</li>
                  </ul>
                  
                  <h4 className="font-semibold text-card-foreground">Marketing Cookies:</h4>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Used to deliver relevant advertisements</li>
                    <li>Only with your explicit consent</li>
                  </ul>
                </div>
                <p>
                  You can control cookie settings through your browser preferences, our cookie consent banner, 
                  or by visiting our{' '}
                  <a href="/cookie-settings" className="text-primary hover:underline">
                    Cookie Settings
                  </a>{' '}
                  page at any time.
                </p>
              </div>
            </section>

            <Separator />

            {/* Section 6 */}
            <section id="section-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <Shield className="w-4 h-4 text-primary" />
                </div>
                <h2 className="text-xl font-bold text-card-foreground">6. Your Rights</h2>
              </div>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  You have the following rights regarding your personal information:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li><strong>Access:</strong> Request a copy of your personal data</li>
                  <li><strong>Rectification:</strong> Correct inaccurate or incomplete information</li>
                  <li><strong>Erasure:</strong> Request deletion of your personal data</li>
                  <li><strong>Portability:</strong> Receive your data in a structured format</li>
                  <li><strong>Restriction:</strong> Limit how we process your information</li>
                  <li><strong>Objection:</strong> Object to certain types of processing</li>
                </ul>
                <p>
                  To exercise these rights, please contact our privacy team using the information provided below.
                </p>
              </div>
            </section>
          </div>

          {/* Contact Information */}
          <div className="mt-12 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">Privacy Questions or Concerns?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              If you have any questions about this privacy policy or how we handle your data, 
              please don't hesitate to contact our privacy team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button>
                Contact Privacy Team
              </Button>
              <Button variant="outline" asChild>
                <a href="/cookie-settings">
                  Manage Cookie Settings
                </a>
              </Button>
            </div>
            <div className="mt-6 text-sm text-muted-foreground space-y-1">
              <p>Email: <EMAIL></p>
              <p>Phone: +****************</p>
              <p>Address: 123 Chemical Way, Industrial District, City, State 12345</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}