"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import ProductCard from "@/components/product-card"
import type { Product } from "@/lib/types"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Filter, Grid, List } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

interface FeaturedProductsClientProps {
  initialProducts: Product[]
  error: string | null
}

export default function FeaturedProductsClient({ initialProducts, error }: FeaturedProductsClientProps) {
  const [activeFilter, setActiveFilter] = useState<string>("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Get unique categories from products' collections
  const categories = Array.from(
    new Set(
      initialProducts.flatMap((product) =>
        product.collections.edges.map((edge) => edge.node.title)
      )
    )
  )

  const filteredProducts =
    activeFilter === "all"
      ? initialProducts
      : initialProducts.filter((product) =>
          product.collections.edges.some((edge) => edge.node.title === activeFilter)
        )

  if (error) {
    return (
      <Card className="border border-destructive/20 bg-destructive/5">
        <CardContent className="p-6">
          <Alert variant="destructive" className="border-0 bg-transparent">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Error loading products: {error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (initialProducts.length === 0) {
    return (
      <Card className="border border-border/20 bg-card/50 backdrop-blur-sm">
        <CardContent className="p-12 text-center">
          <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-4">
            <Grid className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">No Products Available</h3>
          <p className="text-muted-foreground">
            No products found. Please contact our customer care for assistance.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-8">
      {/* Filter Controls */}
      <div className="space-y-4">
        {/* Filter Header and View Toggle */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
            <Filter className="h-4 w-4" />
            Filter by:
          </div>
          
          {/* View Mode Toggle */}
          <div className="flex items-center gap-2 bg-muted/50 rounded-lg p-1">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="h-8 w-8 p-0"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="h-8 w-8 p-0"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={activeFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveFilter("all")}
            className="rounded-full"
          >
            All Products
            <Badge variant="secondary" className="ml-2 text-xs">
              {initialProducts.length}
            </Badge>
          </Button>

          {categories.map((category) => {
            const categoryCount = initialProducts.filter((product) =>
              product.collections.edges.some((edge) => edge.node.title === category)
            ).length

            return (
              <Button
                key={category}
                variant={activeFilter === category ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(category)}
                className="rounded-full"
              >
                {category}
                <Badge variant="secondary" className="ml-2 text-xs">
                  {categoryCount}
                </Badge>
              </Button>
            )
          })}
        </div>
      </div>

      {/* Products Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing <span className="font-medium text-foreground">{filteredProducts.length}</span> of{" "}
          <span className="font-medium text-foreground">{initialProducts.length}</span> products
        </p>
      </div>

      {/* Products Grid */}
      <motion.div
        key={`${activeFilter}-${viewMode}`}
        className={`grid gap-6 ${
          viewMode === "grid"
            ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
            : "grid-cols-1"
        }`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, staggerChildren: 0.1 }}
      >
        {filteredProducts.length > 0 ? (
          filteredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <ProductCard product={product} />
            </motion.div>
          ))
        ) : (
          <Card className="col-span-full border border-border/20 bg-card/50 backdrop-blur-sm">
            <CardContent className="p-12 text-center">
              <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-4">
                <Filter className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">No Products Found</h3>
              <p className="text-muted-foreground">
                No products found in the "{activeFilter}" category. Try selecting a different filter.
              </p>
            </CardContent>
          </Card>
        )}
      </motion.div>
    </div>
  )
}