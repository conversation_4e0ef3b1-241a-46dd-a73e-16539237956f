"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Check } from "lucide-react"

export default function Newsletter() {
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      setEmail("")
      // In a real app, you would submit to an API here
    }
  }

  return (
    <section className="bg-neutral-900 text-white py-16 md:py-24">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="max-w-2xl mx-auto text-center"
        >
          <h2 className="text-3xl font-medium mb-4">Stay Updated</h2>
          <p className="text-neutral-400 mb-8">
            Subscribe to our newsletter for the latest product updates, industry news, and exclusive offers.
          </p>

          {isSubmitted ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-teal-600/20 rounded-lg p-6 flex items-center justify-center"
            >
              <Check className="h-5 w-5 text-teal-500 mr-2" />
              <p className="text-teal-400">Thank you for subscribing to our newsletter!</p>
            </motion.div>
          ) : (
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-white/10 border-white/20 text-white placeholder:text-neutral-400 focus-visible:ring-teal-500"
              />
              <Button type="submit" className="bg-teal-600 hover:bg-teal-700 text-white">
                Subscribe
              </Button>
            </form>
          )}
        </motion.div>
      </div>
    </section>
  )
}
