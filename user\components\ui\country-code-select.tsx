"use client"

import * as React from "react"
import ReactCountryFlag from "react-country-flag"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

type CountryCode = {
  code: string
  name: string
  dial_code: string
}

const countries: CountryCode[] = [
  { code: "IN", name: "India", dial_code: "+91" },
  { code: "US", name: "United States", dial_code: "+1" },
  { code: "GB", name: "United Kingdom", dial_code: "+44" },
  { code: "CA", name: "Canada", dial_code: "+1" },
  { code: "AU", name: "Australia", dial_code: "+61" },
  { code: "DE", name: "Germany", dial_code: "+49" },
  { code: "FR", name: "France", dial_code: "+33" },
  { code: "IT", name: "Italy", dial_code: "+39" },
  { code: "ES", name: "Spain", dial_code: "+34" },
  { code: "SG", name: "Singapore", dial_code: "+65" },
  // Add more countries as needed
]

interface CountryCodeSelectProps {
  value: string
  onChange: (value: string) => void
}

export function CountryCodeSelect({ value, onChange }: CountryCodeSelectProps) {
  const [open, setOpen] = React.useState(false)
  const selectedCountry = countries.find(country => country.dial_code === value)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[110px] justify-between"
        >
          {selectedCountry ? (
            <div className="flex items-center gap-2">
              <ReactCountryFlag
                countryCode={selectedCountry.code}
                svg
                style={{
                  width: '1.2em',
                  height: '1.2em',
                }}
              />
              {selectedCountry.dial_code}
            </div>
          ) : (
            "+91"
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search country..." />
          <CommandEmpty>No country found.</CommandEmpty>
          <CommandGroup className="max-h-[300px] overflow-auto custom-scrollbar">
            {countries.map((country) => (
              <CommandItem
                key={country.code}
                value={country.name}
                onSelect={() => {
                  onChange(country.dial_code)
                  setOpen(false)
                }}
              >
                <div className="flex items-center gap-2">
                  <ReactCountryFlag
                    countryCode={country.code}
                    svg
                    style={{
                      width: '1.2em',
                      height: '1.2em',
                    }}
                  />
                  <span>{country.name}</span>
                  <span className="ml-auto text-sm text-muted-foreground">
                    {country.dial_code}
                  </span>
                </div>
                <Check
                  className={cn(
                    "ml-auto h-4 w-4",
                    value === country.dial_code ? "opacity-100" : "opacity-0"
                  )}
                />
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  )
}