/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ["class"],
    content: [
      './pages/**/*.{js,ts,jsx,tsx,mdx}',
      './components/**/*.{js,ts,jsx,tsx,mdx}',
      './app/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
      container: {
        center: true,
        padding: "2rem",
        screens: {
          "2xl": "1400px",
        },
      },
      extend: {
        colors: {
          border: "var(--border)",
          input: "var(--input)",
          ring: "var(--ring)",
          background: "var(--background)",
          foreground: "var(--foreground)",
          primary: {
            DEFAULT: "var(--primary)",
            foreground: "var(--primary-foreground)",
          },
          secondary: {
            DEFAULT: "var(--secondary)",
            foreground: "var(--secondary-foreground)",
          },
          destructive: {
            DEFAULT: "var(--destructive)",
            foreground: "var(--destructive-foreground)",
          },
          muted: {
            DEFAULT: "var(--muted)",
            foreground: "var(--muted-foreground)",
          },
          accent: {
            DEFAULT: "var(--accent)",
            foreground: "var(--accent-foreground)",
          },
          popover: {
            DEFAULT: "var(--popover)",
            foreground: "var(--popover-foreground)",
          },
          card: {
            DEFAULT: "var(--card)",
            foreground: "var(--card-foreground)",
          },
          // Custom vanilla latte colors
          vanilla: {
            50: "oklch(0.98 0.01 70)",
            100: "oklch(0.95 0.02 75)",
            200: "oklch(0.9 0.025 75)",
            300: "oklch(0.85 0.03 80)",
            400: "oklch(0.8 0.035 80)",
            500: "oklch(0.75 0.04 85)",
            600: "oklch(0.7 0.045 85)",
            700: "oklch(0.65 0.05 90)",
            800: "oklch(0.6 0.055 90)",
            900: "oklch(0.55 0.06 95)",
          },
          teal: {
            50: "oklch(0.95 0.02 170)",
            100: "oklch(0.9 0.04 165)",
            200: "oklch(0.8 0.06 165)",
            300: "oklch(0.7 0.08 165)",
            400: "oklch(0.6 0.1 165)",
            500: "oklch(0.5 0.12 165)",
            600: "oklch(0.45 0.14 165)",
            700: "oklch(0.4 0.16 165)",
            800: "oklch(0.35 0.18 165)",
            900: "oklch(0.3 0.2 165)",
          },
        },
        borderColor: {
          DEFAULT: "var(--border)",
        },
        outlineColor: {
          ring: "var(--ring)",
        },
        borderRadius: {
          lg: "var(--radius)",
          md: "calc(var(--radius) - 2px)",
          sm: "calc(var(--radius) - 4px)",
          xl: "calc(var(--radius) + 4px)",
          "2xl": "calc(var(--radius) + 8px)",
        },
        fontFamily: {
          'sf-pro': ['var(--font-sf-pro)', 'system-ui', 'sans-serif'],
          'space-grotesk': ['var(--font-space-grotesk)', 'sans-serif'],
          'inter': ['var(--font-inter)', 'sans-serif'],
        },
        backgroundImage: {
          'vanilla-gradient': 'linear-gradient(135deg, oklch(0.97 0.015 70) 0%, oklch(0.94 0.02 75) 100%)',
          'dark-gradient': 'linear-gradient(135deg, oklch(0.15 0.02 215) 0%, oklch(0.12 0.02 220) 100%)',
          'warm-gradient': 'linear-gradient(45deg, oklch(0.94 0.02 75) 0%, oklch(0.88 0.025 75) 50%, oklch(0.85 0.03 80) 100%)',
        },
        keyframes: {
          "accordion-down": {
            from: { height: 0 },
            to: { height: "var(--radix-accordion-content-height)" },
          },
          "accordion-up": {
            from: { height: "var(--radix-accordion-content-height)" },
            to: { height: 0 },
          },
          "fade-in": {
            "0%": { opacity: "0", transform: "translateY(10px)" },
            "100%": { opacity: "1", transform: "translateY(0)" },
          },
          "slide-in": {
            "0%": { transform: "translateX(-100%)" },
            "100%": { transform: "translateX(0)" },
          },
        },
        animation: {
          "accordion-down": "accordion-down 0.2s ease-out",
          "accordion-up": "accordion-up 0.2s ease-out",
          "fade-in": "fade-in 0.5s ease-out",
          "slide-in": "slide-in 0.3s ease-out",
        },
        boxShadow: {
          'warm': '0 4px 6px -1px oklch(0.8 0.03 75 / 0.1), 0 2px 4px -1px oklch(0.8 0.03 75 / 0.06)',
          'warm-lg': '0 10px 15px -3px oklch(0.8 0.03 75 / 0.1), 0 4px 6px -2px oklch(0.8 0.03 75 / 0.05)',
          'dark-warm': '0 4px 6px -1px oklch(0.05 0.02 220 / 0.3), 0 2px 4px -1px oklch(0.05 0.02 220 / 0.2)',
        },
        scrollbar: {
          'thin': 'thin',
          'none': 'none',
        },
      },
    },
    plugins: [require("tailwindcss-animate")],
  }