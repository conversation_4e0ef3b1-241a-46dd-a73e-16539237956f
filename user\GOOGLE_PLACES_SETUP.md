# Google Places API Setup Guide

This guide will help you set up Google Places API for the address search functionality.

## 🚀 Quick Setup

### Step 1: Get Google Places API Key

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/

2. **Create or Select a Project**
   - Create a new project or select an existing one
   - Note down your project ID

3. **Enable APIs**
   - Go to "APIs & Services" > "Library"
   - Search for and enable these APIs:
     - **Places API**
     - **Places API (New)** (recommended)
     - **Geocoding API** (optional, for enhanced functionality)

4. **Create API Key**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the generated API key

5. **Secure Your API Key (Important!)**
   - Click on your API key to edit it
   - Under "API restrictions", select "Restrict key"
   - Choose the APIs you enabled (Places API, etc.)
   - Under "Application restrictions", you can:
     - Set HTTP referrers for web apps
     - Set IP addresses for server apps
     - Or leave unrestricted for development (not recommended for production)

### Step 2: Configure Environment Variables

1. **Update .env.local**
   ```bash
   # Replace YOUR_GOOGLE_PLACES_API_KEY_HERE with your actual API key
   GOOGLE_PLACES_API_KEY=AIzaSyBvOkBwGyOnOiKagFhlFikcgHiCtqp6oFo
   ```

2. **Restart Development Server**
   ```bash
   npm run dev
   ```

### Step 3: Test the Integration

1. **Open the application**
   - Go to http://localhost:3001/account
   - Click "Edit" on the address section

2. **Test address search**
   - Type in the address field (e.g., "Andheri", "Koramangala", "Connaught Place")
   - You should see real Google Places suggestions
   - Select a suggestion to auto-fill city, state, country, and PIN code

## 🔧 API Configuration Details

### Supported Features

- **Autocomplete**: Real-time address suggestions as you type
- **Address Parsing**: Automatic extraction of city, state, country, postal code
- **India Focus**: Configured to prioritize Indian addresses
- **Accurate Data**: Uses Google's comprehensive address database

### API Endpoints Used

1. **Places Autocomplete API**
   - Endpoint: `https://maps.googleapis.com/maps/api/place/autocomplete/json`
   - Purpose: Get address suggestions
   - Parameters:
     - `input`: User's search query
     - `components`: `country:in` (restricts to India)
     - `types`: `address` (focuses on addresses)
     - `language`: `en` (English language)

2. **Places Details API**
   - Endpoint: `https://maps.googleapis.com/maps/api/place/details/json`
   - Purpose: Get detailed address information
   - Parameters:
     - `place_id`: From autocomplete results
     - `fields`: `address_components,formatted_address`

### Cost Considerations

- **Places Autocomplete**: ~$2.83 per 1,000 requests
- **Places Details**: ~$17 per 1,000 requests
- **Free Tier**: $200 credit per month (covers ~70,000 autocomplete requests)

### Rate Limits

- **Default**: 1,000 requests per 100 seconds per user
- **Daily**: No daily limit by default
- **Concurrent**: 50 requests per second

## 🛡️ Security Best Practices

### For Development
```bash
# .env.local
GOOGLE_PLACES_API_KEY=your_development_key_here
```

### For Production
1. **Use separate API keys** for development and production
2. **Set HTTP referrer restrictions** to your domain
3. **Enable only required APIs** (Places API, Places Details API)
4. **Monitor usage** in Google Cloud Console
5. **Set up billing alerts** to avoid unexpected charges

### API Key Restrictions Example
```
Application restrictions:
- HTTP referrers: https://yourdomain.com/*, https://*.yourdomain.com/*

API restrictions:
- Places API
- Places API (New)
```

## 🐛 Troubleshooting

### Common Issues

1. **"API configuration error"**
   - Check if `GOOGLE_PLACES_API_KEY` is set in `.env.local`
   - Restart the development server

2. **"Google Places API error: REQUEST_DENIED"**
   - Verify API key is correct
   - Check if Places API is enabled in Google Cloud Console
   - Verify API key restrictions allow your domain/IP

3. **"Google Places API error: OVER_QUERY_LIMIT"**
   - You've exceeded your quota
   - Check usage in Google Cloud Console
   - Consider upgrading your billing plan

4. **No suggestions appearing**
   - Check browser console for errors
   - Verify API key has Places API enabled
   - Try typing more than 3 characters

5. **Suggestions not India-focused**
   - The API is configured with `components=country:in`
   - This should prioritize Indian addresses

### Testing API Key
```bash
# Test your API key directly
curl "https://maps.googleapis.com/maps/api/place/autocomplete/json?input=Andheri&key=YOUR_API_KEY&components=country:in"
```

## 📊 Monitoring Usage

1. **Google Cloud Console**
   - Go to "APIs & Services" > "Dashboard"
   - Monitor requests and quotas

2. **Set up Alerts**
   - Go to "Billing" > "Budgets & alerts"
   - Create budget alerts for API usage

## 🔄 Fallback Options

If Google Places API is not available, the system will:
1. Show an error message to the user
2. Allow manual entry of all address fields
3. Continue to work without search functionality

The address search is an enhancement - the core functionality remains intact without it.
