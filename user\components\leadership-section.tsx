"use client"

import { motion } from "framer-motion"
import { Users, Target, TrendingUp, Award } from "lucide-react"
import EnhancedTeamMember from "@/components/enhanced-team-member"
import AnimatedBackground from "@/components/animated-background"
import { Card, CardContent } from "@/components/ui/card"

const leadershipStats = [
  {
    icon: Users,
    value: "20+",
    label: "Years Combined Experience",
    description: "Decades of industry expertise"
  },
  {
    icon: Target,
    value: "500+",
    label: "Successful Projects",
    description: "Delivered across industries"
  },
  {
    icon: TrendingUp,
    value: "98%",
    label: "Client Satisfaction",
    description: "Consistent quality delivery"
  },
  {
    icon: Award,
    value: "15+",
    label: "Industry Awards",
    description: "Recognition for excellence"
  }
]

const teamMembers = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    position: "CEO & Founder",
    image: "/images/jcr-profile-img.png",
    bio: "Visionary leader with over 20 years of experience in chemical engineering and business development. Passionate about driving innovation and sustainable growth in the chemical industry.",
    experience: "20+ years in Chemical Industry",
    location: "Gujarat, India",
    achievements: [
      "Founded Benzochem Industries in 1995",
      "Led company to become industry leader",
      "Pioneered sustainable chemical practices",
      "Established international partnerships"
    ],
    specialties: [
      "Chemical Engineering",
      "Business Strategy",
      "Innovation Management",
      "Sustainability"
    ],
    linkedinUrl: "#",
    email: "<EMAIL>"
  },
  {
    name: "Hetrajsinh Jashvantsinh Raj",
    position: "Managing Director",
    image: "/images/htr-profile-img.png",
    bio: "Strategic operations expert specializing in chemical manufacturing processes, supply chain optimization, and quality management systems. Committed to operational excellence and customer satisfaction.",
    experience: "15+ years in Operations",
    location: "Gujarat, India",
    achievements: [
      "Streamlined global supply chain operations",
      "Implemented ISO quality standards",
      "Reduced operational costs by 30%",
      "Expanded international market presence"
    ],
    specialties: [
      "Operations Management",
      "Supply Chain",
      "Quality Assurance",
      "Process Optimization"
    ],
    linkedinUrl: "#",
    email: "<EMAIL>"
  }
]

export default function LeadershipSection() {
  return (
    <section className="py-20 md:py-28 relative overflow-hidden">
      {/* Animated Background */}
      <AnimatedBackground />
      
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-secondary/20" />
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Users className="h-4 w-4" />
            Leadership Team
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Meet Our Leaders
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Our experienced leadership team combines decades of industry expertise with a passion for innovation, 
            driving Benzochem Industries toward a sustainable and successful future.
          </p>
        </motion.div>

        {/* Leadership Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {leadershipStats.map((stat, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 max-w-6xl mx-auto">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <EnhancedTeamMember {...member} />
            </motion.div>
          ))}
        </div>

        {/* Leadership Philosophy */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20"
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-semibold mb-6 text-foreground">
                Our Leadership Philosophy
              </h3>
              <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                We believe in leading by example, fostering innovation, and building lasting relationships. 
                Our leadership team is committed to creating value for our customers, employees, and stakeholders 
                while maintaining the highest standards of integrity and excellence in everything we do.
              </p>
              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Innovation-Driven
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Customer-Centric
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Sustainability-Focused
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Excellence-Oriented
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}