"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import ProductCard from "@/components/product-card"
import type { Product } from "@/lib/types"
import { apiClient, transformApiProductToProduct, type ApiProduct } from "@/lib/api-client"

interface RelatedProductsProps {
  currentProductId: string
  category?: string
}

export default function RelatedProducts({ currentProductId, category }: RelatedProductsProps) {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchRelatedProducts() {
      try {
        setLoading(true)
        // Fetch products, optionally filtered by category
        const response = await apiClient.getProducts({ 
          limit: 20,
          collection: category 
        })
        
        if (response.success && response.data && Array.isArray(response.data)) {
          // Transform API products to Product type and filter out current product
          const allProducts = (response.data as ApiProduct[])
            .map(transformApiProductToProduct)
            .filter(product => product.id !== currentProductId)
            .slice(0, 4) // Limit to 4 related products
          
          setProducts(allProducts)
        } else {
          setProducts([])
        }
      } catch (error) {
        console.error("Error fetching related products:", error)
        setProducts([])
      } finally {
        setLoading(false)
      }
    }

    fetchRelatedProducts()
  }, [currentProductId, category])

  if (loading) {
    return (
      <div className="mt-24 mb-16">
        <h2 className="text-2xl font-bold mb-8">Related Products</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 animate-pulse">
              <div className="h-40 w-full bg-gray-200 dark:bg-gray-700 rounded-md mb-4" />
              <div className="h-6 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
              <div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-700 rounded" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!products || products.length === 0) {
    return null
  }

  return (
    <div className="mt-24 mb-16">
      <h2 className="text-2xl font-bold mb-8">Related Products</h2>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {products.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </motion.div>
    </div>
  )
}
