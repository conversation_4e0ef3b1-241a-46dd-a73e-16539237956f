# Benzochem Industries - Production Deployment Guide

## 🎯 Overview

This guide provides comprehensive instructions for deploying the Benzochem Industries chemical trading platform to production. The platform consists of two main applications:

- **@admin**: Administrative dashboard for managing users, products, and quotations
- **@user**: Customer-facing application for browsing products and requesting quotations

## 📋 Pre-Deployment Checklist

### ✅ Security Requirements

- [ ] **Admin Authentication**: Manual password entry system configured
- [ ] **API Key Management**: Production API keys generated and configured
- [ ] **Session Security**: Secure cookie-based authentication implemented
- [ ] **Rate Limiting**: Production-grade rate limiting configured
- [ ] **Input Validation**: Comprehensive input sanitization enabled
- [ ] **Security Headers**: All security headers properly configured
- [ ] **HTTPS/TLS**: SSL certificates installed and configured
- [ ] **CSRF Protection**: Cross-site request forgery protection enabled

### ✅ Infrastructure Requirements

- [ ] **Database**: Production database (PostgreSQL/MySQL) configured
- [ ] **Redis**: Redis instance for session storage and rate limiting
- [ ] **CDN**: Content delivery network configured for static assets
- [ ] **Load Balancer**: Load balancing configured for high availability
- [ ] **Monitoring**: Application performance monitoring (APM) set up
- [ ] **Logging**: Centralized logging system configured
- [ ] **Backup**: Automated backup and recovery procedures in place

### ✅ Environment Configuration

- [ ] **Environment Variables**: All production environment variables configured
- [ ] **API Keys**: All third-party API keys updated for production
- [ ] **Domain Configuration**: Production domains properly configured
- [ ] **Email Service**: Production email service configured
- [ ] **File Storage**: Production file storage (S3/CloudFlare) configured

## 🚀 Deployment Steps

### Step 1: Infrastructure Setup

#### 1.1 Database Setup

```bash
# PostgreSQL setup (recommended)
# Create production database
createdb benzochem_production

# Run migrations (if using traditional database)
# Note: Currently using Convex, migration to PostgreSQL recommended for production
```

#### 1.2 Redis Setup

```bash
# Install and configure Redis for session storage
sudo apt-get install redis-server
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

#### 1.3 SSL/TLS Configuration

```bash
# Install SSL certificates (using Let's Encrypt)
sudo certbot --nginx -d admin.benzochem.com
sudo certbot --nginx -d benzochem.com
```

### Step 2: Admin Application Deployment

#### 2.1 Environment Configuration

```bash
# Copy production environment file
cp .env.production .env.local

# Update all placeholder values with actual production values
# CRITICAL: Replace all REPLACE_WITH_* values
```

#### 2.2 Build and Deploy

```bash
# Install dependencies
npm ci --production

# Build application
npm run build

# Start production server
npm start
```

#### 2.3 Admin User Setup

Since admin authentication uses manual password entry:

1. Access Convex dashboard
2. Navigate to the `admins` table
3. Insert admin user record:

```javascript
// Example admin user record
{
  email: "<EMAIL>",
  firstName: "Admin",
  lastName: "User",
  password: "YourSecurePassword123!", // Plain text as per design
  role: "super_admin",
  permissions: [
    "users:read", "users:write", "users:delete",
    "products:read", "products:write", "products:delete",
    "collections:read", "collections:write", "collections:delete",
    "quotations:read", "quotations:write", "quotations:delete",
    "analytics:read", "api_keys:read", "api_keys:write"
  ],
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
}
```

#### 2.4 API Key Generation

1. Login to admin dashboard
2. Navigate to Settings > API Keys
3. Generate production API key with required permissions:
   - `users:read`, `users:write`
   - `products:read`
   - `collections:read`
   - `quotations:read`, `quotations:write`

### Step 3: User Application Deployment

#### 3.1 Environment Configuration

```bash
# Copy production environment file
cp .env.production .env.local

# Update NEXT_PUBLIC_API_KEY with the API key generated in Step 2.4
# Update all other placeholder values
```

#### 3.2 Build and Deploy

```bash
# Install dependencies
npm ci --production

# Build application
npm run build

# Start production server
npm start
```

### Step 4: Nginx Configuration

#### 4.1 Admin Application (admin.benzochem.com)

```nginx
server {
    listen 443 ssl http2;
    server_name admin.benzochem.com;

    ssl_certificate /etc/letsencrypt/live/admin.benzochem.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/admin.benzochem.com/privkey.pem;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=admin_login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=admin_api:10m rate=100r/m;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /api/auth/login {
        limit_req zone=admin_login burst=3 nodelay;
        proxy_pass http://localhost:3001;
        # ... other proxy settings
    }

    location /api/ {
        limit_req zone=admin_api burst=50 nodelay;
        proxy_pass http://localhost:3001;
        # ... other proxy settings
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name admin.benzochem.com;
    return 301 https://$server_name$request_uri;
}
```

#### 4.2 User Application (benzochem.com)

```nginx
server {
    listen 443 ssl http2;
    server_name benzochem.com www.benzochem.com;

    ssl_certificate /etc/letsencrypt/live/benzochem.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/benzochem.com/privkey.pem;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=user_api:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=user_auth:10m rate=10r/m;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /api/ {
        limit_req zone=user_api burst=30 nodelay;
        proxy_pass http://localhost:3000;
        # ... other proxy settings
    }

    location ~ ^/(login|register) {
        limit_req zone=user_auth burst=5 nodelay;
        proxy_pass http://localhost:3000;
        # ... other proxy settings
    }
}

# Redirect HTTP to HTTPS and www to non-www
server {
    listen 80;
    server_name benzochem.com www.benzochem.com;
    return 301 https://benzochem.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.benzochem.com;
    ssl_certificate /etc/letsencrypt/live/benzochem.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/benzochem.com/privkey.pem;
    return 301 https://benzochem.com$request_uri;
}
```

### Step 5: Process Management

#### 5.1 PM2 Configuration

```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'benzochem-admin',
      script: 'npm',
      args: 'start',
      cwd: '/path/to/admin',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 2,
      exec_mode: 'cluster',
      max_memory_restart: '1G',
      error_file: '/var/log/pm2/benzochem-admin-error.log',
      out_file: '/var/log/pm2/benzochem-admin-out.log',
      log_file: '/var/log/pm2/benzochem-admin.log'
    },
    {
      name: 'benzochem-user',
      script: 'npm',
      args: 'start',
      cwd: '/path/to/user',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      instances: 2,
      exec_mode: 'cluster',
      max_memory_restart: '1G',
      error_file: '/var/log/pm2/benzochem-user-error.log',
      out_file: '/var/log/pm2/benzochem-user-out.log',
      log_file: '/var/log/pm2/benzochem-user.log'
    }
  ]
};
```

```bash
# Start applications with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Step 6: Monitoring and Logging

#### 6.1 Application Monitoring

```bash
# Install monitoring tools
npm install -g @sentry/cli

# Configure Sentry for error tracking
# Update SENTRY_DSN in environment variables
```

#### 6.2 Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/benzochem

# Add log rotation configuration
/var/log/pm2/*.log {
    daily
    missingok
    rotate 52
    compress
    notifempty
    create 644 pm2 pm2
    postrotate
        pm2 reloadLogs
    endscript
}
```

### Step 7: Backup and Recovery

#### 7.1 Database Backup

```bash
# Create backup script
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/database"
DB_NAME="benzochem_production"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup Convex data (if still using Convex)
# Note: Implement Convex backup strategy

# Backup PostgreSQL (when migrated)
# pg_dump $DB_NAME > $BACKUP_DIR/benzochem_$DATE.sql
# gzip $BACKUP_DIR/benzochem_$DATE.sql

# Upload to S3
# aws s3 cp $BACKUP_DIR/benzochem_$DATE.sql.gz s3://benzochem-backups/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

#### 7.2 Application Backup

```bash
# Create application backup script
#!/bin/bash
# backup-application.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/application"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/benzochem-admin_$DATE.tar.gz /path/to/admin
tar -czf $BACKUP_DIR/benzochem-user_$DATE.tar.gz /path/to/user

# Upload to S3
aws s3 cp $BACKUP_DIR/ s3://benzochem-backups/application/ --recursive

# Clean old backups
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### Step 8: Security Hardening

#### 8.1 Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

#### 8.2 Fail2Ban Configuration

```bash
# Install and configure Fail2Ban
sudo apt-get install fail2ban

# Create custom jail for Nginx
sudo nano /etc/fail2ban/jail.local

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

### Step 9: Performance Optimization

#### 9.1 CDN Configuration

```bash
# Configure CloudFlare or AWS CloudFront
# Update CDN_URL in environment variables
# Configure cache rules for static assets
```

#### 9.2 Database Optimization

```sql
-- Create indexes for better performance (when using PostgreSQL)
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_quotations_user_id ON quotations(user_id);
CREATE INDEX idx_quotations_status ON quotations(status);
```

### Step 10: Health Checks and Monitoring

#### 10.1 Health Check Endpoints

Both applications include health check endpoints:
- Admin: `https://admin.benzochem.com/api/health`
- User: `https://benzochem.com/api/health`

#### 10.2 Uptime Monitoring

```bash
# Configure uptime monitoring service
# Examples: UptimeRobot, Pingdom, StatusCake
```

## 🔧 Post-Deployment Tasks

### ✅ Verification Checklist

- [ ] **Admin Login**: Verify admin login functionality
- [ ] **API Connectivity**: Test API communication between user and admin apps
- [ ] **User Registration**: Test user registration and approval workflow
- [ ] **Product Catalog**: Verify product display and search functionality
- [ ] **Quotation System**: Test quotation creation and management
- [ ] **Email Notifications**: Verify email delivery for approvals/rejections
- [ ] **Security Headers**: Verify all security headers are present
- [ ] **SSL/TLS**: Verify SSL certificates and HTTPS redirection
- [ ] **Rate Limiting**: Test rate limiting functionality
- [ ] **Backup System**: Verify backup creation and restoration
- [ ] **Monitoring**: Verify monitoring and alerting systems

### 🚨 Security Testing

```bash
# Run security scans
npm audit
nmap -sV your-domain.com
sslyze your-domain.com

# Test for common vulnerabilities
# - SQL injection
# - XSS attacks
# - CSRF attacks
# - Authentication bypass
# - Rate limiting bypass
```

### 📊 Performance Testing

```bash
# Load testing with Artillery
npm install -g artillery
artillery quick --count 100 --num 10 https://benzochem.com

# Monitor performance metrics
# - Response times
# - Memory usage
# - CPU usage
# - Database performance
```

## 🆘 Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Verify API key configuration
   - Check network connectivity between applications
   - Verify CORS settings

2. **Authentication Issues**
   - Check JWT secret configuration
   - Verify cookie settings
   - Check session storage

3. **Database Connection Issues**
   - Verify Convex URL and credentials
   - Check network connectivity
   - Monitor database performance

4. **Email Delivery Issues**
   - Verify SMTP configuration
   - Check email service quotas
   - Monitor email logs

### Emergency Procedures

1. **Application Rollback**
   ```bash
   # Rollback to previous version
   pm2 stop all
   # Restore previous application version
   pm2 start ecosystem.config.js
   ```

2. **Database Recovery**
   ```bash
   # Restore from backup
   # Follow backup restoration procedures
   ```

3. **Emergency Contacts**
   - System Administrator: [contact info]
   - Database Administrator: [contact info]
   - Security Team: [contact info]

## 📚 Additional Resources

- [Next.js Production Deployment](https://nextjs.org/docs/deployment)
- [Convex Production Guide](https://docs.convex.dev/production)
- [Nginx Security Best Practices](https://nginx.org/en/docs/)
- [PM2 Production Guide](https://pm2.keymetrics.io/docs/usage/deployment/)

## 📝 Maintenance Schedule

- **Daily**: Monitor application health and performance
- **Weekly**: Review security logs and update dependencies
- **Monthly**: Security patches and performance optimization
- **Quarterly**: Full security audit and penetration testing
- **Annually**: Disaster recovery testing and compliance review

---

**Note**: This deployment guide assumes a Linux-based production environment. Adjust configurations as needed for your specific infrastructure and requirements.