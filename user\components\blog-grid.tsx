"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import BlogPostCard from "@/components/blog-post-card"
import type { BlogPost } from "@/lib/types"

interface BlogGridProps {
  posts: BlogPost[]
}

export default function BlogGrid({ posts }: BlogGridProps) {
  const [categoryFilter, setCategoryFilter] = useState<string>("all")

  // Get unique categories
  const categories = ["all", ...new Set(posts.map((post) => post.category))]

  // Apply filtering
  const filteredPosts = categoryFilter === "all" ? posts : posts.filter((post) => post.category === categoryFilter)

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-medium">Latest Articles</h2>
          <p className="text-neutral-600">{filteredPosts.length} articles found</p>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              {categoryFilter === "all" ? "All Categories" : categoryFilter}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuRadioGroup value={categoryFilter} onValueChange={setCategoryFilter}>
              {categories.map((category) => (
                <DropdownMenuRadioItem key={category} value={category}>
                  {category === "all" ? "All Categories" : category}
                </DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {filteredPosts.map((post) => (
          <BlogPostCard key={post.slug} post={post} />
        ))}
      </motion.div>
    </div>
  )
}
