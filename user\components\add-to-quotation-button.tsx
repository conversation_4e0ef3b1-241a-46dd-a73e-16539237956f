"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Button } from "@/components/ui/button"
import { FileText, Plus } from "lucide-react"
import { toast } from "sonner"

interface AddToQuotationButtonProps {
  productId: string
  variantId: string
  name: string
  category: string
  image: string
  price: number
  quantity?: number
  unit?: string
  packageSize?: string
  disabled?: boolean
  className?: string
}

export default function AddToQuotationButton({
  productId,
  variantId,
  name,
  category,
  image,
  price,
  quantity = 1,
  unit = "KG",
  packageSize,
  disabled = false,
  className
}: AddToQuotationButtonProps) {
  const { user } = useAuth()
  const { addToQuotation } = useSecureQuotation()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleAddToQuotation = async () => {
    if (!user) {
      toast.error("Please log in to request quotations", {
        action: {
          label: "Login",
          onClick: () => router.push("/login")
        }
      })
      return
    }

    try {
      setIsLoading(true)
      
      await addToQuotation({
        productId,
        variantId,
        name,
        category,
        image,
        price,
        quantity,
        unit: packageSize || unit,
        specifications: packageSize ? `Package Size: ${packageSize}` : undefined
      })
    } catch (error) {
      console.error("Error adding to quotation:", error)
      toast.error("Failed to add item to quotation.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      onClick={handleAddToQuotation}
      disabled={disabled || isLoading}
      className={`border shadow-xs bg-accent text-accent-foreground dark:bg-input/60 dark:border-input dark:hover:bg-input/50 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      <FileText className="h-4 w-4 mr-2" />
      {isLoading ? "Adding..." : "Request Quote"}
    </Button>
  )
}