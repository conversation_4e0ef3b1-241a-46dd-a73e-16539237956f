import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const { email, firstName, title, excerpt, articles } = await request.json()

    if (!email || !firstName || !title || !excerpt || !articles) {
      return NextResponse.json(
        { error: 'Email, firstName, title, excerpt, and articles are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate articles array
    if (!Array.isArray(articles) || articles.length === 0) {
      return NextResponse.json(
        { error: 'Articles must be a non-empty array' },
        { status: 400 }
      )
    }

    // Validate each article
    for (const article of articles) {
      if (!article.title || !article.link || !article.excerpt) {
        return NextResponse.json(
          { error: 'Each article must have title, link, and excerpt' },
          { status: 400 }
        )
      }
    }

    // Send newsletter email
    const emailResult = await emailService.sendNewsletterEmail(email, firstName, {
      title,
      excerpt,
      articles
    })
    
    if (!emailResult.success) {
      console.error('Failed to send newsletter email:', emailResult.error)
      return NextResponse.json(
        { error: 'Failed to send newsletter email' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Newsletter email sent successfully'
    })

  } catch (error) {
    console.error('Send newsletter email error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}