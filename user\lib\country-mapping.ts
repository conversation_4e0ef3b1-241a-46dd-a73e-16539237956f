// Country name to country code mapping for Shopify compatibility
// This maps Google Places API country names to ISO 3166-1 alpha-2 country codes

export interface CountryMapping {
  name: string
  code: string
  dialCode: string
}

export const countryMappings: CountryMapping[] = [
  // Major countries
  { name: "India", code: "IN", dialCode: "+91" },
  { name: "United States", code: "US", dialCode: "+1" },
  { name: "United Kingdom", code: "GB", dialCode: "+44" },
  { name: "Canada", code: "CA", dialCode: "+1" },
  { name: "Australia", code: "AU", dialCode: "+61" },
  { name: "Germany", code: "DE", dialCode: "+49" },
  { name: "France", code: "FR", dialCode: "+33" },
  { name: "Italy", code: "IT", dialCode: "+39" },
  { name: "Spain", code: "ES", dialCode: "+34" },
  { name: "Singapore", code: "SG", dialCode: "+65" },
  { name: "Japan", code: "<PERSON>", dialCode: "+81" },
  { name: "South Korea", code: "KR", dialCode: "+82" },
  { name: "China", code: "CN", dialCode: "+86" },
  { name: "Brazil", code: "BR", dialCode: "+55" },
  { name: "Mexico", code: "MX", dialCode: "+52" },
  { name: "Netherlands", code: "NL", dialCode: "+31" },
  { name: "Belgium", code: "BE", dialCode: "+32" },
  { name: "Switzerland", code: "CH", dialCode: "+41" },
  { name: "Austria", code: "AT", dialCode: "+43" },
  { name: "Sweden", code: "SE", dialCode: "+46" },
  { name: "Norway", code: "NO", dialCode: "+47" },
  { name: "Denmark", code: "DK", dialCode: "+45" },
  { name: "Finland", code: "FI", dialCode: "+358" },
  { name: "Poland", code: "PL", dialCode: "+48" },
  { name: "Russia", code: "RU", dialCode: "+7" },
  { name: "South Africa", code: "ZA", dialCode: "+27" },
  { name: "Egypt", code: "EG", dialCode: "+20" },
  { name: "Israel", code: "IL", dialCode: "+972" },
  { name: "Turkey", code: "TR", dialCode: "+90" },
  { name: "Greece", code: "GR", dialCode: "+30" },
  { name: "Portugal", code: "PT", dialCode: "+351" },
  { name: "Ireland", code: "IE", dialCode: "+353" },
  { name: "New Zealand", code: "NZ", dialCode: "+64" },
  { name: "Thailand", code: "TH", dialCode: "+66" },
  { name: "Malaysia", code: "MY", dialCode: "+60" },
  { name: "Indonesia", code: "ID", dialCode: "+62" },
  { name: "Philippines", code: "PH", dialCode: "+63" },
  { name: "Vietnam", code: "VN", dialCode: "+84" },
  { name: "Hong Kong", code: "HK", dialCode: "+852" },
  { name: "Taiwan", code: "TW", dialCode: "+886" },
  { name: "United Arab Emirates", code: "AE", dialCode: "+971" },
  { name: "Saudi Arabia", code: "SA", dialCode: "+966" },
  { name: "Kuwait", code: "KW", dialCode: "+965" },
  { name: "Qatar", code: "QA", dialCode: "+974" },
  { name: "Bahrain", code: "BH", dialCode: "+973" },
  { name: "Oman", code: "OM", dialCode: "+968" },
  
  // South Asian countries (neighbors of India)
  { name: "Pakistan", code: "PK", dialCode: "+92" },
  { name: "Bangladesh", code: "BD", dialCode: "+880" },
  { name: "Sri Lanka", code: "LK", dialCode: "+94" },
  { name: "Nepal", code: "NP", dialCode: "+977" },
  { name: "Bhutan", code: "BT", dialCode: "+975" },
  { name: "Maldives", code: "MV", dialCode: "+960" },
  { name: "Afghanistan", code: "AF", dialCode: "+93" },
  { name: "Myanmar", code: "MM", dialCode: "+95" },
]

// Create lookup maps for efficient searching
const nameToCodeMap = new Map<string, string>()
const codeToNameMap = new Map<string, string>()

countryMappings.forEach(country => {
  nameToCodeMap.set(country.name.toLowerCase(), country.code)
  codeToNameMap.set(country.code, country.name)
})

/**
 * Convert country name to country code (for Shopify)
 * @param countryName - Full country name (e.g., "India")
 * @returns Country code (e.g., "IN") or the original name if not found
 */
export function getCountryCode(countryName: string): string {
  if (!countryName) return ''
  
  const code = nameToCodeMap.get(countryName.toLowerCase())
  if (code) {
    return code
  }
  
  // If exact match not found, try partial matching
  const lowerName = countryName.toLowerCase()
  for (const [name, code] of nameToCodeMap.entries()) {
    if (name.includes(lowerName) || lowerName.includes(name)) {
      return code
    }
  }
  
  // If no match found, return the original name
  // This ensures we don't break existing functionality
  console.warn(`Country code not found for: ${countryName}`)
  return countryName
}

/**
 * Convert country code to country name
 * @param countryCode - Country code (e.g., "IN")
 * @returns Full country name (e.g., "India") or the original code if not found
 */
export function getCountryName(countryCode: string): string {
  if (!countryCode) return ''
  
  const name = codeToNameMap.get(countryCode.toUpperCase())
  return name || countryCode
}

/**
 * Check if a country code is valid
 * @param countryCode - Country code to validate
 * @returns true if valid, false otherwise
 */
export function isValidCountryCode(countryCode: string): boolean {
  return codeToNameMap.has(countryCode.toUpperCase())
}

/**
 * Get all supported countries
 * @returns Array of all country mappings
 */
export function getAllCountries(): CountryMapping[] {
  return [...countryMappings]
}

/**
 * Search countries by name (case-insensitive)
 * @param searchTerm - Search term
 * @returns Array of matching countries
 */
export function searchCountries(searchTerm: string): CountryMapping[] {
  if (!searchTerm) return []

  const lowerSearch = searchTerm.toLowerCase()
  return countryMappings.filter(country =>
    country.name.toLowerCase().includes(lowerSearch) ||
    country.code.toLowerCase().includes(lowerSearch)
  )
}

// Indian state name to code mapping for Shopify compatibility
export const indianStates: Record<string, string> = {
  // Major states
  'andhra pradesh': 'AP',
  'arunachal pradesh': 'AR',
  'assam': 'AS',
  'bihar': 'BR',
  'chhattisgarh': 'CG',
  'goa': 'GA',
  'gujarat': 'GJ',
  'haryana': 'HR',
  'himachal pradesh': 'HP',
  'jharkhand': 'JH',
  'karnataka': 'KA',
  'kerala': 'KL',
  'madhya pradesh': 'MP',
  'maharashtra': 'MH',
  'manipur': 'MN',
  'meghalaya': 'ML',
  'mizoram': 'MZ',
  'nagaland': 'NL',
  'odisha': 'OR',
  'punjab': 'PB',
  'rajasthan': 'RJ',
  'sikkim': 'SK',
  'tamil nadu': 'TN',
  'telangana': 'TG',
  'tripura': 'TR',
  'uttar pradesh': 'UP',
  'uttarakhand': 'UK',
  'west bengal': 'WB',

  // Union territories
  'andaman and nicobar islands': 'AN',
  'chandigarh': 'CH',
  'dadra and nagar haveli and daman and diu': 'DH',
  'delhi': 'DL',
  'jammu and kashmir': 'JK',
  'ladakh': 'LA',
  'lakshadweep': 'LD',
  'puducherry': 'PY',

  // Alternative names
  'orissa': 'OR', // Old name for Odisha
  'pondicherry': 'PY', // Old name for Puducherry
  'new delhi': 'DL',
  'national capital territory of delhi': 'DL',
}

/**
 * Convert Indian state name to state code
 * @param stateName - Full state name (e.g., "Maharashtra")
 * @returns State code (e.g., "MH") or original name if not found
 */
export function getIndianStateCode(stateName: string): string {
  if (!stateName) return ''

  const lowerStateName = stateName.toLowerCase()
  const stateCode = indianStates[lowerStateName]

  if (stateCode) {
    return stateCode
  }

  // Try partial matching
  for (const [name, code] of Object.entries(indianStates)) {
    if (name.includes(lowerStateName) || lowerStateName.includes(name)) {
      return code
    }
  }

  // If no match found, return the original name
  console.warn(`Indian state code not found for: ${stateName}`)
  return stateName
}

/**
 * Get province/state code based on country
 * @param provinceName - Province/state name
 * @param countryCode - Country code (e.g., "IN")
 * @returns Province code or original name
 */
export function getProvinceCode(provinceName: string, countryCode: string): string {
  if (!provinceName) return ''

  // Handle Indian states specifically
  if (countryCode === 'IN') {
    return getIndianStateCode(provinceName)
  }

  // For other countries, return the original name
  // Can be extended for other countries as needed
  return provinceName
}
