"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { ShoppingBag, Info } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { getProductImageUrl, getProductCategory } from "@/lib/image-utils"
import type { Product } from "@/lib/types"

export default function ProductCard({ product }: { product: Product }) {
  const [isHovered, setIsHovered] = useState(false)
  const [imageError, setImageError] = useState(false)

  // Helper function to get the main image URL
  const getImageUrl = () => {
    if (imageError) return "/placeholder-chemical.jpg"
    return getProductImageUrl(product)
  }

  // Helper function to clean and format description
  const getCleanDescription = () => {
    if (!product.description) return 'High-quality chemical product for industrial applications.'
    
    // Remove HTML tags and decode HTML entities
    const cleanText = product.description
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim() // Remove leading/trailing whitespace
    
    return cleanText || 'High-quality chemical product for industrial applications.'
  }

  // Helper function to get metafield value - supports both 'custom' and 'chemical' namespaces
  const getMetafieldValue = (key: string, namespace: string = 'custom') => {
    if (!product.metafields || !Array.isArray(product.metafields)) {
      return ''
    }
    // Try the specified namespace first, then try 'chemical' namespace as fallback
    let metafield = product.metafields.find(m => m?.namespace === namespace && m?.key === key)
    if (!metafield && namespace !== 'chemical') {
      metafield = product.metafields.find(m => m?.namespace === 'chemical' && m?.key === key)
    }
    return metafield?.value || ''
  }

  const purity = getMetafieldValue('purity') || getMetafieldValue('purity', 'chemical')
  const packaging = getMetafieldValue('package_size') || getMetafieldValue('packaging', 'chemical')

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <Card
        className="overflow-hidden border-0 bg-card shadow-sm transition-all duration-300 hover:shadow-md"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={getImageUrl()}
            alt={product.title}
            fill
            className="object-cover transition-transform duration-500 hover:scale-105"
            onError={() => setImageError(true)}
          />

          {isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute inset-0 flex items-center justify-center bg-black/40"
            >
              <Link href={`/products/${product.id.split('/').pop()}`}>
                <Button variant="secondary" size="sm" className="mr-2">
                  <Info className="h-4 w-4 mr-1" />
                  Details
                </Button>
              </Link>
            </motion.div>
          )}

          <div className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded">
            {getProductCategory(product)}
          </div>
        </div>

        <CardContent className="p-4">
          <Link href={`/products/${product.id.split('/').pop()}`}>
            <h3 className="font-medium text-lg mb-1 text-card-foreground hover:text-primary transition-colors">
              {product.title}
            </h3>
          </Link>
          <p className="text-sm text-muted-foreground mb-2">
            {getCleanDescription().slice(0, 100)}...
          </p>
          <div className="flex flex-wrap gap-2 text-xs">
            {purity && (
              <span className="bg-muted text-muted-foreground px-2 py-1 rounded">
                Purity: {purity}
              </span>
            )}
            {packaging && (
              <span className="bg-muted text-muted-foreground px-2 py-1 rounded">
                {packaging}
              </span>
            )}
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex items-center justify-end">
          <Button
            variant="ghost"
            size="sm"
            className="text-primary hover:text-primary hover:bg-primary/10"
          >
            <ShoppingBag className="h-4 w-4 mr-1" />
            Request Quote
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  )
}