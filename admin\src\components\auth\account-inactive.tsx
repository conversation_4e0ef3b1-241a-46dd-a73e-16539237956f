"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  AlertTriangle, 
  Database, 
  Settings, 
  ExternalLink, 
  RefreshCw,
  Shield,
  User,
  Clock
} from "lucide-react";

interface AccountInactiveProps {
  adminEmail?: string;
  onRetry?: () => void;
  isRetrying?: boolean;
}

export function AccountInactive({ adminEmail, onRetry, isRetrying }: AccountInactiveProps) {
  const convexDashboardUrl = process.env.NEXT_PUBLIC_CONVEX_URL?.replace('https://', 'https://dashboard.convex.dev/t/') || 'https://dashboard.convex.dev';

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl shadow-lg border-0 bg-white/80 backdrop-blur-sm dark:bg-slate-900/80">
        <CardHeader className="text-center space-y-4 pb-6">
          <div className="mx-auto w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-orange-600 dark:text-orange-400" />
          </div>
          <div className="space-y-2">
            <CardTitle className="text-2xl font-bold text-slate-900 dark:text-slate-100">
              Account Inactive
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400 text-base">
              Your admin account is currently inactive and requires manual activation
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Account Info */}
          {adminEmail && (
            <div className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border">
              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-slate-500" />
                <div>
                  <p className="font-medium text-slate-900 dark:text-slate-100">Account Email</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">{adminEmail}</p>
                </div>
              </div>
              <Badge variant="secondary" className="bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800">
                <Clock className="w-3 h-3 mr-1" />
                Inactive
              </Badge>
            </div>
          )}

          {/* Alert Message */}
          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20">
            <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            <AlertDescription className="text-orange-800 dark:text-orange-200">
              <strong>Manual Activation Required:</strong> Your account needs to be activated by a system administrator through the Convex dashboard.
            </AlertDescription>
          </Alert>

          {/* Instructions */}
          <div className="space-y-4">
            <h3 className="font-semibold text-slate-900 dark:text-slate-100 flex items-center">
              <Settings className="w-5 h-5 mr-2 text-slate-600 dark:text-slate-400" />
              Activation Instructions
            </h3>
            
            <div className="space-y-3 pl-7">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">1</span>
                </div>
                <div>
                  <p className="font-medium text-slate-900 dark:text-slate-100">Access Convex Dashboard</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Go to the Convex dashboard and navigate to your project
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">2</span>
                </div>
                <div>
                  <p className="font-medium text-slate-900 dark:text-slate-100">Navigate to Admins Table</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Find the "admins" table in your database schema
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">3</span>
                </div>
                <div>
                  <p className="font-medium text-slate-900 dark:text-slate-100">Update isActive Field</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Locate your admin record and set the <code className="bg-slate-200 dark:bg-slate-700 px-1 py-0.5 rounded text-xs">isActive</code> field to <code className="bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-1 py-0.5 rounded text-xs">true</code>
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold text-blue-600 dark:text-blue-400">4</span>
                </div>
                <div>
                  <p className="font-medium text-slate-900 dark:text-slate-100">Save Changes</p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Save the changes and return to this page to retry login
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button 
              onClick={onRetry}
              disabled={isRetrying}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Checking Status...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry Login
                </>
              )}
            </Button>
            
            <Button 
              variant="outline" 
              asChild
              className="flex-1 border-slate-300 dark:border-slate-600"
            >
              <a 
                href={convexDashboardUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center justify-center"
              >
                <Database className="w-4 h-4 mr-2" />
                Open Convex Dashboard
                <ExternalLink className="w-3 h-3 ml-1" />
              </a>
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-center pt-4 border-t border-slate-200 dark:border-slate-700">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              Need help? Contact your system administrator or check the documentation.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}