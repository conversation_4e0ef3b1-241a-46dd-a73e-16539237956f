import FeaturedProductsClient from "./featured-products-client"
import type { Product } from "@/lib/types"
import { getFeaturedProducts } from "@/lib/api-client"

export default async function FeaturedProductsServer() {
  let products: Product[] = []
  let error = null

  try {
    products = await getFeaturedProducts()
  } catch (err) {
    console.error("Error fetching featured products:", err)
    error = err instanceof Error ? err.message : "Failed to load products"
  }

  return <FeaturedProductsClient initialProducts={products} error={error} />
}