import { EmailTemplate } from './email-templates'

export interface EmailOptions {
  to: string
  subject: string
  template: string
  data?: Record<string, any>
}

export class EmailService {
  private static instance: EmailService
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  }

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  async sendEmail(options: EmailOptions): Promise<{ success: boolean; error?: string }> {
    try {
      // In production, this would integrate with your email service (SendGrid, AWS SES, etc.)
      // For now, we'll simulate the email sending and log the template
      
      const template = EmailTemplate.getTemplate(options.template, options.data || {})
      
      console.log('📧 Email would be sent:')
      console.log('To:', options.to)
      console.log('Subject:', options.subject)
      console.log('Template:', options.template)
      console.log('HTML Content:', template.html)
      console.log('Text Content:', template.text)
      
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      return { success: true }
    } catch (error) {
      console.error('Email sending error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  async sendPasswordResetEmail(email: string, resetToken: string): Promise<{ success: boolean; error?: string }> {
    const resetLink = `${this.baseUrl}/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`
    
    return this.sendEmail({
      to: email,
      subject: 'Reset Your Benzochem Industries Password',
      template: 'password-reset',
      data: {
        resetLink,
        email,
        expiryTime: '15 minutes'
      }
    })
  }

  async sendWelcomeEmail(email: string, firstName: string): Promise<{ success: boolean; error?: string }> {
    return this.sendEmail({
      to: email,
      subject: 'Welcome to Benzochem Industries',
      template: 'welcome',
      data: {
        firstName,
        email,
        loginLink: `${this.baseUrl}/login`,
        supportEmail: '<EMAIL>'
      }
    })
  }

  async sendAccountStatusEmail(
    email: string, 
    firstName: string, 
    status: 'approved' | 'rejected' | 'pending'
  ): Promise<{ success: boolean; error?: string }> {
    const subjects = {
      approved: 'Your Benzochem Industries Account Has Been Approved',
      rejected: 'Update on Your Benzochem Industries Application',
      pending: 'Your Benzochem Industries Application is Under Review'
    }

    return this.sendEmail({
      to: email,
      subject: subjects[status],
      template: 'account-status',
      data: {
        firstName,
        email,
        status,
        loginLink: `${this.baseUrl}/login`,
        contactLink: `${this.baseUrl}/contact`,
        supportEmail: '<EMAIL>'
      }
    })
  }

  async sendQuotationEmail(
    email: string,
    firstName: string,
    quotationId: string,
    type: 'created' | 'updated' | 'approved' | 'rejected'
  ): Promise<{ success: boolean; error?: string }> {
    const subjects = {
      created: 'Your Quotation Request Has Been Received',
      updated: 'Your Quotation Has Been Updated',
      approved: 'Your Quotation Has Been Approved',
      rejected: 'Update on Your Quotation Request'
    }

    return this.sendEmail({
      to: email,
      subject: subjects[type],
      template: 'quotation',
      data: {
        firstName,
        email,
        quotationId,
        type,
        quotationLink: `${this.baseUrl}/quotation`,
        supportEmail: '<EMAIL>'
      }
    })
  }

  async sendContactFormEmail(
    name: string,
    email: string,
    message: string
  ): Promise<{ success: boolean; error?: string }> {
    return this.sendEmail({
      to: '<EMAIL>',
      subject: `New Contact Form Submission from ${name}`,
      template: 'contact-form',
      data: {
        name,
        email,
        message,
        timestamp: new Date().toLocaleString()
      }
    })
  }

  async sendNewsletterEmail(
    email: string,
    firstName: string,
    content: {
      title: string
      excerpt: string
      articles: Array<{ title: string; link: string; excerpt: string }>
    }
  ): Promise<{ success: boolean; error?: string }> {
    return this.sendEmail({
      to: email,
      subject: content.title,
      template: 'newsletter',
      data: {
        firstName,
        email,
        ...content,
        unsubscribeLink: `${this.baseUrl}/unsubscribe?email=${encodeURIComponent(email)}`,
        websiteLink: this.baseUrl
      }
    })
  }
}

export const emailService = EmailService.getInstance()