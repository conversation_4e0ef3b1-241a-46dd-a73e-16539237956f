import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Enhanced password validation with timing attack protection
function verifyPassword(password: string, storedPassword: string): boolean {
  // Use constant-time comparison to prevent timing attacks
  if (password.length !== storedPassword.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < password.length; i++) {
    result |= password.charCodeAt(i) ^ storedPassword.charCodeAt(i);
  }
  
  return result === 0;
}

// Enhanced admin authentication with security logging
export const authenticateAdmin = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Input validation
      if (!args.email || !args.password) {
        await logSecurityEvent(ctx, {
          eventType: "invalid_login_attempt",
          severity: "medium",
          description: "Login attempt with missing credentials",
          ipAddress: args.ipAddress,
          userAgent: args.userAgent,
        });
        return { success: false, error: "Email and password are required" };
      }

      // Find admin by email
      const admin = await ctx.db
        .query("admins")
        .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase().trim()))
        .first();

      if (!admin) {
        await logSecurityEvent(ctx, {
          eventType: "invalid_login_attempt",
          severity: "medium",
          description: `Login attempt for non-existent admin: ${args.email}`,
          ipAddress: args.ipAddress,
          userAgent: args.userAgent,
        });
        return { success: false, error: "Invalid email or password" };
      }

      // Check if admin is active
      if (!admin.isActive) {
        await logSecurityEvent(ctx, {
          eventType: "inactive_admin_login",
          severity: "high",
          description: `Login attempt for inactive admin: ${args.email}`,
          ipAddress: args.ipAddress,
          userAgent: args.userAgent,
        });
        return { success: false, error: "Account is inactive" };
      }

      // Verify password
      if (!verifyPassword(args.password, admin.password)) {
        await logSecurityEvent(ctx, {
          eventType: "failed_login_attempt",
          severity: "medium",
          description: `Failed login attempt for admin: ${args.email}`,
          ipAddress: args.ipAddress,
          userAgent: args.userAgent,
        });
        return { success: false, error: "Invalid email or password" };
      }

      // Log successful login
      await logSecurityEvent(ctx, {
        eventType: "successful_login",
        severity: "low",
        description: `Successful login for admin: ${args.email}`,
        ipAddress: args.ipAddress,
        userAgent: args.userAgent,
      });

      // Update last login timestamp
      await ctx.db.patch(admin._id, {
        updatedAt: Date.now(),
      });

      // Return admin data (without password)
      const { password, ...adminData } = admin;
      return {
        success: true,
        admin: {
          ...adminData,
          permissions: adminData.permissions || [],
          role: adminData.role || "admin",
        },
      };
    } catch (error) {
      console.error("Authentication error:", error);
      await logSecurityEvent(ctx, {
        eventType: "authentication_error",
        severity: "high",
        description: `Authentication system error: ${error}`,
        ipAddress: args.ipAddress,
        userAgent: args.userAgent,
      });
      return { success: false, error: "Authentication failed" };
    }
  },
});

// Enhanced admin creation with validation
export const createAdmin = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    role: v.optional(v.union(v.literal("admin"), v.literal("super_admin"))),
    permissions: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    try {
      // Input validation
      const email = args.email.toLowerCase().trim();
      if (!email || !args.password || !args.firstName || !args.lastName) {
        return { success: false, error: "All fields are required" };
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return { success: false, error: "Invalid email format" };
      }

      // Password strength validation (minimum requirements)
      if (args.password.length < 8) {
        return { success: false, error: "Password must be at least 8 characters long" };
      }

      // Check if admin already exists
      const existingAdmin = await ctx.db
        .query("admins")
        .withIndex("by_email", (q) => q.eq("email", email))
        .first();

      if (existingAdmin) {
        return { success: false, error: "Admin with this email already exists" };
      }

      // Create admin with enhanced data
      const adminId = await ctx.db.insert("admins", {
        email,
        password: args.password, // Manual password entry as per requirement
        firstName: args.firstName.trim(),
        lastName: args.lastName.trim(),
        role: args.role || "admin",
        permissions: args.permissions || [
          "users:read",
          "users:write",
          "products:read",
          "collections:read",
          "quotations:read",
          "quotations:write",
        ],
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      return { success: true, adminId };
    } catch (error) {
      console.error("Create admin error:", error);
      return { success: false, error: "Failed to create admin" };
    }
  },
});

// Enhanced session validation with security checks
export const validateAdminSession = query({
  args: {
    adminId: v.id("admins"),
  },
  handler: async (ctx, args) => {
    try {
      const admin = await ctx.db.get(args.adminId);
      if (!admin) {
        return { valid: false, error: "Admin not found" };
      }

      if (!admin.isActive) {
        return { valid: false, error: "Admin account is inactive" };
      }

      // Return admin data (without password)
      const { password, ...adminData } = admin;
      return { 
        valid: true, 
        admin: {
          ...adminData,
          permissions: adminData.permissions || [],
          role: adminData.role || "admin",
        }
      };
    } catch (error) {
      console.error("Session validation error:", error);
      return { valid: false, error: "Session validation failed" };
    }
  },
});


// Security event logging helper
async function logSecurityEvent(ctx: any, event: {
  eventType: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  ipAddress?: string;
  userAgent?: string;
}) {
  try {
    await ctx.db.insert("securityEvents", {
      eventType: event.eventType as any,
      severity: event.severity,
      description: event.description,
      details: {
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        timestamp: Date.now(),
      },
      status: "open",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  } catch (error) {
    console.error("Failed to log security event:", error);
  }
}

// Admin password update (for manual password changes)
export const updateAdminPassword = mutation({
  args: {
    adminId: v.id("admins"),
    newPassword: v.string(),
    updatedBy: v.id("admins"),
  },
  handler: async (ctx, args) => {
    try {
      // Validate new password
      if (args.newPassword.length < 8) {
        return { success: false, error: "Password must be at least 8 characters long" };
      }

      // Update password
      await ctx.db.patch(args.adminId, {
        password: args.newPassword,
        updatedAt: Date.now(),
      });

      // Log password change
      await logSecurityEvent(ctx, {
        eventType: "password_changed",
        severity: "medium",
        description: `Password updated for admin ID: ${args.adminId}`,
      });

      return { success: true };
    } catch (error) {
      console.error("Password update error:", error);
      return { success: false, error: "Failed to update password" };
    }
  },
});

// Admin status management
export const updateAdminStatus = mutation({
  args: {
    adminId: v.id("admins"),
    isActive: v.boolean(),
    updatedBy: v.id("admins"),
  },
  handler: async (ctx, args) => {
    try {
      await ctx.db.patch(args.adminId, {
        isActive: args.isActive,
        updatedAt: Date.now(),
      });

      await logSecurityEvent(ctx, {
        eventType: args.isActive ? "admin_activated" : "admin_deactivated",
        severity: "medium",
        description: `Admin ${args.isActive ? "activated" : "deactivated"}: ${args.adminId}`,
      });

      return { success: true };
    } catch (error) {
      console.error("Admin status update error:", error);
      return { success: false, error: "Failed to update admin status" };
    }
  },
});


