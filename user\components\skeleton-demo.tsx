"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ProductDetailWrapper from "@/components/product-detail-wrapper";
import EnhancedProductDetailSkeleton from "@/components/enhanced-product-detail-skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function SkeletonDemo() {
  const [showSkeleton, setShowSkeleton] = useState(true);

  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Product Detail Skeleton Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <Button 
              onClick={() => setShowSkeleton(true)}
              variant={showSkeleton ? "default" : "outline"}
            >
              Show Skeleton
            </Button>
            <Button 
              onClick={() => setShowSkeleton(false)}
              variant={!showSkeleton ? "default" : "outline"}
            >
              Hide Skeleton
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground mb-4">
            {showSkeleton 
              ? "Displaying the enhanced skeleton loader with modern animations"
              : "Skeleton hidden - would show actual product content here"
            }
          </div>
        </CardContent>
      </Card>

      {showSkeleton && <EnhancedProductDetailSkeleton />}
    </div>
  );
}