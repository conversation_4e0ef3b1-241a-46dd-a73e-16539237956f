// Secure quotation service that uses server-side storage instead of localStorage
// This service handles quotation data through API calls and secure cookies

interface QuotationItem {
  id: string;
  productId: string;
  variantId?: string;
  name: string;
  quantity: number;
  unit: string;
  specifications?: string;
  notes?: string;
  category?: string;
  image?: string;
  price?: number;
}

interface Quotation {
  id: string;
  items: QuotationItem[];
  status: "draft" | "submitted" | "under_review" | "quoted" | "approved" | "rejected" | "pending";
  submittedAt?: number;
  notes?: string;
  unreadMessageCount?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class SecureQuotationService {
  private static readonly API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';
  private static readonly ADMIN_API_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || '';
  private static readonly API_KEY = process.env.NEXT_PUBLIC_API_KEY || '';

  // Get default headers with API key
  private static getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.API_KEY) {
      headers['X-API-Key'] = this.API_KEY;
    }

    return headers;
  }

  // Get quotations from server (no localStorage)
  static async getQuotations(userId: string): Promise<Quotation[]> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations?userId=${userId}`, {
        method: 'GET',
        headers: this.getHeaders(),
        credentials: 'include', // Include cookies for authentication
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();
      
      if (result.success && result.data) {
        // Transform database quotations to match frontend interface
        const transformedQuotations: Quotation[] = result.data.map((dbQuotation: any) => {
          // Handle both legacy products and new lineItems
          let items: QuotationItem[] = [];
          
          if (dbQuotation.lineItems && dbQuotation.lineItems.length > 0) {
            // Use new lineItems structure
            items = dbQuotation.lineItems.map((item: any) => ({
              id: item.itemId || `item_${Date.now()}_${Math.random()}`,
              productId: item.productId,
              name: item.productName,
              quantity: item.quantity || 1,
              unit: item.unit || 'piece',
              specifications: item.specifications || '',
              notes: item.notes || '',
              category: 'Product',
              image: item.productImage || '',
              price: item.unitPrice || 0
            }));
          } else if (dbQuotation.products && dbQuotation.products.length > 0) {
            // Use legacy products structure
            items = dbQuotation.products.map((product: any) => ({
              id: product.productId || `item_${Date.now()}_${Math.random()}`,
              productId: product.productId,
              name: product.productName,
              quantity: parseInt(product.quantity) || 1,
              unit: product.unit || 'piece',
              specifications: product.specifications || '',
              notes: '',
              category: 'Product',
              image: product.productImage || '',
              price: 0
            }));
          }

          return {
            id: dbQuotation.id,
            items,
            status: dbQuotation.status || 'pending',
            submittedAt: dbQuotation.createdAt ? (typeof dbQuotation.createdAt === 'number' ? dbQuotation.createdAt : new Date(dbQuotation.createdAt).getTime()) : undefined,
            notes: dbQuotation.additionalRequirements || '',
            unreadMessageCount: 0
          };
        });
        
        return transformedQuotations;
      } else {
        console.warn('API returned unsuccessful result:', result.error || result.message);
        return [];
      }
    } catch (error) {
      console.error('Error fetching quotations:', error);
      return [];
    }
  }

  // Get current draft quotation from server
  static async getCurrentQuotation(userId: string): Promise<Quotation | null> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/current?userId=${userId}`, {
        method: 'GET',
        headers: this.getHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No current quotation
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Quotation> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching current quotation:', error);
      return null;
    }
  }

  // Create new quotation on server
  static async createNewQuotation(userId: string): Promise<Quotation> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/draft`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Quotation> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to create quotation');
      }
    } catch (error) {
      console.error('Error creating new quotation:', error);
      throw error;
    }
  }

  // Add item to quotation on server
  static async addToQuotation(userId: string, item: Omit<QuotationItem, "id">): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/items`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId, item }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Add to quotation error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to add item to quotation');
      }
    } catch (error) {
      console.error('Error adding item to quotation:', error);
      throw error;
    }
  }

  // Remove item from quotation on server
  static async removeFromQuotation(userId: string, itemId: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/items/${itemId}`, {
        method: 'DELETE',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to remove item from quotation');
      }
    } catch (error) {
      console.error('Error removing item from quotation:', error);
      throw error;
    }
  }

  // Update quotation item on server
  static async updateQuotationItem(userId: string, itemId: string, updates: Partial<QuotationItem>): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/items/${itemId}`, {
        method: 'PATCH',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId, updates }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to update quotation item');
      }
    } catch (error) {
      console.error('Error updating quotation item:', error);
      throw error;
    }
  }

  // Submit quotation to server
  static async submitQuotation(userId: string, notes?: string, urgency: "standard" | "urgent" | "asap" = "standard"): Promise<string> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/submit`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId, notes, urgency }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<{ quotationId: string }> = await response.json();
      
      if (result.success && result.data) {
        return result.data.quotationId;
      } else {
        throw new Error(result.error || 'Failed to submit quotation');
      }
    } catch (error) {
      console.error('Error submitting quotation:', error);
      throw error;
    }
  }

  // Clear current quotation on server
  static async clearCurrentQuotation(userId: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/current`, {
        method: 'DELETE',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to clear quotation');
      }
    } catch (error) {
      console.error('Error clearing quotation:', error);
      throw error;
    }
  }

  // Get quotation messages
  static async getQuotationMessages(quotationId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/${quotationId}/messages`, {
        method: 'GET',
        headers: this.getHeaders(),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();
      
      if (result.success && result.data) {
        return result.data;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error fetching quotation messages:', error);
      return [];
    }
  }

  // Send message in quotation thread
  static async sendMessage(quotationId: string, content: string, authorId: string, authorName: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/${quotationId}/messages`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({
          content,
          authorId,
          authorName,
          authorRole: 'user',
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Mark messages as read
  static async markMessagesAsRead(quotationId: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/${quotationId}/messages/read`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ readerRole: 'user' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to mark messages as read');
      }
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }

  // Grant permission for thread closure
  static async grantClosurePermission(quotationId: string, userId: string, userName: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/${quotationId}/closure-permission`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId, userName }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to grant closure permission');
      }
    } catch (error) {
      console.error('Error granting closure permission:', error);
      throw error;
    }
  }

  // Reject permission for thread closure
  static async rejectClosurePermission(quotationId: string, userId: string, userName: string, reason?: string): Promise<void> {
    try {
      const response = await fetch(`${this.ADMIN_API_URL}/api/v1/quotations/${quotationId}/reject-closure`, {
        method: 'POST',
        headers: this.getHeaders(),
        credentials: 'include',
        body: JSON.stringify({ userId, userName, reason }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to reject closure permission');
      }
    } catch (error) {
      console.error('Error rejecting closure permission:', error);
      throw error;
    }
  }
}

export default SecureQuotationService;
export type { Quotation, QuotationItem, ApiResponse };