// Utility functions for handling images

/**
 * Normalizes image URLs by removing remove.bg markers
 * @param url - The image URL to normalize
 * @returns The normalized URL
 */
export function normalizeImageUrl(url: string): string {
  if (!url) return url
  
  // Handle remove.bg processed images by removing the marker
  if (url.startsWith('data:image/removebg-')) {
    return url.replace('data:image/removebg-', 'data:image/')
  }
  
  return url
}

/**
 * Gets the first image URL from a product, handling both admin API and legacy formats
 * @param product - The product object
 * @returns The first image URL or a placeholder
 */
export function getProductImageUrl(product: any): string {
  // Handle new admin API format
  if (product.images && Array.isArray(product.images) && product.images.length > 0) {
    const firstImage = product.images[0]
    const url = typeof firstImage === 'string' ? firstImage : firstImage.url
    return normalizeImageUrl(url)
  }
  
  // Handle legacy Shopify format
  const legacyUrl = product.images?.edges?.[0]?.node?.url ||
                   product.media?.edges?.[0]?.node?.image?.url ||
                   product.featuredMedia?.preview?.image?.url
  
  return legacyUrl ? normalizeImageUrl(legacyUrl) : '/placeholder-chemical.jpg'
}

/**
 * Gets all image URLs from a product, handling both admin API and legacy formats
 * @param product - The product object
 * @returns Array of image URLs
 */
export function getProductImageUrls(product: any): string[] {
  let images: string[] = []
  
  // Handle new admin API format
  if (product.images && Array.isArray(product.images)) {
    images = product.images.map((img: any) => {
      const url = typeof img === 'string' ? img : img.url
      return normalizeImageUrl(url)
    }).filter(Boolean)
  }
  
  // Handle legacy Shopify format fallback
  if (images.length === 0) {
    const legacyImages = [
      product.featuredMedia?.preview?.image?.url,
      ...(product.media?.edges ? product.media.edges.map((edge: any) => edge.node.image.url) : [])
    ].filter(Boolean).map(normalizeImageUrl)
    images = legacyImages
  }

  // If no images are available, use a placeholder
  if (images.length === 0) {
    images.push('/placeholder-chemical.jpg')
  }

  return images
}

/**
 * Gets the category from a product, handling both admin API and legacy formats
 * @param product - The product object
 * @returns The category name
 */
export function getProductCategory(product: any): string {
  // Handle new admin API format
  if (product.collections && Array.isArray(product.collections) && product.collections.length > 0) {
    return product.collections[0]
  }
  
  // Handle legacy Shopify format
  return product.collections?.edges?.[0]?.node?.title || 'Chemical Products'
}