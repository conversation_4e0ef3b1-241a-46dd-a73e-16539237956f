"use client"

import { motion } from "framer-motion"
import { MapPin, Phone, Mail, Clock, Building, Globe, Users, Award } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const contactMethods = [
  {
    icon: Building,
    title: "Headquarters",
    description: "Our main office and manufacturing facility is located in Gujarat, India, with additional distribution centers across North America, Europe, and Asia.",
    details: []
  },
  {
    icon: MapPin,
    title: "Address",
    description: "",
    details: [
      "123 Chemical Lane, Industrial District",
      "Gujarat 380001",
      "India"
    ]
  },
  {
    icon: Phone,
    title: "Phone",
    description: "",
    details: [
      "Main: +91 (234) 567-890",
      "Customer Service: +91 (234) 567-891",
      "Technical Support: +91 (234) 567-892"
    ]
  },
  {
    icon: Mail,
    title: "Email",
    description: "",
    details: [
      "General Inquiries: <EMAIL>",
      "Sales: <EMAIL>",
      "Support: <EMAIL>"
    ]
  },
  {
    icon: Clock,
    title: "Business Hours",
    description: "",
    details: [
      "Monday - Friday: 9:00 AM - 6:00 PM IST",
      "Saturday: 9:00 AM - 1:00 PM IST",
      "Sunday: Closed"
    ]
  }
]

const quickStats = [
  {
    icon: Globe,
    value: "28+",
    label: "Years Experience",
    description: "Industry expertise"
  },
  {
    icon: Users,
    value: "500+",
    label: "Happy Clients",
    description: "Worldwide"
  },
  {
    icon: Award,
    value: "ISO",
    label: "Certified",
    description: "Quality assured"
  }
]

export default function ContactInfo() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: 0.2 }}
    >
      {/* Section Header */}
      <div className="mb-8">
        <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
          <Phone className="h-4 w-4" />
          Contact Information
        </div>
        <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
          Get In Touch
        </h2>
        <p className="text-lg text-muted-foreground leading-relaxed">
          Multiple ways to reach our team. We're here to help with your chemical needs.
        </p>
      </div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 0.3 }}
        className="grid grid-cols-3 gap-4 mb-8"
      >
        {quickStats.map((stat, index) => (
          <Card key={index} className="border-0 bg-card/30 backdrop-blur-sm text-center">
            <CardContent className="p-4">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                <stat.icon className="h-4 w-4 text-primary" />
              </div>
              <div className="text-lg font-bold text-foreground">{stat.value}</div>
              <div className="text-xs font-medium text-foreground">{stat.label}</div>
              <div className="text-xs text-muted-foreground">{stat.description}</div>
            </CardContent>
          </Card>
        ))}
      </motion.div>

      {/* Contact Methods */}
      <div className="space-y-6">
        {contactMethods.map((method, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
          >
            <Card className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300 group">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors">
                    <method.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-foreground mb-2">{method.title}</h3>
                    {method.description && (
                      <p className="text-muted-foreground mb-3 leading-relaxed">
                        {method.description}
                      </p>
                    )}
                    {method.details.length > 0 && (
                      <div className="space-y-1">
                        {method.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="text-sm text-muted-foreground">
                            {detail}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Additional Info Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="mt-8"
      >
        <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                <Award className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground mb-2">Quality Commitment</h3>
                <p className="text-muted-foreground text-sm leading-relaxed mb-3">
                  ISO certified facility with 24/7 quality monitoring. All products come with certificates of analysis and technical support.
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="text-xs">ISO 9001:2015</Badge>
                  <Badge variant="secondary" className="text-xs">24/7 Support</Badge>
                  <Badge variant="secondary" className="text-xs">Global Shipping</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
