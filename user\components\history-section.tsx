"use client"

import { motion } from "framer-motion"
import { History, TrendingUp, Award, Globe } from "lucide-react"
import AlternatingTimeline from "@/components/alternating-timeline"
import { Card, CardContent } from "@/components/ui/card"

const historyStats = [
  {
    icon: History,
    value: "28+",
    label: "Years of Excellence",
    description: "Serving industries worldwide"
  },
  {
    icon: TrendingUp,
    value: "6",
    label: "Major Milestones",
    description: "Key achievements in our journey"
  },
  {
    icon: Award,
    value: "ISO",
    label: "Quality Certified",
    description: "International standards compliance"
  },
  {
    icon: Globe,
    value: "Global",
    label: "Market Presence",
    description: "Serving customers worldwide"
  }
]

const timelineEvents = [
  {
    year: "1995",
    title: "Company Founded",
    category: "Foundation",
    description:
      "Benzochem Industries was established with a vision to provide high-quality chemical products for industrial applications. Starting with a small team and big dreams, we laid the foundation for what would become a leading chemical trading company.",
    highlights: [
      "Established initial operations in Gujarat",
      "Formed partnerships with key suppliers",
      "Focused on powder chemical products"
    ]
  },
  {
    year: "2003",
    title: "Research Center Opened",
    category: "Innovation",
    description:
      "We opened our state-of-the-art research and development center to drive innovation in chemical formulations. This marked our commitment to continuous improvement and product development.",
    highlights: [
      "Invested in modern laboratory equipment",
      "Hired specialized R&D team",
      "Began custom formulation services"
    ]
  },
  {
    year: "2008",
    title: "International Expansion",
    category: "Growth",
    description:
      "Expanded operations to serve international markets, establishing distribution centers in Europe and Asia. This milestone marked our transformation from a regional to a global player.",
    highlights: [
      "Opened European distribution center",
      "Established Asian market presence",
      "Achieved international quality certifications"
    ]
  },
  {
    year: "2015",
    title: "ISO Certification",
    category: "Quality",
    description:
      "Achieved ISO 9001:2015 certification, demonstrating our commitment to quality management systems and continuous improvement in all business processes.",
    highlights: [
      "Implemented comprehensive QMS",
      "Enhanced customer satisfaction metrics",
      "Streamlined operational processes"
    ]
  },
  {
    year: "2020",
    title: "Sustainability Initiative",
    category: "Environment",
    description:
      "Launched our comprehensive sustainability program, focusing on reducing environmental impact across all operations while maintaining product excellence.",
    highlights: [
      "Reduced carbon footprint by 30%",
      "Implemented green packaging solutions",
      "Achieved zero-waste manufacturing"
    ]
  },
  {
    year: "2023",
    title: "Digital Transformation",
    category: "Technology",
    description:
      "Implemented advanced digital solutions to enhance customer experience and streamline operations. This digital-first approach revolutionized how we serve our customers.",
    highlights: [
      "Launched online quotation system",
      "Implemented AI-powered inventory management",
      "Enhanced customer portal with real-time tracking"
    ]
  },
]

export default function HistorySection() {
  return (
    <section className="py-20 md:py-28 bg-vanilla-gradient dark:bg-dark-gradient relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-transparent to-secondary/10" />
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <History className="h-4 w-4" />
            Our Journey
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Our History
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            From our humble beginnings to becoming an industry leader, our journey has been defined by innovation,
            excellence, and an unwavering commitment to quality.
          </p>
        </motion.div>

        {/* History Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {historyStats.map((stat, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Enhanced Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <AlternatingTimeline events={timelineEvents} />
        </motion.div>

        {/* Future Vision */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-20"
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-semibold mb-6 text-foreground">
                Looking Ahead
              </h3>
              <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                As we continue our journey, we remain committed to innovation, sustainability, and excellence. 
                Our vision for the future includes expanding our global reach, developing cutting-edge chemical solutions, 
                and maintaining our position as a trusted partner in the chemical industry.
              </p>
              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Sustainable Growth
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Innovation Focus
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Global Expansion
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Customer Excellence
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}