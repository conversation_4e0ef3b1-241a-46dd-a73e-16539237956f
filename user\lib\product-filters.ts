import type { Product } from './types'

// Helper function to filter products by category type
export function filterProductsByCategory(products: Product[], category: 'powder' | 'liquid'): Product[] {
  console.log(`🔍 Filtering ${products.length} products for category: ${category}`);
  
  const filteredProducts = products.filter(product => {
    const title = product.title.toLowerCase();
    const description = product.description.toLowerCase();
    const tags = product.tags.map(tag => tag.toLowerCase());
    
    // Check collections first
    const collections = product.collections.edges.map(edge => edge.node.title.toLowerCase());
    const hasMatchingCollection = collections.some(collection => 
      collection.includes(category)
    );
    
    console.log(`📦 Product: "${product.title}" - Collections: [${collections.join(', ')}] - Has matching collection: ${hasMatchingCollection}`);
    
    if (hasMatchingCollection) {
      console.log(`✅ "${product.title}" included via collection match`);
      return true;
    }
    
    // If no matching collection, filter by content
    if (category === 'powder') {
      const isPowderByContent = (
        title.includes('powder') ||
        description.includes('powder') ||
        tags.some(tag => tag.includes('powder')) ||
        // Common powder chemical forms
        title.includes('oxide') ||
        title.includes('carbonate') ||
        title.includes('sulfate') ||
        title.includes('chloride') ||
        title.includes('nitrate') ||
        title.includes('phosphate') ||
        description.includes('crystalline') ||
        description.includes('granular') ||
        description.includes('dry')
      );
      
      const hasLiquidIndicators = (
        title.includes('liquid') || 
        title.includes('solution') || 
        title.includes('acid') || 
        description.includes('liquid') ||
        description.includes('aqueous')
      );
      
      const result = isPowderByContent && !hasLiquidIndicators;
      console.log(`🧪 "${product.title}" - Powder content: ${isPowderByContent}, Liquid indicators: ${hasLiquidIndicators}, Result: ${result}`);
      return result;
      
    } else if (category === 'liquid') {
      const isLiquidByContent = (
        title.includes('liquid') ||
        description.includes('liquid') ||
        tags.some(tag => tag.includes('liquid')) ||
        // Common liquid chemical forms
        title.includes('solution') ||
        title.includes('acid') ||
        title.includes('base') ||
        title.includes('solvent') ||
        title.includes('oil') ||
        title.includes('concentrate') ||
        description.includes('aqueous') ||
        description.includes('solution') ||
        description.includes('concentrate')
      );
      
      const hasPowderIndicators = (
        title.includes('powder') || 
        title.includes('crystalline') || 
        title.includes('granular') ||
        description.includes('powder') ||
        description.includes('dry')
      );
      
      const result = isLiquidByContent && !hasPowderIndicators;
      console.log(`💧 "${product.title}" - Liquid content: ${isLiquidByContent}, Powder indicators: ${hasPowderIndicators}, Result: ${result}`);
      return result;
    }
    
    console.log(`❌ "${product.title}" excluded - no category match`);
    return false;
  });
  
  console.log(`📊 Final result: ${filteredProducts.length} products for category ${category}`);
  return filteredProducts;
}

// Get products by category with smart filtering
export async function getProductsByCategory(category: 'powder' | 'liquid'): Promise<Product[]> {
  const { apiClient, transformApiProductToProduct } = await import('./api-client');
  
  console.log(`🚀 Starting getProductsByCategory for: ${category}`);
  
  // First try to get products by collection
  const collectionNames = category === 'powder' 
    ? ["powder", "Powder", "Powder Products", "powders", "Powders"]
    : ["liquid", "Liquid", "Liquid Products", "liquids", "Liquids"];
  
  for (const collectionName of collectionNames) {
    console.log(`🔍 Trying collection: "${collectionName}"`);
    const response = await apiClient.getProducts({ collection: collectionName, limit: 100 });
    console.log(`📡 Collection "${collectionName}" response:`, {
      success: response.success,
      dataLength: response.data ? response.data.length : 0,
      error: response.error
    });
    
    if (response.success && response.data && Array.isArray(response.data) && response.data.length > 0) {
      const products = response.data.map(transformApiProductToProduct);
      console.log(`✅ Found ${products.length} products for collection "${collectionName}"`);
      return products;
    }
  }
  
  // If no collection-based results, get all products and filter
  console.log(`⚠️ No collection-based results found, filtering all products for category: ${category}`);
  const response = await apiClient.getProducts({ limit: 100 });
  console.log(`📡 All products response:`, {
    success: response.success,
    dataLength: response.data ? response.data.length : 0,
    error: response.error
  });
  
  if (response.success && response.data && Array.isArray(response.data)) {
    const allProducts = response.data.map(transformApiProductToProduct);
    console.log(`🔄 About to filter ${allProducts.length} products for category: ${category}`);
    
    // For liquid category, if no collection match and no content-based matches, 
    // let's be more lenient and show products that are NOT clearly powder
    if (category === 'liquid') {
      const filteredProducts = filterProductsByCategory(allProducts, category);
      
      if (filteredProducts.length === 0) {
        console.log(`⚠️ No liquid products found with strict filtering, trying lenient approach`);
        // More lenient approach: exclude only obvious powder products
        const lenientLiquidProducts = allProducts.filter(product => {
          const title = product.title.toLowerCase();
          const description = product.description.toLowerCase();
          
          // Exclude obvious powder products
          const isObviousPowder = (
            title.includes('powder') ||
            description.includes('powder') ||
            title.includes('crystalline') ||
            title.includes('granular') ||
            description.includes('crystalline') ||
            description.includes('granular')
          );
          
          console.log(`🧪 "${product.title}" - Is obvious powder: ${isObviousPowder}`);
          return !isObviousPowder;
        });
        
        console.log(`📊 Lenient filtering result: ${lenientLiquidProducts.length} products`);
        return lenientLiquidProducts;
      }
      
      return filteredProducts;
    } else {
      const filteredProducts = filterProductsByCategory(allProducts, category);
      return filteredProducts;
    }
  }
  
  console.warn(`❌ No products found for category: ${category}`);
  return [];
}