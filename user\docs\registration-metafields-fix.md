# Registration Form Metafields and Marketing Consent Fix

## Issues Fixed

1. **<PERSON>afields not displaying in Shopify Admin** - Business information and GST data not being stored
2. **Email and SMS consent not applying** - Marketing preferences not being saved to customer records
3. **Missing business data storage** - Registration form data not being persisted as metafields

## Changes Made

### 1. Updated Auth Context (`contexts/shopify-auth-context.tsx`)

#### **Enhanced Register Function Interface**
```typescript
register: (userData: {
  firstName: string
  lastName: string
  email: string
  password: string
  phone: string
  businessName?: string
  gstNumber?: string
  // Business information
  legalNameOfBusiness?: string
  tradeName?: string
  dateOfRegistration?: string
  constitutionOfBusiness?: string
  taxpayerType?: string
  principalPlaceOfBusiness?: string
  natureOfCoreBusinessActivity?: string
  // Marketing consent
  agreedToEmailMarketing?: boolean
  agreedToSmsMarketing?: boolean
}) => Promise<{ success: boolean; error?: string; message?: string }>
```

#### **Enhanced Registration Logic**
- **Step 1**: Create customer account with marketing consent
- **Step 2**: Store GST information as metafields using `updateCustomerGSTInfo()`
- **Step 3**: Store business preferences and marketing consent as metafields using `updateCustomerBusinessPreferences()`
- **Added detailed logging** for debugging metafield storage

### 2. Updated Registration Form (`components/shopify-register-form.tsx`)

#### **Enhanced Data Passing**
Now passes all business information and marketing consent to the register function:
```typescript
const result = await register({
  firstName: formData.firstName,
  lastName: formData.lastName,
  email: formData.email,
  password: formData.password,
  phone: `${formData.countryCode}${formData.phone}`,
  businessName: formData.legalNameOfBusiness || formData.tradeName,
  gstNumber: formData.gstNumber,
  // Business information
  legalNameOfBusiness: formData.legalNameOfBusiness,
  tradeName: formData.tradeName,
  dateOfRegistration: formData.dateOfRegistration,
  constitutionOfBusiness: formData.constitutionOfBusiness,
  taxpayerType: formData.taxpayerType,
  principalPlaceOfBusiness: formData.principalPlaceOfBusiness,
  natureOfCoreBusinessActivity: formData.natureOfCoreBusinessActivity,
  // Marketing consent
  agreedToEmailMarketing: formData.agreedToEmailMarketing,
  agreedToSmsMarketing: formData.agreedToSmsMarketing,
})
```

### 3. Updated Customer Creation API (`app/api/shopify/send-customer-invite/route.ts`)

#### **Added Marketing Consent Support**
```typescript
const { 
  email, 
  firstName, 
  lastName, 
  phone, 
  password, 
  acceptsMarketing = false, 
  acceptsSmsMarketing = false 
} = await request.json()

// In customer creation
customer: {
  email,
  first_name: firstName,
  last_name: lastName,
  phone: phone || '',
  accepts_marketing: acceptsMarketing, // Apply email marketing consent
  accepts_sms_marketing: acceptsSmsMarketing, // Apply SMS marketing consent
  verified_email: true,
  password: password,
  password_confirmation: password,
  send_email_invite: false,
  send_email_welcome: true
}
```

### 4. Updated Invitation Utils (`lib/invitation-utils.ts`)

#### **Enhanced Function Signatures**
Added marketing consent parameters to:
- `sendCustomerInvitation()`
- `registerCustomerWithInvitation()`

## Metafields Storage Structure

### GST Information (Namespace: "custom")
- `gstin` - GST Number
- `legal_name_of_business` - Legal Name of Business
- `trade_name` - Trade Name
- `date_of_registration` - Date of Registration
- `constitution_of_business` - Constitution of Business
- `taxpayer_type` - Taxpayer Type
- `principal_place_of_business_address` - Business Address
- `nature_of_core_business_activity` - Business Activity

### Business Preferences (Namespace: "business")
- `business_type` - "B2B"
- `industry_type` - "Chemicals"
- `business_category` - "Trading" or "Individual"
- `notes` - Marketing consent information

## How to Verify the Fix

### 1. Test Registration Process
1. Go to `/register`
2. Fill in personal information (first tab)
3. Fill in business information including GST details (second tab)
4. Check both marketing consent checkboxes
5. Click "Complete Registration"

### 2. Check Browser Console
Look for these log messages:
```
Customer created successfully, storing metafields for: gid://shopify/Customer/123
Storing GST information for customer: gid://shopify/Customer/123
GST data: { gstNumber: "...", legalNameOfBusiness: "...", ... }
✅ GST information stored successfully
Storing business preferences and marketing consent for customer: gid://shopify/Customer/123
Marketing consent: { email: true, sms: true }
✅ Business preferences and marketing consent stored successfully
```

### 3. Verify in Shopify Admin

#### **Customer Record**
1. Go to Shopify Admin > Customers
2. Find the newly created customer
3. Check customer details:
   - **Marketing**: Should show email/SMS consent status
   - **Tags**: May show business-related tags

#### **Metafields**
1. In the customer detail page, scroll down to "Metafields" section
2. Look for:
   - **Custom namespace**: GST and business information
   - **Business namespace**: Business preferences and marketing consent

### 4. Use Admin Test Page
1. Go to `/admin/metafields-test`
2. Enter the customer ID (format: `gid://shopify/Customer/*********`)
3. Click "Get All Metafields" to see stored data
4. Click "Get GST Info" to see GST-specific data

### 5. API Testing
Use the test API endpoint:
```javascript
// Get customer metafields
fetch('/api/test-metafields', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'get',
    customerId: 'gid://shopify/Customer/*********'
  })
})
.then(res => res.json())
.then(console.log)
```

## Troubleshooting

### If Metafields Are Not Showing
1. **Check Console Logs**: Look for error messages during registration
2. **Verify Customer ID**: Ensure the customer was created successfully
3. **Check API Permissions**: Verify Shopify Admin API has metafield permissions
4. **Test Manually**: Use the admin test page to create metafields directly

### If Marketing Consent Not Applied
1. **Check Customer Record**: Look at the customer's marketing status in Shopify Admin
2. **Verify API Parameters**: Check that `accepts_marketing` and `accepts_sms_marketing` are being sent
3. **Check Form Values**: Ensure checkboxes are properly bound to form state

### Common Issues
1. **Customer ID Format**: Must be `gid://shopify/Customer/*********`
2. **Metafield Types**: Ensure correct types are used (e.g., `single_line_text_field`)
3. **API Rate Limits**: Shopify may rate limit metafield operations
4. **Permissions**: Admin API token needs metafield read/write permissions

## Expected Results

After successful registration:
1. **Customer created** with immediate account activation
2. **Welcome email sent** (not invitation email)
3. **Marketing consent applied** to customer record
4. **GST information stored** as metafields in "custom" namespace
5. **Business preferences stored** as metafields in "business" namespace
6. **All data visible** in Shopify Admin customer record

## Testing Checklist

- [ ] Registration form accepts all business information
- [ ] Marketing consent checkboxes work properly
- [ ] Customer account created successfully
- [ ] Welcome email received (check spam folder)
- [ ] Console shows successful metafield storage
- [ ] Shopify Admin shows customer with marketing consent
- [ ] Metafields visible in Shopify Admin customer record
- [ ] Admin test page can retrieve stored metafields
- [ ] GST information properly formatted and stored
- [ ] Business preferences and marketing consent stored
