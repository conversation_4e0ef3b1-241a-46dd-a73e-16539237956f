/**
 * Real-time Notification Service
 * Handles creation and management of system notifications
 */

import { api } from "../../convex/_generated/api";
import { ConvexReactClient } from "convex/react";

export interface NotificationData {
  type: "user_registration" | "user_approval" | "user_rejection" | "product_update" | "system_alert" | "gst_verification" | "order_notification";
  title: string;
  message: string;
  recipientType: "admin" | "user" | "all_admins" | "specific_user";
  recipientId?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  relatedEntityType?: "user" | "product" | "order";
  relatedEntityId?: string;
  expiresAt?: number;
  createdBy?: string;
}

export class NotificationService {
  private convex: ConvexReactClient;

  constructor(convex: ConvexReactClient) {
    this.convex = convex;
  }

  /**
   * Create a new notification
   */
  async createNotification(data: NotificationData): Promise<string | null> {
    try {
      const notificationId = await this.convex.mutation(api.notifications.createNotification, {
        type: data.type,
        title: data.title,
        message: data.message,
        recipientType: data.recipientType,
        recipientId: data.recipientId as any,
        priority: data.priority || "medium",
        relatedEntityType: data.relatedEntityType,
        relatedEntityId: data.relatedEntityId,
        expiresAt: data.expiresAt,
        createdBy: data.createdBy as any,
      });

      console.log(`📢 Notification created: ${data.title}`);
      return notificationId;
    } catch (error) {
      console.error("Failed to create notification:", error);
      return null;
    }
  }

  /**
   * Create user registration notification
   */
  async notifyUserRegistration(userData: {
    firstName: string;
    lastName: string;
    email: string;
    businessName?: string;
    userId: string;
  }): Promise<string | null> {
    const businessInfo = userData.businessName ? ` from ${userData.businessName}` : '';
    
    return this.createNotification({
      type: "user_registration",
      title: "New User Registration",
      message: `${userData.firstName} ${userData.lastName}${businessInfo} has registered and is pending approval.`,
      recipientType: "all_admins",
      priority: "medium",
      relatedEntityType: "user",
      relatedEntityId: userData.userId,
    });
  }

  /**
   * Create user approval notification
   */
  async notifyUserApproval(userData: {
    firstName: string;
    lastName: string;
    email: string;
    userId: string;
    approvedBy: string;
    customMessage?: string;
  }): Promise<string | null> {
    const message = userData.customMessage || 
      `Your account has been approved by an administrator. You can now access all features.`;

    return this.createNotification({
      type: "user_approval",
      title: "Account Approved",
      message: message,
      recipientType: "specific_user",
      recipientId: userData.userId,
      priority: "high",
      relatedEntityType: "user",
      relatedEntityId: userData.userId,
      createdBy: userData.approvedBy,
    });
  }

  /**
   * Create user rejection notification
   */
  async notifyUserRejection(userData: {
    firstName: string;
    lastName: string;
    email: string;
    userId: string;
    rejectedBy: string;
    reason: string;
    customMessage?: string;
  }): Promise<string | null> {
    const message = userData.customMessage || 
      `Your account application has been declined. Reason: ${userData.reason}`;

    return this.createNotification({
      type: "user_rejection",
      title: "Account Application Declined",
      message: message,
      recipientType: "specific_user",
      recipientId: userData.userId,
      priority: "high",
      relatedEntityType: "user",
      relatedEntityId: userData.userId,
      createdBy: userData.rejectedBy,
    });
  }

  /**
   * Create quotation notification
   */
  async notifyNewQuotation(quotationData: {
    quotationId: string;
    userName: string;
    userEmail: string;
    businessName?: string;
    productCount: number;
    urgency?: string;
  }): Promise<string | null> {
    const businessInfo = quotationData.businessName ? ` from ${quotationData.businessName}` : '';
    const urgencyText = quotationData.urgency === 'urgent' ? ' (URGENT)' : 
                       quotationData.urgency === 'asap' ? ' (ASAP)' : '';
    
    return this.createNotification({
      type: "order_notification",
      title: `New Quotation Request${urgencyText}`,
      message: `${quotationData.userName}${businessInfo} has submitted a quotation request for ${quotationData.productCount} product${quotationData.productCount > 1 ? 's' : ''}.`,
      recipientType: "all_admins",
      priority: quotationData.urgency === 'urgent' || quotationData.urgency === 'asap' ? "urgent" : "high",
      relatedEntityType: "order",
      relatedEntityId: quotationData.quotationId,
    });
  }

  /**
   * Create product update notification
   */
  async notifyProductUpdate(productData: {
    productId: string;
    productName: string;
    updateType: string;
    updatedBy: string;
  }): Promise<string | null> {
    return this.createNotification({
      type: "product_update",
      title: "Product Updated",
      message: `${productData.productName} has been ${productData.updateType}.`,
      recipientType: "all_admins",
      priority: "low",
      relatedEntityType: "product",
      relatedEntityId: productData.productId,
      createdBy: productData.updatedBy,
    });
  }

  /**
   * Create system alert notification
   */
  async notifySystemAlert(alertData: {
    title: string;
    message: string;
    priority?: "low" | "medium" | "high" | "urgent";
    relatedEntityType?: "user" | "product" | "order";
    relatedEntityId?: string;
  }): Promise<string | null> {
    return this.createNotification({
      type: "system_alert",
      title: alertData.title,
      message: alertData.message,
      recipientType: "all_admins",
      priority: alertData.priority || "medium",
      relatedEntityType: alertData.relatedEntityType,
      relatedEntityId: alertData.relatedEntityId,
    });
  }

  /**
   * Create GST verification notification
   */
  async notifyGSTVerification(gstData: {
    userId: string;
    userName: string;
    businessName: string;
    gstNumber: string;
    status: "verified" | "failed";
  }): Promise<string | null> {
    const statusText = gstData.status === "verified" ? "verified successfully" : "verification failed";
    
    return this.createNotification({
      type: "gst_verification",
      title: `GST ${gstData.status === "verified" ? "Verified" : "Verification Failed"}`,
      message: `GST number ${gstData.gstNumber} for ${gstData.businessName} has ${statusText}.`,
      recipientType: "all_admins",
      priority: gstData.status === "failed" ? "high" : "medium",
      relatedEntityType: "user",
      relatedEntityId: gstData.userId,
    });
  }

  /**
   * Create bulk notifications for multiple recipients
   */
  async createBulkNotifications(notifications: NotificationData[]): Promise<(string | null)[]> {
    const results = await Promise.allSettled(
      notifications.map(notification => this.createNotification(notification))
    );

    return results.map(result => 
      result.status === "fulfilled" ? result.value : null
    );
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications(): Promise<string[]> {
    try {
      const deletedIds = await this.convex.mutation(api.notifications.deleteExpiredNotifications, {});
      console.log(`🧹 Cleaned up ${deletedIds.length} expired notifications`);
      return deletedIds;
    } catch (error) {
      console.error("Failed to cleanup expired notifications:", error);
      return [];
    }
  }
}

// Singleton instance
let notificationService: NotificationService | null = null;

export function getNotificationService(convex: ConvexReactClient): NotificationService {
  if (!notificationService) {
    notificationService = new NotificationService(convex);
  }
  return notificationService;
}

// Helper functions for common notification patterns
export const NotificationHelpers = {
  /**
   * Calculate expiration time (default: 30 days from now)
   */
  getExpirationTime(days: number = 30): number {
    return Date.now() + (days * 24 * 60 * 60 * 1000);
  },

  /**
   * Get priority based on urgency
   */
  getPriorityFromUrgency(urgency?: string): "low" | "medium" | "high" | "urgent" {
    switch (urgency) {
      case "urgent":
      case "asap":
        return "urgent";
      case "high":
        return "high";
      case "low":
        return "low";
      default:
        return "medium";
    }
  },

  /**
   * Format notification message with user-friendly text
   */
  formatUserName(firstName: string, lastName: string, businessName?: string): string {
    const fullName = `${firstName} ${lastName}`;
    return businessName ? `${fullName} from ${businessName}` : fullName;
  },

  /**
   * Generate notification title based on type and context
   */
  generateTitle(type: string, context?: any): string {
    switch (type) {
      case "user_registration":
        return "New User Registration";
      case "user_approval":
        return "Account Approved";
      case "user_rejection":
        return "Account Application Declined";
      case "product_update":
        return "Product Updated";
      case "system_alert":
        return context?.title || "System Alert";
      case "gst_verification":
        return context?.verified ? "GST Verified" : "GST Verification Failed";
      case "order_notification":
        return context?.urgent ? "Urgent Quotation Request" : "New Quotation Request";
      default:
        return "Notification";
    }
  }
};