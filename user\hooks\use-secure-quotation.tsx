"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import SecureQuotationService, { Quotation, QuotationItem } from "@/lib/quotation-service";
import { toast } from "sonner";

interface QuotationContextType {
  quotations: Quotation[];
  currentQuotation: Quotation | null;
  addToQuotation: (item: Omit<QuotationItem, "id">) => Promise<void>;
  removeFromQuotation: (itemId: string) => Promise<void>;
  updateQuotationItem: (itemId: string, updates: Partial<QuotationItem>) => Promise<void>;
  submitQuotation: (notes?: string, urgency?: "standard" | "urgent" | "asap") => Promise<void>;
  createNewQuotation: () => Promise<void>;
  loadQuotation: (quotationId: string) => Promise<void>;
  clearCurrentQuotation: () => Promise<void>;
  refreshQuotations: () => Promise<void>;
  sendMessage: (quotationId: string, content: string) => Promise<void>;
  getQuotationMessages: (quotationId: string) => Promise<any[]>;
  markMessagesAsRead: (quotationId: string) => Promise<void>;
  grantClosurePermission: (quotationId: string) => Promise<void>;
  rejectClosurePermission: (quotationId: string, reason?: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

const QuotationContext = createContext<QuotationContextType | null>(null);

export function SecureQuotationProvider({ children }: { children: React.ReactNode }) {
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [currentQuotation, setCurrentQuotation] = useState<Quotation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Initialize quotations when component mounts or user changes
  useEffect(() => {
    async function initQuotations() {
      if (!user?.id) {
        setQuotations([]);
        setCurrentQuotation(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch quotations from server
        const serverQuotations = await SecureQuotationService.getQuotations(user.id);
        setQuotations(serverQuotations);

        // Fetch current draft quotation
        const current = await SecureQuotationService.getCurrentQuotation(user.id);
        setCurrentQuotation(current);

      } catch (error) {
        console.error("Error initializing quotations:", error);
        setError("Failed to load quotations");
        setQuotations([]);
        setCurrentQuotation(null);
      } finally {
        setIsLoading(false);
      }
    }

    initQuotations();
  }, [user?.id]);

  const addToQuotation = async (item: Omit<QuotationItem, "id">) => {
    if (!user) {
      toast.error("Please log in to add items to quotation");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      await SecureQuotationService.addToQuotation(user.id, item);

      // Refresh current quotation
      const updated = await SecureQuotationService.getCurrentQuotation(user.id);
      setCurrentQuotation(updated);

      toast.success(
        <div className="flex items-center space-x-2">
          <span className="font-medium">{item.name}</span>
          <span className="text-sm text-gray-600">added to quotation</span>
        </div>
      );
    } catch (error) {
      console.error("Error adding item to quotation:", error);
      setError("Failed to add item to quotation");
      toast.error("Failed to add item to quotation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeFromQuotation = async (itemId: string) => {
    if (!user || !currentQuotation) return;

    try {
      setIsLoading(true);
      setError(null);

      await SecureQuotationService.removeFromQuotation(user.id, itemId);

      // Refresh current quotation
      const updated = await SecureQuotationService.getCurrentQuotation(user.id);
      setCurrentQuotation(updated);

      toast.success("Item removed from quotation");
    } catch (error) {
      console.error("Error removing item from quotation:", error);
      setError("Failed to remove item from quotation");
      toast.error("Failed to remove item from quotation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const updateQuotationItem = async (itemId: string, updates: Partial<QuotationItem>) => {
    if (!user || !currentQuotation) return;

    try {
      setIsLoading(true);
      setError(null);

      await SecureQuotationService.updateQuotationItem(user.id, itemId, updates);

      // Refresh current quotation
      const updated = await SecureQuotationService.getCurrentQuotation(user.id);
      setCurrentQuotation(updated);

    } catch (error) {
      console.error("Error updating quotation item:", error);
      setError("Failed to update quotation item");
      toast.error("Failed to update item. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const submitQuotation = async (notes?: string, urgency: "standard" | "urgent" | "asap" = "standard") => {
    if (!currentQuotation || currentQuotation.items.length === 0) {
      toast.error("Cannot submit empty quotation");
      return;
    }

    if (!user) {
      toast.error("Please log in to submit quotation");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const quotationId = await SecureQuotationService.submitQuotation(user.id, notes, urgency);

      // Refresh quotations and clear current
      const updatedQuotations = await SecureQuotationService.getQuotations(user.id);
      setQuotations(updatedQuotations);
      setCurrentQuotation(null);

      toast.success("Quotation submitted successfully! We'll get back to you soon.");
    } catch (error) {
      console.error("Error submitting quotation:", error);
      setError("Failed to submit quotation");
      
      if (error instanceof Error) {
        if (error.message.includes('Admin API error')) {
          toast.error(`Submission failed: ${error.message.replace('Admin API error: ', '')}`);
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error("Failed to submit quotation. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createNewQuotation = async () => {
    if (!user) {
      toast.error("Please log in to create quotation");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const newQuotation = await SecureQuotationService.createNewQuotation(user.id);
      setCurrentQuotation(newQuotation);

    } catch (error) {
      console.error("Error creating new quotation:", error);
      setError("Failed to create new quotation");
      toast.error("Failed to create new quotation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const loadQuotation = async (quotationId: string) => {
    if (!user) return;

    try {
      setIsLoading(true);
      setError(null);

      // Ensure quotations is defined before searching
      if (quotations && quotations.length > 0) {
        const quotation = quotations.find(q => q.id === quotationId);
        if (quotation && quotation.status === "draft") {
          setCurrentQuotation(quotation);
        }
      }
    } catch (error) {
      console.error("Error loading quotation:", error);
      setError("Failed to load quotation");
      toast.error("Failed to load quotation. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const clearCurrentQuotation = async () => {
    if (!user) return;

    try {
      await SecureQuotationService.clearCurrentQuotation(user.id);
      setCurrentQuotation(null);
    } catch (error) {
      console.error("Error clearing quotation:", error);
      setError("Failed to clear quotation");
    }
  };

  const refreshQuotations = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      setError(null);

      const updatedQuotations = await SecureQuotationService.getQuotations(user.id);
      setQuotations(updatedQuotations);

      toast.success("Quotations refreshed successfully");
    } catch (error) {
      console.error("Error refreshing quotations:", error);
      setError("Failed to refresh quotations");
      
      if (error instanceof Error) {
        console.error("Error refreshing quotations:", error.message);
        toast.warning("Unable to connect to admin system for refresh");
      } else {
        console.error("Unknown error refreshing quotations:", error);
        toast.error("Failed to refresh quotations");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async (quotationId: string, content: string) => {
    if (!user) {
      toast.error("Please log in to send messages");
      return;
    }

    try {
      await SecureQuotationService.sendMessage(
        quotationId, 
        content, 
        user.id, 
        `${user.firstName} ${user.lastName}`
      );
      
      toast.success("Message sent successfully");
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message. Please try again.");
    }
  };

  const getQuotationMessages = async (quotationId: string): Promise<any[]> => {
    try {
      return await SecureQuotationService.getQuotationMessages(quotationId);
    } catch (error) {
      console.error("Error fetching messages:", error);
      return [];
    }
  };

  const markMessagesAsRead = async (quotationId: string) => {
    try {
      await SecureQuotationService.markMessagesAsRead(quotationId);
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  };

  const grantClosurePermission = async (quotationId: string) => {
    if (!user) {
      toast.error("Please log in to grant permission");
      return;
    }

    try {
      await SecureQuotationService.grantClosurePermission(
        quotationId, 
        user.id, 
        `${user.firstName} ${user.lastName}`
      );
      
      toast.success("Thread closure permission granted");
      
      // Refresh quotations to update status
      await refreshQuotations();
    } catch (error) {
      console.error("Error granting closure permission:", error);
      toast.error("Failed to grant permission. Please try again.");
    }
  };

  const rejectClosurePermission = async (quotationId: string, reason?: string) => {
    if (!user) {
      toast.error("Please log in to reject permission");
      return;
    }

    try {
      await SecureQuotationService.rejectClosurePermission(
        quotationId, 
        user.id, 
        `${user.firstName} ${user.lastName}`,
        reason
      );
      
      toast.success("Thread closure permission rejected");
      
      // Refresh quotations to update status
      await refreshQuotations();
    } catch (error) {
      console.error("Error rejecting closure permission:", error);
      toast.error("Failed to reject permission. Please try again.");
    }
  };

  return (
    <QuotationContext.Provider
      value={{
        quotations,
        currentQuotation,
        addToQuotation,
        removeFromQuotation,
        updateQuotationItem,
        submitQuotation,
        createNewQuotation,
        loadQuotation,
        clearCurrentQuotation,
        refreshQuotations,
        sendMessage,
        getQuotationMessages,
        markMessagesAsRead,
        grantClosurePermission,
        rejectClosurePermission,
        isLoading,
        error,
      }}
    >
      {children}
    </QuotationContext.Provider>
  );
}

export function useSecureQuotation() {
  const context = useContext(QuotationContext);
  if (!context) {
    throw new Error("useSecureQuotation must be used within a SecureQuotationProvider");
  }
  return context;
}