"use client";

import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { AccountInactive } from "./account-inactive";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  fallback?: React.ReactNode;
}

export function ProtectedRoute({ 
  children, 
  requiredPermission, 
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, hasPermission, isAccountInactive, inactiveAdminEmail, retryLogin } = useAuth();
  const router = useRouter();
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    if (!isLoading && !isAuthenticated && !isAccountInactive) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, isAccountInactive, router]);

  const handleRetryLogin = async () => {
    setIsRetrying(true);
    await retryLogin();
    setIsRetrying(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show inactive account UI if account is inactive
  if (isAccountInactive) {
    return (
      <AccountInactive 
        adminEmail={inactiveAdminEmail || undefined}
        onRetry={handleRetryLogin}
        isRetrying={isRetrying}
      />
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">
              You don't have permission to access this page.
            </p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}
