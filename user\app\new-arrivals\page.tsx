import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { Search, Filter, Grid, List, Star, Clock, Zap, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "New Arrivals | Benzochem Industries",
  description: "Discover our latest chemical products and innovations. Stay ahead with cutting-edge solutions for your business needs.",
}

export default function NewArrivalsPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium mb-6">
              <Zap className="w-4 h-4" />
              Latest Innovations
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
              New <span className="text-primary">Arrivals</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our latest chemical products and cutting-edge solutions. 
              Stay ahead of the competition with our newest innovations.
            </p>
          </div>

          {/* Stats Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">This Month</h3>
              <p className="text-2xl font-bold text-primary mb-1">24</p>
              <p className="text-sm text-muted-foreground">New Products Added</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Top Rated</h3>
              <p className="text-2xl font-bold text-green-600 mb-1">4.8</p>
              <p className="text-sm text-muted-foreground">Average Rating</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Certified</h3>
              <p className="text-2xl font-bold text-blue-600 mb-1">100%</p>
              <p className="text-sm text-muted-foreground">Quality Assured</p>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search new arrivals..."
                    className="pl-10 focus:border-primary focus:ring-primary/20"
                  />
                </div>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="organic">Organic Chemicals</SelectItem>
                    <SelectItem value="inorganic">Inorganic Chemicals</SelectItem>
                    <SelectItem value="specialty">Specialty Chemicals</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest First</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                    <SelectItem value="name">Name A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Grid className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <List className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4" />
                  Filters
                </Button>
              </div>
            </div>
          </div>

          {/* Products Grid Placeholder */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200">
                <div className="aspect-square bg-muted flex items-center justify-center relative">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <Zap className="w-8 h-8 text-primary" />
                  </div>
                  <Badge className="absolute top-3 left-3 bg-green-100 text-green-700 hover:bg-green-100">
                    New
                  </Badge>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">(4.8)</span>
                  </div>
                  
                  <h3 className="font-semibold text-card-foreground mb-2 line-clamp-2">
                    Product Name {index + 1}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    High-quality chemical compound for industrial applications
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">CAS: 123-45-{index}</span>
                    <Button size="sm" className="h-8">
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center">
            <Button variant="outline" size="lg" className="px-8">
              Load More Products
            </Button>
          </div>

          {/* Newsletter Section */}
          <div className="mt-16 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Stay Updated with New Arrivals
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Be the first to know about our latest products and innovations. 
              Subscribe to our newsletter for exclusive updates.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1"
              />
              <Button className="px-6">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}