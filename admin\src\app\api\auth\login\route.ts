import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';
import { 
  createSessionToken, 
  setSessionCookie, 
  setCSRFCookie, 
  generateCSRFToken,
  validateCSRFToken 
} from '@/lib/session';

// Initialize Convex client for server-side operations
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// Rate limiting store (in production, use Redis or database)
const loginAttempts = new Map<string, { count: number; lastAttempt: number; blocked: boolean }>();
const MAX_LOGIN_ATTEMPTS = 5;
const BLOCK_DURATION = 15 * 60 * 1000; // 15 minutes
const ATTEMPT_WINDOW = 5 * 60 * 1000; // 5 minutes

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = forwarded?.split(',')[0] || realIP || 'unknown';
  return clientIP;
}

function isRateLimited(ip: string): boolean {
  const attempts = loginAttempts.get(ip);
  if (!attempts) return false;

  const now = Date.now();
  
  // Reset if block duration has passed
  if (attempts.blocked && now - attempts.lastAttempt > BLOCK_DURATION) {
    loginAttempts.delete(ip);
    return false;
  }

  // Reset if attempt window has passed
  if (now - attempts.lastAttempt > ATTEMPT_WINDOW) {
    loginAttempts.delete(ip);
    return false;
  }

  return attempts.blocked || attempts.count >= MAX_LOGIN_ATTEMPTS;
}

function recordLoginAttempt(ip: string, success: boolean): void {
  const now = Date.now();
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0, blocked: false };

  if (success) {
    // Clear attempts on successful login
    loginAttempts.delete(ip);
    return;
  }

  // Reset count if attempt window has passed
  if (now - attempts.lastAttempt > ATTEMPT_WINDOW) {
    attempts.count = 1;
  } else {
    attempts.count++;
  }

  attempts.lastAttempt = now;
  attempts.blocked = attempts.count >= MAX_LOGIN_ATTEMPTS;

  loginAttempts.set(ip, attempts);
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let clientIP: string;
  let userAgent: string;

  try {
    // Extract client information
    clientIP = getClientIP(request);
    userAgent = request.headers.get('user-agent') || 'unknown';

    // Check rate limiting
    if (isRateLimited(clientIP)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many login attempts. Please try again later.',
          retryAfter: Math.ceil(BLOCK_DURATION / 1000)
        },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { email, password, csrfToken } = body;

    // Input validation
    if (!email || !password) {
      recordLoginAttempt(clientIP, false);
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      recordLoginAttempt(clientIP, false);
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Password length validation
    if (password.length < 3) {
      recordLoginAttempt(clientIP, false);
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 400 }
      );
    }

    // Validate CSRF token (skip for initial login, but validate for subsequent requests)
    const hasCsrfCookie = request.cookies.get('benzochem-csrf-token');
    if (hasCsrfCookie && csrfToken && !validateCSRFToken(request, csrfToken)) {
      recordLoginAttempt(clientIP, false);
      return NextResponse.json(
        { success: false, error: 'Invalid CSRF token' },
        { status: 403 }
      );
    }

    // Authenticate with Convex (include security context)
    const authResult = await convex.mutation(api.auth.authenticateAdmin, {
      email: email.toLowerCase().trim(),
      password,
      ipAddress: clientIP,
      userAgent,
    });

    if (!authResult.success || !authResult.admin) {
      recordLoginAttempt(clientIP, false);
      
      // Add delay for failed attempts to prevent brute force
      const delay = Math.min(1000 + (loginAttempts.get(clientIP)?.count || 0) * 500, 5000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: 401 }
      );
    }

    // Record successful login
    recordLoginAttempt(clientIP, true);

    // Create session token with enhanced security
    const sessionToken = await createSessionToken({
      adminId: authResult.admin._id,
      email: authResult.admin.email,
      firstName: authResult.admin.firstName,
      lastName: authResult.admin.lastName,
      role: authResult.admin.role || 'admin',
      permissions: authResult.admin.permissions || [],
      isActive: authResult.admin.isActive,
    });

    // Generate CSRF token
    const newCSRFToken = generateCSRFToken();

    // Create response with security headers
    const response = NextResponse.json({
      success: true,
      admin: {
        _id: authResult.admin._id,
        email: authResult.admin.email,
        firstName: authResult.admin.firstName,
        lastName: authResult.admin.lastName,
        role: authResult.admin.role || 'admin',
        permissions: authResult.admin.permissions || [],
        isActive: authResult.admin.isActive,
      },
      csrfToken: newCSRFToken,
      loginTime: Date.now(),
    });

    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Set secure cookies
    setSessionCookie(response, sessionToken);
    setCSRFCookie(response, newCSRFToken);

    return response;
  } catch (error) {
    console.error('Login API error:', error);
    
    // Record failed attempt if we have client IP
    if (clientIP) {
      recordLoginAttempt(clientIP, false);
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: Date.now()
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.NODE_ENV === 'production' 
        ? process.env.NEXT_PUBLIC_ADMIN_URL || 'https://admin.benzochem.com'
        : '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-CSRF-Token',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400',
    },
  });
}
