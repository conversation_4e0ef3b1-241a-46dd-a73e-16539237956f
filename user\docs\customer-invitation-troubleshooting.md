# Customer Invitation Email Troubleshooting Guide

## Overview

This guide helps troubleshoot issues with the "Customer Account Invite" email not being sent when users register for accounts.

## How Customer Invitation Emails Work

1. **Registration Flow**: When a user registers, the system creates a Shopify customer with `send_email_invite: true`
2. **Shopify Handles Email**: Shopify automatically sends the "Customer Account Invite" email template
3. **Email Template**: The email uses Shopify's built-in "Customer account invite" template
4. **Activation**: Users click the link in the email to activate their account and set their password

## Common Issues and Solutions

### 1. Email Not Being Sent

**Possible Causes:**
- Shopify email notifications are disabled
- Email template is not configured
- SMTP settings are incorrect
- Email is going to spam folder

**Solutions:**
1. **Check Shopify Admin Settings:**
   - Go to Shopify Admin > Settings > Notifications
   - Find "Customer account invite" template
   - Ensure it's enabled and configured
   - Check the email template content

2. **Verify SMTP Configuration:**
   - In Shopify Admin > Settings > Notifications
   - Check "Email sender" settings
   - Ensure sender email is verified

3. **Check Spam Folder:**
   - Ask users to check spam/junk folders
   - Add your domain to safe senders list

### 2. API Errors

**Check API Response:**
```javascript
// Test the invitation API
fetch('/api/shopify/send-customer-invite', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'TestPassword123!'
  })
})
.then(res => res.json())
.then(console.log)
```

**Common API Errors:**
- `CUSTOMER_EXISTS`: Customer already exists, use resend invitation
- `CREATION_FAILED`: Check Shopify API permissions
- `Server configuration error`: Check environment variables

### 3. Environment Variables

**Required Variables:**
```env
NEXT_PUBLIC_SHOPIFY_STORE_DOMAIN=your-store.myshopify.com
SHOPIFY_ADMIN_API_ACCESS_TOKEN=shpat_xxxxx
SHOPIFY_API_VERSION=2025-04
```

**Check Configuration:**
```javascript
// In browser console
fetch('/api/shopify/send-customer-invite', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: '<EMAIL>' })
})
```

## Testing Tools

### 1. Admin Test Page
Visit `/admin/invitation-test` to test invitation functionality:
- Create test customers
- Resend invitations
- View API responses

### 2. Browser Console Testing
```javascript
// Test invitation for existing customer
window.testInvitation('<EMAIL>')

// Create new test customer
window.createTestCustomer()
```

### 3. API Endpoints

**Create Customer with Invitation:**
```
POST /api/shopify/send-customer-invite
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "password123",
  "phone": "+**********"
}
```

**Resend Invitation:**
```
POST /api/shopify/resend-invitation
{
  "email": "<EMAIL>"
}
```

## Shopify Admin Checklist

### Email Template Configuration
1. Go to Shopify Admin > Settings > Notifications
2. Find "Customer account invite" in the list
3. Click to edit the template
4. Ensure the template is enabled
5. Check the subject line and email content
6. Save any changes

### Email Settings
1. Go to Settings > Notifications
2. Check "Email sender" section
3. Verify sender email address
4. Ensure "Send emails from" is configured correctly

### Customer Settings
1. Go to Settings > Customer accounts
2. Ensure customer accounts are enabled
3. Check account activation settings

## Debugging Steps

### 1. Check Server Logs
Look for these log messages:
```
🔧 Shopify configuration loaded for customer invitation
Creating customer with invitation: { email, firstName, lastName }
Customer created successfully with invitation email: [customer_id]
```

### 2. Verify API Response
Expected success response:
```json
{
  "success": true,
  "customer": { "id": "gid://shopify/Customer/123", ... },
  "message": "Account created successfully! Please check your email..."
}
```

### 3. Check Shopify Customer State
In Shopify Admin > Customers:
- Find the customer by email
- Check customer state (should be "Invited" or "Disabled")
- Look for "Account activation" option

## Manual Solutions

### Resend Invitation Manually
1. Go to Shopify Admin > Customers
2. Find the customer
3. Click on the customer
4. Click "Send account invite" button

### Reset Customer Password
1. In Shopify Admin > Customers
2. Find the customer
3. Click "Reset password"
4. This will send a password reset email

## Email Template Customization

### Default Template Location
Shopify Admin > Settings > Notifications > Customer account invite

### Template Variables
Available in the email template:
- `{{ customer.first_name }}`
- `{{ customer.last_name }}`
- `{{ customer.email }}`
- `{{ shop.name }}`
- `{{ account_activation_url }}`

### Custom Template Example
```html
<h1>Welcome to {{ shop.name }}!</h1>
<p>Hi {{ customer.first_name }},</p>
<p>Click the link below to activate your account:</p>
<a href="{{ account_activation_url }}">Activate Account</a>
```

## Contact Support

If issues persist:
1. Check Shopify status page
2. Contact Shopify support
3. Verify email deliverability with your email provider
4. Check domain reputation and SPF/DKIM records

## Additional Resources

- [Shopify Customer Account Documentation](https://help.shopify.com/en/manual/customers)
- [Shopify Email Notifications](https://help.shopify.com/en/manual/sell-online/notifications)
- [Email Deliverability Best Practices](https://help.shopify.com/en/manual/sell-online/notifications/email-deliverability)
