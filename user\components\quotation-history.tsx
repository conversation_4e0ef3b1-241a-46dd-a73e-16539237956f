"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
    FileText,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle,
    Eye,
    ThumbsUp,
    ThumbsDown,
    RefreshCw,
    Building2,
    MapPin,
    Phone,
    Mail,
    Calendar,
    DollarSign,
    Package,
    Truck,
    FileCheck,
    Download,
    Send,
    User,
    MessageSquare,
    ShoppingCart,
    CreditCard,
    Banknote,
    Calculator,
    Timer,
    Shield,
    Award,
    TrendingUp
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Skeleton } from "@/components/ui/skeleton"
import { useAuth } from "@/contexts/auth-context"
import { apiClient } from "@/lib/api-client"
import { toast } from "sonner"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import QuotationMessageThread from "./quotation-message-thread"

// Skeleton for a single quotation history row
function QuotationHistoryRowSkeleton() {
    return (
        <tr>
            <td className="px-6 py-4 whitespace-nowrap">
                <Skeleton className="h-4 w-24 rounded" />
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <Skeleton className="h-4 w-20 rounded" />
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <Skeleton className="h-6 w-16 rounded-full" />
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <Skeleton className="h-4 w-12 rounded" />
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <Skeleton className="h-8 w-16 rounded" />
            </td>
        </tr>
    )
}

// Skeleton for the entire QuotationHistory table
function QuotationHistoryTableSkeleton() {
    return (
        <div className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl overflow-hidden shadow-sm">
            <div className="overflow-x-auto custom-scrollbar">
                <table className="min-w-full divide-y divide-border/20">
                    <thead className="bg-muted/30">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Quotation ID</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date Submitted</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Total Value</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Items</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody className="bg-card/30 divide-y divide-border/20">
                        {[...Array(4)].map((_, i) => (
                            <QuotationHistoryRowSkeleton key={i} />
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    )
}

function getStatusColor(status: string) {
    switch (status) {
        case "submitted":
        case "pending":
            return "bg-blue-500/10 text-blue-600 border-blue-500/20"
        case "under_review":
            return "bg-yellow-500/10 text-yellow-600 border-yellow-500/20"
        case "quoted":
            return "bg-purple-500/10 text-purple-600 border-purple-500/20"
        case "approved":
            return "bg-green-500/10 text-green-600 border-green-500/20"
        case "rejected":
            return "bg-red-500/10 text-red-600 border-red-500/20"
        default:
            return "bg-muted/50 text-muted-foreground border-border/20"
    }
}

function getStatusIcon(status: string) {
    switch (status) {
        case "submitted":
        case "pending":
            return <Clock className="h-3 w-3" />
        case "under_review":
            return <AlertCircle className="h-3 w-3" />
        case "quoted":
            return <FileText className="h-3 w-3" />
        case "approved":
            return <CheckCircle className="h-3 w-3" />
        case "rejected":
            return <XCircle className="h-3 w-3" />
        default:
            return <FileText className="h-3 w-3" />
    }
}

function formatDate(date: Date | string | number | undefined | null) {
    if (!date) return 'N/A';

    let d: Date;

    if (typeof date === 'string') {
        d = new Date(date);
    } else if (typeof date === 'number') {
        d = new Date(date);
    } else if (date instanceof Date) {
        d = date;
    } else {
        return 'N/A';
    }

    // Check if the date is valid
    if (isNaN(d.getTime())) {
        return 'N/A';
    }

    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

export default function QuotationHistory() {
    const { quotations, isLoading, refreshQuotations } = useSecureQuotation()
    const { user } = useAuth()
    const [selectedQuotation, setSelectedQuotation] = useState<any>(null)
    const [rejectionReason, setRejectionReason] = useState("")
    const [isAccepting, setIsAccepting] = useState(false)
    const [isRejecting, setIsRejecting] = useState(false)
    const [detailedQuotation, setDetailedQuotation] = useState<any>(null)
    const [loadingDetails, setLoadingDetails] = useState(false)
    const [showMessageThread, setShowMessageThread] = useState(false)



    // Fetch detailed quotation data from admin API
    const fetchQuotationDetails = async (quotationId: string) => {
        try {
            setLoadingDetails(true)
            const result = await apiClient.getQuotation(quotationId)

            if (result.success && result.data) {
                setDetailedQuotation(result.data)
            } else {
                toast.error("Failed to load quotation details")
            }
        } catch (error) {
            console.error("Error fetching quotation details:", error)
            toast.error("Failed to load quotation details")
        } finally {
            setLoadingDetails(false)
        }
    }

    const handleAcceptQuotation = async (quotationId: string) => {
        if (!user) return

        try {
            setIsAccepting(true)
            const result = await apiClient.acceptQuotation(quotationId)

            if (result.success) {
                toast.success("Quotation accepted successfully! We'll process your order soon.")
                await refreshQuotations()
                setDetailedQuotation(null) // Close modal
            } else {
                toast.error("Failed to accept quotation")
            }
        } catch (error) {
            console.error("Error accepting quotation:", error)
            toast.error("Failed to accept quotation")
        } finally {
            setIsAccepting(false)
        }
    }

    const handleRejectQuotation = async (quotationId: string) => {
        if (!user) return

        try {
            setIsRejecting(true)
            const result = await apiClient.rejectQuotation(quotationId, rejectionReason)

            if (result.success) {
                toast.success("Quotation rejected. Thank you for your feedback.")
                setRejectionReason("")
                await refreshQuotations()
                setDetailedQuotation(null) // Close modal
            } else {
                toast.error("Failed to reject quotation")
            }
        } catch (error) {
            console.error("Error rejecting quotation:", error)
            toast.error("Failed to reject quotation")
        } finally {
            setIsRejecting(false)
        }
    }

    const handleViewDetails = async (quotation: any) => {
        setSelectedQuotation(quotation)
        // Auto-open message thread if permission is required, otherwise reset state
        setShowMessageThread(quotation.threadStatus === "awaiting_user_permission")
        await fetchQuotationDetails(quotation.id)
    }

    const formatCurrency = (amount: number | string) => {
        const num = typeof amount === 'string' ? parseFloat(amount) : amount
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 2
        }).format(num)
    }

    const calculateTotalValue = (quotation: any) => {
        // First check if there's an admin response with total amount
        if (quotation.adminResponse?.totalAmount) {
            return formatCurrency(quotation.adminResponse.totalAmount)
        }
        // Fallback to legacy quotedPrice if available
        if (quotation.quotedPrice) {
            return formatCurrency(quotation.quotedPrice)
        }
        return "Pending"
    }

    const getQuotationPriority = (urgency: string) => {
        switch (urgency) {
            case 'asap':
                return { label: 'ASAP', color: 'bg-red-500/10 text-red-600 border-red-500/20', icon: Timer }
            case 'urgent':
                return { label: 'Urgent', color: 'bg-orange-500/10 text-orange-600 border-orange-500/20', icon: AlertCircle }
            default:
                return { label: 'Standard', color: 'bg-blue-500/10 text-blue-600 border-blue-500/20', icon: Clock }
        }
    }

    if (isLoading) {
        return <QuotationHistoryTableSkeleton />
    }

    if (!quotations || quotations.length === 0) {
        return (
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl overflow-hidden shadow-sm"
            >
                <div className="p-12 text-center">
                    <div className="w-20 h-20 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <FileText className="w-10 h-10 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-foreground">No Quotations Yet</h3>
                    <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                        You haven't submitted any quotation requests yet. Start by browsing our products and requesting quotes from our chemical specialists.
                    </p>
                    <Button asChild>
                        <a href="/products">Browse Products</a>
                    </Button>
                </div>
            </motion.div>
        )
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl overflow-hidden shadow-sm"
        >
            <div className="overflow-x-auto custom-scrollbar">
                <table className="min-w-full divide-y divide-border/20">
                    <thead className="bg-muted/30">
                        <tr>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Quotation ID
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Date Submitted
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Status
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Total Value
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Items
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Priority
                            </th>
                            <th className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-card/30 divide-y divide-border/20">
                        {quotations && quotations.map((quotation, index) => (
                            <motion.tr
                                key={quotation.id}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: index * 0.1 }}
                                className="hover:bg-accent/30 transition-colors duration-200"
                            >
                                <td className="px-6 py-4 whitespace-nowrap font-semibold text-sm text-foreground">
                                    {quotation.id}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                                    {quotation.submittedAt ? formatDate(quotation.submittedAt) : 'Draft'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <Badge
                                        variant="outline"
                                        className={`${getStatusColor(quotation.status)} flex items-center gap-1 font-medium`}
                                    >
                                        {getStatusIcon(quotation.status)}
                                        {quotation.status.replace('_', ' ').toUpperCase()}
                                    </Badge>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                    <div className="flex items-center gap-2">
                                        <div className="w-6 h-6 bg-green-500/10 rounded-lg flex items-center justify-center">
                                            <DollarSign className="h-3 w-3 text-green-600" />
                                        </div>
                                        <span className="font-semibold">
                                            {calculateTotalValue(quotation)}
                                        </span>
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                                    <div className="flex items-center gap-2">
                                        <div className="w-6 h-6 bg-blue-500/10 rounded-lg flex items-center justify-center">
                                            <Package className="h-3 w-3 text-blue-600" />
                                        </div>
                                        <span className="font-medium">{quotation.items ? quotation.items.length : 0} items</span>
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    {(() => {
                                        const urgency = (quotation as any).urgency || 'standard'
                                        const priority = getQuotationPriority(urgency)
                                        const IconComponent = priority.icon
                                        return (
                                            <Badge
                                                variant="outline"
                                                className={`${priority.color} flex items-center gap-1 font-medium`}
                                            >
                                                <IconComponent className="h-3 w-3" />
                                                {priority.label}
                                            </Badge>
                                        )
                                    })()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                                    <div className="flex items-center gap-2 justify-end">
                                        {/* Message thread indicator */}
                                        {(quotation as any).threadStatus === "active" && (
                                            <div className="flex items-center gap-1 text-blue-600 bg-blue-500/10 px-2 py-1 rounded-md">
                                                <MessageSquare className="h-3 w-3" />
                                                <span className="text-xs font-medium">Active</span>
                                            </div>
                                        )}
                                        {(quotation as any).threadStatus === "awaiting_user_permission" && (
                                            <div className="flex items-center gap-1 text-yellow-600 bg-yellow-500/10 px-2 py-1 rounded-md animate-pulse">
                                                <AlertCircle className="h-3 w-3" />
                                                <span className="text-xs font-medium">Permission Required</span>
                                            </div>
                                        )}

                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleViewDetails(quotation)}
                                                >
                                                    <Eye className="h-3 w-3 mr-1" />
                                                    View Details
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto custom-scrollbar">
                                                <DialogHeader>
                                                    <DialogTitle>Quotation Details - {quotation.id}</DialogTitle>
                                                    <DialogDescription>
                                                        Submitted on {quotation.submittedAt ? formatDate(quotation.submittedAt) : 'Draft'}
                                                    </DialogDescription>
                                                </DialogHeader>

                                                {loadingDetails ? (
                                                    <div className="flex items-center justify-center py-12">
                                                        <div className="animate-spin rounded-full h-10 w-10 border-2 border-primary border-t-transparent"></div>
                                                    </div>
                                                ) : selectedQuotation && (
                                                    <div className="space-y-8">
                                                        {/* Status and Info */}
                                                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 bg-muted/50 rounded-xl border border-border">
                                                            <Badge
                                                                variant="outline"
                                                                className={`${getStatusColor(selectedQuotation.status)} flex items-center gap-2 px-3 py-2 text-sm font-semibold`}
                                                            >
                                                                {getStatusIcon(selectedQuotation.status)}
                                                                {selectedQuotation.status.replace('_', ' ').toUpperCase()}
                                                            </Badge>
                                                            {/* Show admin response total amount or fallback to legacy */}
                                                            {(detailedQuotation?.adminResponse?.totalAmount || selectedQuotation.quotedPrice) && (
                                                                <div className="text-right">
                                                                    <p className="text-sm text-muted-foreground font-medium">
                                                                        {detailedQuotation?.adminResponse?.totalAmount ? 'Total Amount' : 'Quoted Price'}
                                                                    </p>
                                                                    <p className="text-2xl font-bold text-green-600">
                                                                        {detailedQuotation?.adminResponse?.totalAmount
                                                                            ? formatCurrency(detailedQuotation.adminResponse.totalAmount)
                                                                            : `₹${selectedQuotation.quotedPrice.toFixed(2)}`
                                                                        }
                                                                    </p>
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Items */}
                                                        <div>
                                                            <h4 className="font-semibold mb-4 text-foreground flex items-center gap-2">
                                                                <Package className="h-5 w-5 text-primary" />
                                                                Items ({selectedQuotation.items ? selectedQuotation.items.length : 0})
                                                            </h4>
                                                            <div className="space-y-3">
                                                                {selectedQuotation.items && selectedQuotation.items.map((item: any) => (
                                                                    <div key={item.id} className="flex items-center justify-between p-4 bg-muted/50 rounded-xl border border-border hover:bg-muted/70 transition-colors">
                                                                        <div className="flex-1">
                                                                            <p className="font-semibold text-foreground">{item.name}</p>
                                                                            <div className="flex items-center gap-2 mt-1">
                                                                                <span className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium">
                                                                                    {item.category}
                                                                                </span>
                                                                                {item.specifications && (
                                                                                    <span className="text-sm text-muted-foreground">• {item.specifications}</span>
                                                                                )}
                                                                            </div>
                                                                            {item.notes && (
                                                                                <div className="mt-2 p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                                                                                    <p className="text-sm text-blue-600 italic">Note: {item.notes}</p>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                        <div className="text-right ml-4">
                                                                            <p className="font-bold text-foreground text-lg">{item.quantity}</p>
                                                                            <p className="text-sm text-muted-foreground">{item.unit}</p>
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>

                                                        {/* Notes */}
                                                        {selectedQuotation.notes && (
                                                            <div>
                                                                <h4 className="font-semibold mb-3 text-foreground flex items-center gap-2">
                                                                    <FileText className="h-5 w-5 text-primary" />
                                                                    Your Notes
                                                                </h4>
                                                                <div className="p-4 bg-muted/50 rounded-xl border border-border">
                                                                    <p className="text-sm text-foreground">
                                                                        {selectedQuotation.notes}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Admin Quoted Response */}
                                                        {detailedQuotation?.adminResponse && (
                                                            <div className="space-y-4">
                                                                <div className="border-t pt-4">
                                                                    <h4 className="font-medium mb-3 flex items-center gap-2">
                                                                        <Award className="h-4 w-4 text-green-600" />
                                                                        Admin Response Details
                                                                    </h4>

                                                                    {/* Admin Response Summary Card */}
                                                                    <Card className="bg-muted/50 rounded-xl border border-border hover:bg-muted/70">
                                                                        <CardHeader className="pb-2">
                                                                            <CardTitle className="text-sm flex items-center justify-between">
                                                                                <span className="flex items-center gap-2">
                                                                                    <User className="h-4 w-4" />
                                                                                    Quoted by: {detailedQuotation.adminResponse.quotedBy}
                                                                                </span>
                                                                                <span className="text-xs text-muted-foreground">
                                                                                    {detailedQuotation.adminResponse.quotedAt ?
                                                                                        formatDate(new Date(detailedQuotation.adminResponse.quotedAt)) :
                                                                                        'Recently'
                                                                                    }
                                                                                </span>
                                                                            </CardTitle>
                                                                        </CardHeader>
                                                                        <CardContent className="pt-2">
                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                                {/* Total Amount */}
                                                                                {detailedQuotation.adminResponse.totalAmount && (
                                                                                    <div className="flex items-center gap-2">
                                                                                        <div className="p-2 bg-green-500/10 rounded-lg">
                                                                                            <DollarSign className="h-4 w-4 text-green-600" />
                                                                                        </div>
                                                                                        <div>
                                                                                            <p className="text-xs text-muted-foreground">Total Amount</p>
                                                                                            <p className="font-semibold text-green-600">
                                                                                                {formatCurrency(detailedQuotation.adminResponse.totalAmount)}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                )}

                                                                                {/* Valid Until */}
                                                                                {detailedQuotation.adminResponse.validUntil && (
                                                                                    <div className="flex items-center gap-2">
                                                                                        <div className="p-2 bg-orange-500/10 rounded-lg">
                                                                                            <Calendar className="h-4 w-4 text-orange-600" />
                                                                                        </div>
                                                                                        <div>
                                                                                            <p className="text-xs text-muted-foreground">Valid Until</p>
                                                                                            <p className="font-semibold text-orange-600">
                                                                                                {formatDate(detailedQuotation.adminResponse.validUntil)}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </CardContent>
                                                                    </Card>

                                                                    {/* GST Breakdown */}
                                                                    {detailedQuotation.adminResponse.gstDetails && (
                                                                        <Card className="mt-4">
                                                                            <CardHeader>
                                                                                <CardTitle className="flex items-center gap-2 text-lg">
                                                                                    <Calculator className="h-5 w-5 text-purple-600" />
                                                                                    GST Breakdown
                                                                                </CardTitle>
                                                                            </CardHeader>
                                                                            <CardContent>
                                                                                <div className="space-y-3">
                                                                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                                                                        <div className="space-y-2">
                                                                                            <div className="flex justify-between">
                                                                                                <span className="text-muted-foreground">Subtotal:</span>
                                                                                                <span className="font-medium">₹{detailedQuotation.adminResponse.gstDetails.subtotal.toFixed(2)}</span>
                                                                                            </div>
                                                                                            {detailedQuotation.adminResponse.gstDetails.cgstRate > 0 && (
                                                                                                <div className="flex justify-between">
                                                                                                    <span className="text-muted-foreground">CGST ({detailedQuotation.adminResponse.gstDetails.cgstRate}%):</span>
                                                                                                    <span className="font-medium">₹{detailedQuotation.adminResponse.gstDetails.cgstAmount.toFixed(2)}</span>
                                                                                                </div>
                                                                                            )}
                                                                                            {detailedQuotation.adminResponse.gstDetails.sgstRate > 0 && (
                                                                                                <div className="flex justify-between">
                                                                                                    <span className="text-muted-foreground">SGST ({detailedQuotation.adminResponse.gstDetails.sgstRate}%):</span>
                                                                                                    <span className="font-medium">₹{detailedQuotation.adminResponse.gstDetails.sgstAmount.toFixed(2)}</span>
                                                                                                </div>
                                                                                            )}
                                                                                            {detailedQuotation.adminResponse.gstDetails.igstRate > 0 && (
                                                                                                <div className="flex justify-between">
                                                                                                    <span className="text-muted-foreground">IGST ({detailedQuotation.adminResponse.gstDetails.igstRate}%):</span>
                                                                                                    <span className="font-medium">₹{detailedQuotation.adminResponse.gstDetails.igstAmount.toFixed(2)}</span>
                                                                                                </div>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className="border-l border-border pl-4">
                                                                                            <div className="bg-green-500/10 p-3 rounded-lg border border-green-500/20">
                                                                                                <div className="flex justify-between font-semibold text-green-600">
                                                                                                    <span>Final Amount:</span>
                                                                                                    <span>₹{detailedQuotation.adminResponse.totalAmount}</span>
                                                                                                </div>
                                                                                                <div className="flex justify-between text-sm text-green-600 mt-1">
                                                                                                    <span>Total Tax:</span>
                                                                                                    <span>₹{detailedQuotation.adminResponse.gstDetails.totalTax.toFixed(2)}</span>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </CardContent>
                                                                        </Card>
                                                                    )}

                                                                    {/* Terms and Conditions */}
                                                                    {detailedQuotation.adminResponse.terms && (
                                                                        <div className="mt-4">
                                                                            <h5 className="font-medium mb-2 flex items-center gap-2">
                                                                                <FileCheck className="h-4 w-4 text-blue-600" />
                                                                                Terms & Conditions
                                                                            </h5>
                                                                            <div className="bg-blue-500/10 p-3 rounded-lg border border-blue-500/20">
                                                                                <p className="text-sm text-blue-600 whitespace-pre-wrap">
                                                                                    {detailedQuotation.adminResponse.terms}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    )}

                                                                    {/* Admin Notes */}
                                                                    {detailedQuotation.adminResponse.notes && (
                                                                        <div className="mt-4">
                                                                            <h5 className="font-medium mb-2 flex items-center gap-2">
                                                                                <MessageSquare className="h-4 w-4 text-purple-600" />
                                                                                Additional Notes
                                                                            </h5>
                                                                            <div className="bg-purple-500/10 p-3 rounded-lg border border-purple-500/20">
                                                                                <p className="text-sm text-purple-600 whitespace-pre-wrap">
                                                                                    {detailedQuotation.adminResponse.notes}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    )}

                                                                    {/* Processing Notes (Internal - if available) */}
                                                                    {detailedQuotation.adminResponse.processingNotes && (
                                                                        <div className="mt-4">
                                                                            <h5 className="font-medium mb-2 flex items-center gap-2">
                                                                                <Shield className="h-4 w-4 text-muted-foreground" />
                                                                                Processing Details
                                                                            </h5>
                                                                            <div className="bg-muted/50 p-3 rounded-lg border border-border">
                                                                                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                                                                                    {detailedQuotation.adminResponse.processingNotes}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Legacy Company Response (fallback) */}
                                                        {selectedQuotation.companyNotes && !detailedQuotation?.adminResponse && (
                                                            <div>
                                                                <h4 className="font-medium mb-2">Our Response</h4>
                                                                <p className="text-sm text-muted-foreground bg-blue-500/10 p-3 rounded-lg border border-blue-500/20">
                                                                    {selectedQuotation.companyNotes}
                                                                </p>
                                                            </div>
                                                        )}

                                                        {/* Legacy Valid Until (fallback) */}
                                                        {selectedQuotation.validUntil && !detailedQuotation?.adminResponse?.validUntil && (
                                                            <div className="text-sm text-muted-foreground">
                                                                <strong>Quote valid until:</strong> {formatDate(selectedQuotation.validUntil)}
                                                            </div>
                                                        )}

                                                        {/* Message Thread Section */}
                                                        <div className="border-t pt-4">
                                                        <div className="flex items-center justify-between mb-4">
                                                        <h4 className="font-medium flex items-center gap-2">
                                                        <MessageSquare className="h-4 w-4 text-blue-600" />
                                                        Discussion Thread
                                                            {(detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission" && (
                                                                         <Badge className="bg-yellow-100 text-yellow-800 animate-pulse ml-2">
                                                                             <AlertCircle className="h-3 w-3 mr-1" />
                                                                             Permission Required
                                                                         </Badge>
                                                                     )}
                                                                 </h4>
                                                                <Button
                                                                variant={
                                                                    (detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission"
                                                                        ? "default" 
                                                                        : "outline"
                                                                    }
                                                                size="sm"
                                                                onClick={() => setShowMessageThread(!showMessageThread)}
                                                                className={
                                                                    (detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission"
                                                                        ? "animate-pulse"
                                                                        : ""
                                                                }
                                                                >
                                                                     <MessageSquare className="h-4 w-4 mr-2" />
                                                                     {showMessageThread ? 'Hide Messages' : 
                                                                         (detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission"
                                                                             ? 'Action Required - View Messages'
                                                                             : 'View Messages'
                                                                     }
                                                                 </Button>
                                                            </div>

                                                            {showMessageThread && (
                                                                <div className="mt-4">
                                                                    <QuotationMessageThread
                                                                    quotationId={selectedQuotation.id}
                                                                    quotation={detailedQuotation || selectedQuotation}
                                                                    onClose={() => setShowMessageThread(false)}
                                                                        onQuotationUpdate={() => fetchQuotationDetails(selectedQuotation.id)}
                                                                     />
                                                                </div>
                                                            )}

                                                            {!showMessageThread && (
                                                            <div className={`text-sm p-3 rounded-lg border ${
                                                            (detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission"
                                                            ? "text-yellow-600 bg-yellow-500/10 border-yellow-500/20"
                                                            : "text-blue-600 bg-blue-500/10 border-blue-500/20"
                                                            }`}>
                                                                {(detailedQuotation?.threadStatus || selectedQuotation.threadStatus) === "awaiting_user_permission" ? (
                                                                        <p className="flex items-center gap-2 font-medium">
                                                                             <AlertCircle className="h-4 w-4 text-yellow-600" />
                                                                             Action Required: Admin has requested permission to close this discussion thread. Please respond above.
                                                                         </p>
                                                                     ) : (
                                                                         <p className="flex items-center gap-2">
                                                                             <MessageSquare className="h-4 w-4 text-blue-600" />
                                                                             Start a conversation with our team to discuss this quotation, ask questions, or provide additional requirements.
                                                                         </p>
                                                                     )}
                                                                 </div>
                                                             )}
                                                        </div>

                                                        {/* Action Buttons for Quoted Status */}
                                                        {selectedQuotation.status === "quoted" && (
                                                            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-border/20">
                                                                <Button
                                                                    onClick={() => handleAcceptQuotation(selectedQuotation.id)}
                                                                    disabled={isAccepting}
                                                                    className="flex-1"
                                                                >
                                                                    <ThumbsUp className="h-4 w-4 mr-2" />
                                                                    {isAccepting ? "Accepting..." : "Accept Quote"}
                                                                </Button>

                                                                <AlertDialog>
                                                                    <AlertDialogTrigger asChild>
                                                                        <Button
                                                                            variant="destructive"
                                                                            className="flex-1"
                                                                        >
                                                                            <ThumbsDown className="h-4 w-4 mr-2" />
                                                                            Reject Quote
                                                                        </Button>
                                                                    </AlertDialogTrigger>
                                                                    <AlertDialogContent>
                                                                        <AlertDialogHeader>
                                                                            <AlertDialogTitle>Reject Quotation</AlertDialogTitle>
                                                                            <AlertDialogDescription>
                                                                                Are you sure you want to reject this quotation? Please provide a reason (optional).
                                                                            </AlertDialogDescription>
                                                                        </AlertDialogHeader>
                                                                        <div className="py-4">
                                                                            <Textarea
                                                                                placeholder="Reason for rejection (optional)"
                                                                                value={rejectionReason}
                                                                                onChange={(e) => setRejectionReason(e.target.value)}
                                                                                rows={3}
                                                                            />
                                                                        </div>
                                                                        <AlertDialogFooter>
                                                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                                            <AlertDialogAction
                                                                                onClick={() => handleRejectQuotation(selectedQuotation.id)}
                                                                                disabled={isRejecting}
                                                                            >
                                                                                {isRejecting ? "Rejecting..." : "Reject Quote"}
                                                                            </AlertDialogAction>
                                                                        </AlertDialogFooter>
                                                                    </AlertDialogContent>
                                                                </AlertDialog>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </DialogContent>
                                        </Dialog>
                                    </div>
                                </td>
                            </motion.tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </motion.div>
    )
}