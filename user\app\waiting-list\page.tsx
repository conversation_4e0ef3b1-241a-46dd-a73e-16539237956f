"use client"

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useRouter } from 'next/navigation'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Mail, 
  RefreshCw, 
  Sparkles, 
  Crown, 
  Shield,
  ArrowRight,
  Timer,
  User,
  Calendar,
  AlertCircle,
  CheckCircle2,
  Loader2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { motion, AnimatePresence } from 'framer-motion'

export default function WaitingListPage() {
  const { user, isLoading, refreshUserData } = useAuth()
  const router = useRouter()
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [timeElapsed, setTimeElapsed] = useState(0)

  useEffect(() => {
    // Redirect if user is approved
    if (user && user.status === 'approved') {
      router.push('/account')
    }
    // Redirect if no user (not logged in)
    if (!isLoading && !user) {
      router.push('/login')
    }
  }, [user, router, isLoading])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refreshUserData()
    setIsRefreshing(false)
  }

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshUserData()
      setTimeElapsed(prev => prev + 30)
    }, 30000)

    return () => clearInterval(interval)
  }, [refreshUserData])

  // Time elapsed counter
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTimeElapsed = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center vanilla-gradient">
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center space-y-6"
        >
          <div className="relative">
            <div className="w-16 h-16 border-4 border-primary/20 rounded-full"></div>
            <div className="absolute top-0 left-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
          <div className="text-center space-y-2">
            <p className="text-lg font-medium text-foreground">Loading Account Status</p>
            <p className="text-sm text-muted-foreground">Verifying your credentials...</p>
          </div>
        </motion.div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const getStatusIcon = () => {
    switch (user.status) {
      case 'pending':
        return <Clock className="w-12 h-12 text-chart-1" />
      case 'approved':
        return <CheckCircle2 className="w-12 h-12 text-chart-4" />
      case 'rejected':
        return <XCircle className="w-12 h-12 text-destructive" />
      case 'suspended':
        return <AlertCircle className="w-12 h-12 text-destructive" />
      default:
        return <Timer className="w-12 h-12 text-muted-foreground" />
    }
  }

  const getStatusConfig = () => {
    switch (user.status) {
      case 'pending':
        return {
          title: 'Account Under Review',
          subtitle: 'Your application is being carefully evaluated',
          message: 'Thank you for your registration with Benzochem Industries. Our team is currently reviewing your application to ensure compliance with our quality standards. You will receive an email notification once the review process is complete.',
          cardClass: 'bg-gradient-to-br from-chart-1/5 to-chart-1/10 border-chart-1/20',
          iconBg: 'bg-chart-1/10',
          badgeVariant: 'secondary' as const,
          badgeText: 'In Review',
          estimatedTime: '24-48 hours'
        }
      case 'approved':
        return {
          title: 'Welcome to Benzochem',
          subtitle: 'Your account has been successfully approved',
          message: 'Congratulations! Your account has been approved and you now have full access to our premium chemical products and services. Welcome to the Benzochem Industries family.',
          cardClass: 'bg-gradient-to-br from-chart-4/5 to-chart-4/10 border-chart-4/20',
          iconBg: 'bg-chart-4/10',
          badgeVariant: 'default' as const,
          badgeText: 'Approved',
          estimatedTime: null
        }
      case 'rejected':
        return {
          title: 'Application Not Approved',
          subtitle: 'We were unable to approve your application',
          message: 'Unfortunately, your application does not meet our current requirements. This decision is based on our strict quality and compliance standards. Please contact our support team for detailed feedback and potential next steps.',
          cardClass: 'bg-gradient-to-br from-destructive/5 to-destructive/10 border-destructive/20',
          iconBg: 'bg-destructive/10',
          badgeVariant: 'destructive' as const,
          badgeText: 'Declined',
          estimatedTime: null
        }
      case 'suspended':
        return {
          title: 'Account Suspended',
          subtitle: 'Your account access has been temporarily restricted',
          message: 'Your account has been suspended due to policy violations or security concerns. Please contact our support team immediately to resolve this issue and restore your account access.',
          cardClass: 'bg-gradient-to-br from-destructive/5 to-destructive/10 border-destructive/20',
          iconBg: 'bg-destructive/10',
          badgeVariant: 'destructive' as const,
          badgeText: 'Suspended',
          estimatedTime: null
        }
      default:
        return {
          title: 'Verifying Status',
          subtitle: 'Please wait while we check your account',
          message: 'We are currently verifying your account status. If this continues for more than a few minutes, please refresh the page or contact support.',
          cardClass: 'bg-gradient-to-br from-muted/5 to-muted/10 border-border',
          iconBg: 'bg-muted/10',
          badgeVariant: 'outline' as const,
          badgeText: 'Verifying',
          estimatedTime: null
        }
    }
  }

  const statusConfig = getStatusConfig()

  return (
    <main className="min-h-screen w-full vanilla-gradient">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-chart-2/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-chart-1/10 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-8 min-h-screen flex items-center justify-center">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-4xl"
        >

          {/* Main Status Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <Card className={`${statusConfig.cardClass} border-2 premium-glow overflow-hidden`}>
              <CardContent className="p-0">
                {/* Status Header */}
                <div className="p-8 pb-6">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className={`p-4 rounded-2xl ${statusConfig.iconBg}`}>
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.6, type: "spring", stiffness: 200 }}
                        >
                          {getStatusIcon()}
                        </motion.div>
                      </div>
                      <div>
                        <Badge variant={statusConfig.badgeVariant} className="mb-2">
                          {statusConfig.badgeText}
                        </Badge>
                        <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-1">
                          {statusConfig.title}
                        </h1>
                        <p className="text-muted-foreground text-sm">
                          {statusConfig.subtitle}
                        </p>
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRefresh}
                      disabled={isRefreshing}
                      className="shrink-0"
                    >
                      {isRefreshing ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4" />
                      )}
                    </Button>
                  </div>

                  {/* User Info */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="flex items-center gap-3 p-3 bg-card/50 rounded-lg border border-border/50">
                      <User className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground">Account</p>
                        <p className="text-sm font-medium">{user.email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-card/50 rounded-lg border border-border/50">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground">Registered</p>
                        <p className="text-sm font-medium">
                          {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Recently'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 bg-card/50 rounded-lg border border-border/50">
                      <Timer className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs text-muted-foreground">Time Elapsed</p>
                        <p className="text-sm font-medium">{formatTimeElapsed(timeElapsed)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Status Message */}
                  <div className="p-6 bg-card/30 rounded-xl border border-border/30">
                    <p className="text-foreground leading-relaxed text-center">
                      {statusConfig.message}
                    </p>
                    
                    {statusConfig.estimatedTime && (
                      <div className="mt-4 text-center">
                        <p className="text-sm text-muted-foreground">
                          <Clock className="w-4 h-4 inline mr-1" />
                          Estimated review time: <span className="font-medium">{statusConfig.estimatedTime}</span>
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Section */}
                <div className="p-8 pt-0">
                  <AnimatePresence mode="wait">
                    {user.status === 'approved' && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-4"
                      >
                        <Button
                          onClick={() => router.push('/account')}
                          size="lg"
                          className="w-full premium-button text-primary-foreground group"
                        >
                          <Crown className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                          Access Your Dashboard
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </motion.div>
                    )}

                    {(user.status === 'rejected' || user.status === 'suspended') && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-4"
                      >
                        <Button
                          onClick={() => router.push('/contact')}
                          size="lg"
                          className="w-full premium-button text-primary-foreground group"
                        >
                          <Mail className="w-5 h-5 mr-2" />
                          Contact Support Team
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </motion.div>
                    )}

                    {user.status === 'pending' && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="text-center"
                      >
                        <div className="inline-flex items-center gap-2 px-4 py-2 bg-chart-1/10 text-chart-1 rounded-full text-sm">
                          <Shield className="w-4 h-4" />
                          Your application is being processed securely
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Footer Info */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            className="mt-8 text-center space-y-2"
          >
            <p className="text-xs text-muted-foreground">
              This page automatically refreshes every 30 seconds to check for status updates
            </p>
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
              <span>•</span>
              <span>Next check in: {30 - (timeElapsed % 30)}s</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>
  )
}