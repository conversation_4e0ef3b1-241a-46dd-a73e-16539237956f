/**
 * Emergency cookie cleanup script
 * This runs immediately when the page loads to prevent JSON parsing errors
 */
(function() {
  'use strict';
  
  if (typeof window === 'undefined') return;
  
  try {
    const cookiesToCheck = ['cookie_consent', 'user_session', 'analytics_session_id'];
    
    cookiesToCheck.forEach(function(cookieName) {
      const nameEQ = cookieName + "=";
      const ca = document.cookie.split(';');
      
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        
        if (c.indexOf(nameEQ) === 0) {
          const cookieValue = c.substring(nameEQ.length, c.length);
          
          try {
            // Try to decode and parse the cookie
            let decodedValue = cookieValue;
            
            if (cookieValue.includes('%')) {
              decodedValue = decodeURIComponent(cookieValue);
            }
            
            // Check if it looks like JSON
            if (decodedValue.trim().startsWith('{') && decodedValue.trim().endsWith('}')) {
              JSON.parse(decodedValue);
              // If we get here, the cookie is valid
            } else if (decodedValue.trim().length > 0) {
              // Non-JSON cookie, might be valid for other purposes
              continue;
            }
          } catch (error) {
            console.warn('Clearing malformed cookie: ' + cookieName, error);
            // Clear the malformed cookie
            document.cookie = cookieName + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Strict';
          }
        }
      }
    });
  } catch (error) {
    console.error('Error during emergency cookie cleanup:', error);
  }
})();