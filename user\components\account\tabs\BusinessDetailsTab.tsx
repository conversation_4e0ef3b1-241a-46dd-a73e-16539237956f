"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
// import { Loader2 } from "lucide-react" // Will be replaced by Skeleton
import { Skeleton } from "@/components/ui/skeleton" // Added

interface BusinessDetailsTabProps {
  isLoading: boolean;
  shopifyData: any; // Consider defining a more specific type
}

// Skeleton for the Business Details Tab
function BusinessDetailsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Business Name and GST Number */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/3 rounded mb-1" /> {/* Label: Business Name */}
          <Skeleton className="h-10 w-full rounded-md p-2 border" /> {/* Value container */}
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/3 rounded mb-1" /> {/* Label: GST Number */}
          <Skeleton className="h-10 w-full rounded-md p-2 border flex items-center"> {/* Value container */}
            <Skeleton className="h-5 flex-grow rounded" /> {/* GST text part */}
            <Skeleton className="h-6 w-20 ml-2 rounded-full" /> {/* Badge part */}
          </Skeleton>
        </div>
      </div>
      
      {/* Additional Business Details Section */}
      <div className="mt-6">
        <Skeleton className="h-6 w-2/5 mb-4 rounded" /> {/* "Additional Business Details" title */}
        <div className="space-y-4 border-t pt-4">
          {/* Legal Name & Trade Name */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div className="space-y-1">
              <Skeleton className="h-4 w-1/2 rounded mb-1" /> {/* Label */}
              <Skeleton className="h-5 w-full rounded" /> {/* Value */}
            </div>
            <div className="space-y-1">
              <Skeleton className="h-4 w-1/2 rounded mb-1" /> {/* Label */}
              <Skeleton className="h-5 w-full rounded" /> {/* Value */}
            </div>
          </div>
          {/* Constitution & Taxpayer Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-4">
            <div className="space-y-1">
              <Skeleton className="h-4 w-1/2 rounded mb-1" /> {/* Label */}
              <Skeleton className="h-5 w-full rounded" /> {/* Value */}
            </div>
            <div className="space-y-1">
              <Skeleton className="h-4 w-1/2 rounded mb-1" /> {/* Label */}
              <Skeleton className="h-5 w-full rounded" /> {/* Value */}
            </div>
          </div>
          {/* GST Status */}
          <div className="space-y-1 mt-4">
            <Skeleton className="h-4 w-1/3 rounded mb-1" /> {/* Label */}
            <Skeleton className="h-5 w-full rounded" /> {/* Value */}
          </div>
          {/* Business Activity */}
          <div className="space-y-1 mt-4">
            <Skeleton className="h-4 w-1/3 rounded mb-1" /> {/* Label */}
            <Skeleton className="h-5 w-full rounded" /> {/* Value */}
          </div>
          {/* Principal Place of Business */}
          <div className="space-y-1 mt-4">
            <Skeleton className="h-4 w-2/5 rounded mb-1" /> {/* Label */}
            <Skeleton className="h-5 w-full rounded" /> {/* Value line 1 */}
            <Skeleton className="h-5 w-3/4 rounded mt-1" /> {/* Value line 2 (optional for address) */}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BusinessDetailsTab({ isLoading, shopifyData }: BusinessDetailsTabProps) {
  // Helper function to safely parse JSON and access nested value
  const getPrincipalAddress = (addressString: string | undefined) => {
    if (!addressString) return "Not available";
    try {
      const addressData = JSON.parse(addressString);
      // Adjust the path according to your actual JSON structure
      return addressData?.children?.[0]?.children?.[0]?.value || addressString;
    } catch (error) {
      // If parsing fails, return the original string
      return addressString;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Business Information</CardTitle>
        <CardDescription>
          View your registered business details
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <BusinessDetailsSkeleton />
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Trade Name</h3>
                <p className="p-3 border rounded-md bg-neutral-50 dark:bg-neutral-800 dark:border-neutral-700 min-h-[40px]">
                  {shopifyData?.businessName || "Not provided"}
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">GST Number</h3>
                <div className="p-3 border rounded-md bg-neutral-50 dark:bg-neutral-800 dark:border-neutral-700 flex items-center min-h-[40px]">
                  {shopifyData?.gstNumber || "Not provided"}
                  {shopifyData?.isGstVerified && (
                    <Badge className="ml-2 bg-green-100 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-700">Verified</Badge>
                  )}
                   {!shopifyData?.isGstVerified && shopifyData?.gstNumber && (
                     <Badge variant="outline" className="ml-2 border-orange-300 text-orange-700 bg-orange-50 dark:bg-orange-900/50 dark:text-orange-300 dark:border-orange-700">Pending Verification</Badge>
                  )}
                </div>
              </div>
            </div>
            
            {/* Additional Business Details - using direct properties from API response */}
            {(shopifyData?.legalNameOfBusiness || shopifyData?.constitutionOfBusiness || shopifyData?.taxpayerType || shopifyData?.gstStatus || shopifyData?.principalPlaceOfBusiness) && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">Additional Business Details (from GST Portal)</h3>
                <div className="space-y-4 border-t pt-4 dark:border-neutral-700">
                  {/* Legal Name of Business */}
                  {shopifyData.legalNameOfBusiness && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Legal Name of Business</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.legalNameOfBusiness}</p>
                    </div>
                  )}
                  {/* Trade Name */}
                  {shopifyData.tradeName && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Trade Name</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.tradeName}</p>
                    </div>
                  )}
                  {/* Constitution of Business */}
                  {shopifyData.constitutionOfBusiness && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Constitution of Business</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.constitutionOfBusiness}</p>
                    </div>
                  )}
                  {/* Taxpayer Type */}
                  {shopifyData.taxpayerType && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Taxpayer Type</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.taxpayerType}</p>
                    </div>
                  )}
                  {/* GST Status */}
                  {shopifyData.gstStatus && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">GST Status</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.gstStatus}</p>
                    </div>
                  )}
                  {/* Business Activity */}
                  {shopifyData.natureOfCoreBusinessActivity && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Business Activity</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">{shopifyData.natureOfCoreBusinessActivity}</p>
                    </div>
                  )}
                  {/* Principal Place of Business */}
                  {shopifyData.principalPlaceOfBusiness && (
                    <div>
                      <h4 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Principal Place of Business</h4>
                      <p className="text-neutral-800 dark:text-neutral-200">
                        {getPrincipalAddress(shopifyData.principalPlaceOfBusiness)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}