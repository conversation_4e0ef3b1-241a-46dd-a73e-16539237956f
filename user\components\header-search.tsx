"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import SearchWithSuggestions from "./search-with-suggestions"

interface HeaderSearchProps {
  className?: string
  compact?: boolean
}

export default function HeaderSearch({ className, compact = false }: HeaderSearchProps) {
  const router = useRouter()
  const [isExpanded, setIsExpanded] = useState(false)

  const handleSearch = (query: string) => {
    if (query.trim()) {
      router.push(`/search?q=${encodeURIComponent(query.trim())}`)
      setIsExpanded(false)
    }
  }

  if (compact) {
    return (
      <div className={className}>
        {!isExpanded ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(true)}
            className="h-9 w-9 p-0"
          >
            <Search className="h-4 w-4" />
          </Button>
        ) : (
          <div className="relative w-64">
            <SearchWithSuggestions
              placeholder="Search products..."
              onSearch={handleSearch}
              showRecentSearches={true}
              autoFocus={true}
              className="w-full"
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={className}>
      <SearchWithSuggestions
        placeholder="Search products..."
        onSearch={handleSearch}
        showRecentSearches={true}
        className="w-full max-w-md"
      />
    </div>
  )
}