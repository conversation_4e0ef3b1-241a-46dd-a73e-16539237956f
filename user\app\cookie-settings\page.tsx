import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON>, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import CookiePreferencesManager from "@/components/cookie-preferences-manager"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Cookie Settings | Benzochem Industries",
  description: "Manage your cookie preferences and control how we use cookies on our website. Customize your privacy settings according to your preferences.",
}

export default function CookieSettingsPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          
          {/* Header */}
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors mb-4">
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Link>
            
            <div className="text-center mb-8">
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium mb-6">
                <Cookie className="w-4 h-4" />
                Cookie Management
              </div>
              
              <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
                Cookie <span className="text-primary">Settings</span>
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Control your cookie preferences and manage how we use cookies to enhance your 
                browsing experience on our website.
              </p>
            </div>
          </div>

          {/* Cookie Information Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <div className="bg-card border border-border rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-950/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Shield className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-1">Essential</h3>
              <p className="text-xs text-muted-foreground">Always active for core functionality</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-950/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Cookie className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-1">Analytics</h3>
              <p className="text-xs text-muted-foreground">Help us improve our website</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-950/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Cookie className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-1">Marketing</h3>
              <p className="text-xs text-muted-foreground">Personalized ads and content</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-4 text-center">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-950/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Cookie className="w-5 h-5 text-orange-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-1">Functional</h3>
              <p className="text-xs text-muted-foreground">Enhanced features and services</p>
            </div>
          </div>

          {/* Cookie Preferences Manager */}
          <CookiePreferencesManager className="mb-8" />

          {/* Information Section */}
          <div className="bg-card border border-border rounded-xl p-6">
            <h2 className="text-xl font-bold text-card-foreground mb-4">
              About Cookies
            </h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                Cookies are small text files that are placed on your computer or mobile device when you visit a website. 
                They are widely used to make websites work more efficiently and to provide information to website owners.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-card-foreground mb-2">Why We Use Cookies</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Remember your preferences and settings</li>
                    <li>Provide secure access to your account</li>
                    <li>Analyze website traffic and usage patterns</li>
                    <li>Deliver personalized content and advertisements</li>
                    <li>Improve website functionality and user experience</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-semibold text-card-foreground mb-2">Your Rights</h3>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Control which cookies are set on your device</li>
                    <li>Change your preferences at any time</li>
                    <li>Delete cookies through your browser settings</li>
                    <li>Opt out of non-essential cookies</li>
                    <li>Request information about cookies we use</li>
                  </ul>
                </div>
              </div>
              
              <div className="bg-muted/30 rounded-lg p-4 mt-6">
                <h4 className="font-semibold text-card-foreground mb-2">Browser Settings</h4>
                <p className="text-sm">
                  You can also control cookies through your browser settings. Most browsers allow you to:
                </p>
                <ul className="list-disc list-inside space-y-1 text-sm mt-2">
                  <li>View and delete cookies</li>
                  <li>Block cookies from specific websites</li>
                  <li>Block third-party cookies</li>
                  <li>Clear all cookies when you close the browser</li>
                </ul>
                <p className="text-sm mt-2">
                  <strong>Note:</strong> Disabling cookies may affect the functionality of this and many other websites.
                </p>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          <div className="mt-8 text-center">
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link href="/privacy" className="text-primary hover:underline">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-primary hover:underline">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-primary hover:underline">
                Contact Us
              </Link>
            </div>
            <p className="text-xs text-muted-foreground mt-4">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>
    </main>
  )
}