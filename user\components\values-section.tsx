"use client"

import { motion } from "framer-motion"
import { Heart, Lightbulb, Shield, Leaf, Users, Globe, CheckCircle2 } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const coreValues = [
  {
    icon: Shield,
    title: "Quality Excellence",
    description: "We are committed to delivering products of the highest purity and consistency, exceeding industry standards and customer expectations.",
    features: [
      "99.9% product purity guarantee",
      "Rigorous quality control processes",
      "International certification compliance",
      "Continuous improvement initiatives"
    ],
    color: "from-blue-500/10 to-blue-600/10",
    iconColor: "text-blue-600"
  },
  {
    icon: Lightbulb,
    title: "Innovation",
    description: "We continuously invest in research and development to create innovative solutions that address evolving industry challenges.",
    features: [
      "State-of-the-art R&D facilities",
      "Custom formulation services",
      "Advanced analytical capabilities",
      "Cutting-edge technology adoption"
    ],
    color: "from-amber-500/10 to-amber-600/10",
    iconColor: "text-amber-600"
  },
  {
    icon: Leaf,
    title: "Sustainability",
    description: "We are dedicated to environmentally responsible practices, minimizing our ecological footprint while maximizing product performance.",
    features: [
      "Green manufacturing processes",
      "Waste reduction initiatives",
      "Renewable energy adoption",
      "Sustainable packaging solutions"
    ],
    color: "from-green-500/10 to-green-600/10",
    iconColor: "text-green-600"
  },
  {
    icon: Users,
    title: "Customer Partnership",
    description: "We build lasting relationships with our clients through trust, transparency, and exceptional service delivery.",
    features: [
      "Dedicated account management",
      "24/7 technical support",
      "Flexible delivery options",
      "Collaborative problem-solving"
    ],
    color: "from-purple-500/10 to-purple-600/10",
    iconColor: "text-purple-600"
  },
  {
    icon: Heart,
    title: "Integrity",
    description: "We conduct business with the highest ethical standards, ensuring transparency and honesty in all our interactions.",
    features: [
      "Ethical business practices",
      "Transparent communication",
      "Fair pricing policies",
      "Regulatory compliance"
    ],
    color: "from-red-500/10 to-red-600/10",
    iconColor: "text-red-600"
  },
  {
    icon: Globe,
    title: "Global Excellence",
    description: "We serve clients worldwide with consistent quality and service, adapting to local needs while maintaining global standards.",
    features: [
      "International market presence",
      "Local regulatory expertise",
      "Global supply chain network",
      "Cultural sensitivity"
    ],
    color: "from-indigo-500/10 to-indigo-600/10",
    iconColor: "text-indigo-600"
  }
]

const valueStats = [
  {
    value: "6",
    label: "Core Values",
    description: "Guiding principles"
  },
  {
    value: "100%",
    label: "Ethical Standards",
    description: "Uncompromising integrity"
  },
  {
    value: "Global",
    label: "Reach",
    description: "Worldwide presence"
  },
  {
    value: "24/7",
    label: "Support",
    description: "Always available"
  }
]

export default function ValuesSection() {
  return (
    <section className="py-20 md:py-28 bg-vanilla-gradient dark:bg-dark-gradient relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/50 via-transparent to-secondary/10" />
      <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Heart className="h-4 w-4" />
            Our Values
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            What Drives Us
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Our core values shape every decision we make and guide us in delivering exceptional 
            chemical solutions that make a difference in the world.
          </p>
        </motion.div>

        {/* Values Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-20"
        >
          {valueStats.map((stat, index) => (
            <Card key={index} className="border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300">
              <CardContent className="p-6 text-center">
                <div className="text-2xl font-bold text-foreground mb-1">{stat.value}</div>
                <div className="text-sm font-medium text-foreground mb-1">{stat.label}</div>
                <div className="text-xs text-muted-foreground">{stat.description}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Values Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {coreValues.map((value, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="group h-full border-0 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-500 hover:shadow-xl">
                <CardContent className="p-8 h-full flex flex-col">
                  {/* Icon and Title */}
                  <div className="mb-6">
                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${value.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <value.icon className={`h-8 w-8 ${value.iconColor}`} />
                    </div>
                    <h3 className="text-xl font-semibold text-card-foreground group-hover:text-primary transition-colors duration-300">
                      {value.title}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className="text-muted-foreground leading-relaxed mb-6 flex-grow">
                    {value.description}
                  </p>

                  {/* Features */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-primary" />
                      Key Features
                    </h4>
                    <ul className="space-y-2">
                      {value.features.map((feature, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Values in Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="border-0 bg-gradient-to-r from-primary/5 to-accent/5 backdrop-blur-sm">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-semibold mb-6 text-foreground">
                Values in Action
              </h3>
              <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed mb-8">
                These values aren't just words on a page – they're the foundation of how we operate every day. 
                From our rigorous quality control processes to our commitment to environmental sustainability, 
                our values guide every decision and interaction.
              </p>
              <div className="flex flex-wrap justify-center gap-6">
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Quality First
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Innovation Driven
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Sustainability Focused
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Customer Centric
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Ethically Operated
                </div>
                <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                  <div className="w-2 h-2 bg-primary rounded-full" />
                  Globally Minded
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}