import type { <PERSON>ada<PERSON> } from "next"
import { Suspense } from "react"
import { Search, Filter, Grid, List, Star, TrendingUp, Award, Users, Crown, Flame } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export const metadata: Metadata = {
  title: "Best Sellers | Benzochem Industries",
  description: "Explore our most popular chemical products trusted by thousands of businesses worldwide. Top-rated solutions for your industry needs.",
}

export default function BestSellersPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          
          {/* Hero Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 rounded-full text-orange-700 text-sm font-medium mb-6">
              <Flame className="w-4 h-4" />
              Most Popular
            </div>
            
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
              Best <span className="text-primary">Sellers</span>
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our most trusted and popular chemical products. 
              These industry-leading solutions are chosen by thousands of businesses worldwide.
            </p>
          </div>

          {/* Performance Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">#1 Seller</h3>
              <p className="text-2xl font-bold text-orange-600 mb-1">2,847</p>
              <p className="text-sm text-muted-foreground">Units Sold</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Top Rated</h3>
              <p className="text-2xl font-bold text-green-600 mb-1">4.9</p>
              <p className="text-sm text-muted-foreground">Average Rating</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Customers</h3>
              <p className="text-2xl font-bold text-blue-600 mb-1">1,200+</p>
              <p className="text-sm text-muted-foreground">Happy Clients</p>
            </div>
            
            <div className="bg-card border border-border rounded-xl p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-card-foreground mb-2">Growth</h3>
              <p className="text-2xl font-bold text-purple-600 mb-1">+45%</p>
              <p className="text-sm text-muted-foreground">This Quarter</p>
            </div>
          </div>

          {/* Top Performers Section */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <h2 className="text-xl font-bold text-card-foreground mb-6">Top Performers This Month</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { rank: 1, name: "Premium Catalyst XR-100", sales: 847, growth: "+23%" },
                { rank: 2, name: "Industrial Solvent Pro", sales: 692, growth: "+18%" },
                { rank: 3, name: "Specialty Polymer Base", sales: 534, growth: "+15%" }
              ].map((product) => (
                <div key={product.rank} className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                    product.rank === 1 ? 'bg-yellow-500' : 
                    product.rank === 2 ? 'bg-gray-400' : 'bg-orange-500'
                  }`}>
                    {product.rank}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-card-foreground text-sm">{product.name}</h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-muted-foreground">{product.sales} sold</span>
                      <span className="text-xs text-green-600 font-medium">{product.growth}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search best sellers..."
                    className="pl-10 focus:border-primary focus:ring-primary/20"
                  />
                </div>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="organic">Organic Chemicals</SelectItem>
                    <SelectItem value="inorganic">Inorganic Chemicals</SelectItem>
                    <SelectItem value="specialty">Specialty Chemicals</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Time Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="quarter">This Quarter</SelectItem>
                    <SelectItem value="year">This Year</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Grid className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <List className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4" />
                  Filters
                </Button>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200">
                <div className="aspect-square bg-muted flex items-center justify-center relative">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                    <Award className="w-8 h-8 text-primary" />
                  </div>
                  <Badge className="absolute top-3 left-3 bg-orange-100 text-orange-700 hover:bg-orange-100">
                    #{index + 1} Seller
                  </Badge>
                  {index < 3 && (
                    <div className="absolute top-3 right-3">
                      <Crown className="w-5 h-5 text-yellow-500" />
                    </div>
                  )}
                </div>
                
                <div className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">(4.9)</span>
                  </div>
                  
                  <h3 className="font-semibold text-card-foreground mb-2 line-clamp-2">
                    Best Seller Product {index + 1}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    Top-rated chemical solution trusted by industry leaders
                  </p>
                  
                  <div className="space-y-2 mb-3">
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Sales Progress</span>
                      <span className="text-primary font-medium">{85 - index * 5}%</span>
                    </div>
                    <Progress value={85 - index * 5} className="h-1" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">CAS: 789-12-{index}</span>
                    <Button size="sm" className="h-8">
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center">
            <Button variant="outline" size="lg" className="px-8">
              Load More Best Sellers
            </Button>
          </div>

          {/* Why These Products Section */}
          <div className="mt-16 bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Why These Products Lead the Market
              </h3>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Our best sellers earn their reputation through consistent quality, 
                reliability, and exceptional customer satisfaction.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-6 h-6 text-primary" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">Proven Quality</h4>
                <p className="text-sm text-muted-foreground">
                  Rigorous testing and quality assurance ensure consistent performance
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">Customer Trust</h4>
                <p className="text-sm text-muted-foreground">
                  Thousands of satisfied customers rely on these products daily
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">Market Leadership</h4>
                <p className="text-sm text-muted-foreground">
                  Industry-leading solutions that set the standard for excellence
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}