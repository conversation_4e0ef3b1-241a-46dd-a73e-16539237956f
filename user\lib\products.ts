import type { Product } from './types'
import { getProductsByCollection as apiGetProductsByCollection } from './api-client'

// Get products filtered by collection
export async function getProductsByCollection(collectionTitle: string): Promise<Product[]> {
  try {
    return await apiGetProductsByCollection(collectionTitle)
  } catch (error) {
    console.error(`Error fetching products for collection ${collectionTitle}:`, error)
    return []
  }
}