"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Check, Send, MessageSquare, ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"

export default function ContactForm() {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)

    const formData = new FormData(e.target as HTMLFormElement)
    const data = {
      firstName: formData.get('firstName') as string,
      lastName: formData.get('lastName') as string,
      email: formData.get('email') as string,
      company: formData.get('company') as string,
      inquiryType: formData.get('inquiryType') as string,
      message: formData.get('message') as string,
    }

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (response.ok) {
        setIsSubmitted(true)
      } else {
        setError(result.error || 'Failed to send message. Please try again.')
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 30 }} 
      whileInView={{ opacity: 1, y: 0 }} 
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
      className="max-w-6xl mx-auto"
    >
      {/* Section Header */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
          <MessageSquare className="h-4 w-4" />
          Contact Form
        </div>
        <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-tight">
          Let's Start a Conversation
        </h2>
        <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
          Ready to discuss your chemical needs? Fill out the form below and our experts will get back to you within 24 hours.
        </p>
      </div>

      {isSubmitted ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center py-16"
        >
          <Card className="border-0 bg-card/50 backdrop-blur-sm shadow-2xl max-w-2xl mx-auto">
            <CardContent className="p-12">
              <div className="w-24 h-24 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-8">
                <Check className="h-12 w-12 text-primary" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-6">Message Sent Successfully!</h3>
              <p className="text-muted-foreground mb-8 leading-relaxed text-lg">
                Thank you for contacting Benzochem Industries. Our team will review your message and get back to you within 24 hours.
              </p>
              <Button 
                onClick={() => setIsSubmitted(false)} 
                variant="outline" 
                className="group"
                size="lg"
              >
                Send Another Message
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit}>
          {/* Bento Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* First Name - Small */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="lg:col-span-1"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="firstName" className="text-sm font-semibold text-foreground block mb-3">
                    First Name *
                  </label>
                  <Input 
                    id="firstName" 
                    name="firstName"
                    required 
                    className="h-12 border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all text-base"
                    placeholder="Enter your first name"
                  />
                </CardContent>
              </Card>
            </motion.div>

            {/* Last Name - Small */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="lg:col-span-1"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="lastName" className="text-sm font-semibold text-foreground block mb-3">
                    Last Name *
                  </label>
                  <Input 
                    id="lastName" 
                    name="lastName"
                    required 
                    className="h-12 border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all text-base"
                    placeholder="Enter your last name"
                  />
                </CardContent>
              </Card>
            </motion.div>

            {/* Email - Medium */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="md:col-span-2 lg:col-span-2"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="email" className="text-sm font-semibold text-foreground block mb-3">
                    Email Address *
                  </label>
                  <Input 
                    id="email" 
                    name="email"
                    type="email" 
                    required 
                    className="h-12 border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all text-base"
                    placeholder="<EMAIL>"
                  />
                </CardContent>
              </Card>
            </motion.div>

            {/* Company Name - Medium */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="md:col-span-1 lg:col-span-2"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="company" className="text-sm font-semibold text-foreground block mb-3">
                    Company Name
                  </label>
                  <Input 
                    id="company" 
                    name="company"
                    className="h-12 border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all text-base"
                    placeholder="Your company name"
                  />
                </CardContent>
              </Card>
            </motion.div>

            {/* Inquiry Type - Medium */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="md:col-span-1 lg:col-span-2"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="inquiryType" className="text-sm font-semibold text-foreground block mb-3">
                    Inquiry Type *
                  </label>
                  <Select name="inquiryType">
                    <SelectTrigger className="h-12 border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all text-base">
                      <SelectValue placeholder="Select inquiry type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="product">Product Information</SelectItem>
                      <SelectItem value="quote">Request a Quote</SelectItem>
                      <SelectItem value="technical">Technical Support</SelectItem>
                      <SelectItem value="partnership">Partnership Opportunities</SelectItem>
                      <SelectItem value="bulk">Bulk Orders</SelectItem>
                      <SelectItem value="custom">Custom Formulations</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>
            </motion.div>

            {/* Message - Large */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="md:col-span-2 lg:col-span-4"
            >
              <Card className="border border-border/20 bg-card/30 backdrop-blur-sm shadow-lg hover:shadow-xl hover:border-primary/30 transition-all duration-300 h-full group">
                <CardContent className="p-6">
                  <label htmlFor="message" className="text-sm font-semibold text-foreground block mb-3">
                    Message *
                  </label>
                  <Textarea 
                    id="message" 
                    name="message"
                    rows={6} 
                    required 
                    className="border-0 bg-background/50 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all resize-none text-base"
                    placeholder="Please describe your requirements, quantities needed, or any specific questions you have..."
                  />
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="text-center"
          >
            {error && (
              <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive text-center">
                {error}
              </div>
            )}
            
            <Button 
              type="submit" 
              disabled={isLoading}
              className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-warm hover:shadow-warm-lg transition-all duration-300 group h-14 px-12 text-lg font-semibold disabled:opacity-50"
              size="lg"
            >
              <Send className={`mr-3 h-5 w-5 transition-transform group-hover:translate-x-1 ${isLoading ? 'animate-pulse' : ''}`} />
              {isLoading ? 'Sending...' : 'Send Message'}
            </Button>
            <p className="text-sm text-muted-foreground mt-6">
              * Required fields. We typically respond within 24 hours during business days.
            </p>
          </motion.div>
        </form>
      )}
    </motion.div>
  )
}
