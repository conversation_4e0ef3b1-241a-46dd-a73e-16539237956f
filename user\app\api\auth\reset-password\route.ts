import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import crypto from 'crypto'

// In a real application, you would use a proper database
// For now, we'll simulate with a temporary storage
const resetTokens = new Map<string, { email: string, token: string, expires: Date }>()
const users = new Map<string, { email: string, password: string }>()

// Initialize with some test data (in production, this would be in your database)
if (users.size === 0) {
  users.set('<EMAIL>', {
    email: '<EMAIL>',
    password: 'hashedPassword123' // In production, this would be properly hashed
  })
}

function hashPassword(password: string): string {
  // In production, use bcrypt or similar
  return crypto.createHash('sha256').update(password).digest('hex')
}

export async function POST(request: NextRequest) {
  try {
    const { token, email, newPassword } = await request.json()

    if (!token || !email || !newPassword) {
      return NextResponse.json(
        { error: 'Token, email, and new password are required' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Check if token exists and is valid
    const tokenData = resetTokens.get(email)
    if (!tokenData) {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      )
    }

    // Verify token matches
    if (tokenData.token !== token) {
      return NextResponse.json(
        { error: 'Invalid reset token' },
        { status: 400 }
      )
    }

    // Check if token is expired
    if (new Date() > tokenData.expires) {
      resetTokens.delete(email)
      return NextResponse.json(
        { error: 'Reset token has expired' },
        { status: 400 }
      )
    }

    // Verify email matches
    if (tokenData.email !== email) {
      return NextResponse.json(
        { error: 'Token does not match email' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = users.get(email)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Update password (in production, hash the password properly)
    const hashedPassword = hashPassword(newPassword)
    users.set(email, {
      ...user,
      password: hashedPassword
    })

    // Remove the used token
    resetTokens.delete(email)

    // Clear the reset request cookie
    const cookieStore = cookies()
    cookieStore.delete('reset_request')

    console.log(`Password successfully reset for: ${email}`)

    return NextResponse.json({
      success: true,
      message: 'Password has been successfully reset'
    })

  } catch (error) {
    console.error('Reset password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to validate reset token
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const token = url.searchParams.get('token')
    const email = url.searchParams.get('email')

    if (!token || !email) {
      return NextResponse.json(
        { error: 'Token and email parameters are required' },
        { status: 400 }
      )
    }

    // Check if token exists
    const tokenData = resetTokens.get(email)
    if (!tokenData) {
      return NextResponse.json(
        { error: 'Invalid reset token' },
        { status: 400 }
      )
    }

    // Verify token matches
    if (tokenData.token !== token) {
      return NextResponse.json(
        { error: 'Invalid reset token' },
        { status: 400 }
      )
    }

    // Check if token is expired
    if (new Date() > tokenData.expires) {
      resetTokens.delete(email)
      return NextResponse.json(
        { error: 'Reset token has expired' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      valid: true,
      email: tokenData.email,
      expires: tokenData.expires
    })

  } catch (error) {
    console.error('Token validation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}