# Benzochem Industries - Chemical Trading Platform

This is a Next.js e-commerce application for chemical trading with local authentication and cart management.

## Features

- **Local Authentication System**: User registration and login using browser local storage
- **Product Catalog**: Browse chemical products with detailed specifications
- **Shopping Cart**: Add products to cart with persistent storage
- **User Dashboard**: Manage account details, business information, and GST verification
- **Responsive Design**: Mobile-friendly interface with modern UI components
- **Demo Mode**: Fully functional without external dependencies

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the root of your project. Copy from `.env.example`:

```bash
cp .env.example .env.local
```

### 2. Optional API Services

The application works without external APIs, but you can enhance it with:

#### GST Verification (Optional)
- Get RapidAPI key from [RapidAPI](https://rapidapi.com/)
- Subscribe to GST Return Status API
- Add your key to `RAPIDAPI_KEY` in `.env.local`

#### Google Places API (Optional)
- Get API key from [Google Cloud Console](https://console.cloud.google.com/)
- Enable Places API
- Add your key to `GOOGLE_PLACES_API_KEY` in `.env.local`

### 3. Installation

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Demo Usage

### Creating an Account
1. Go to `/register`
2. Fill in personal details (any valid email format works)
3. Optionally add business information
4. Complete registration

### Using the Application
1. Browse products in the catalog
2. Add items to cart (requires login)
3. Manage cart items and quantities
4. View account dashboard with business details
5. All data persists in browser local storage

## Architecture

### Authentication
- Local storage-based user management
- No external authentication service required
- Session persistence across browser tabs

### Cart Management
- User-specific cart storage
- Persistent cart state
- Real-time cart updates

### Product Data
- Mock product catalog with chemical specifications
- Search functionality
- Category-based browsing

## File Structure

```
├── app/                    # Next.js app router pages
├── components/            # React components
├── contexts/             # React contexts (auth, cart)
├── hooks/               # Custom React hooks
├── lib/                # Utility functions and services
├── public/             # Static assets
└── styles/            # CSS and styling
```

## Development

### Building for Production

```bash
npm run build
npm start
```

### Linting

```bash
npm run lint
```

## Notes

- This is a demo application using local storage for data persistence
- All user data and cart items are stored locally in the browser
- No server-side database or external authentication service is required
- Perfect for development, testing, and demonstration purposes