"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { User, Package, CreditCard, Building, Heart, Mail, FileText, RefreshCw, Clock } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useEffect, useState } from "react"

interface AccountSidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  shopifyData: any;
  user: any; // From useAuth, assuming it might have isGstVerified
}

export default function AccountSidebar({
  activeTab,
  setActiveTab,
  shopifyData,
  user
}: AccountSidebarProps) {
  const { isRefreshing, lastUpdated, refreshUserData } = useAuth()
  const [statusAnimations, setStatusAnimations] = useState<{[key: string]: boolean}>({})

  // Listen for real-time user data updates
  useEffect(() => {
    const handleUserDataUpdate = (event: CustomEvent) => {
      const updatedData = event.detail

      // Trigger animations for status changes
      const newAnimations: {[key: string]: boolean} = {}

      if (updatedData.isGstVerified !== user?.isGstVerified) {
        newAnimations.gst = true
      }

      if (updatedData.businessName !== user?.businessName) {
        newAnimations.business = true
      }

      setStatusAnimations(newAnimations)

      // Clear animations after 2 seconds
      setTimeout(() => {
        setStatusAnimations({})
      }, 2000)
    }

    window.addEventListener('user-data-updated', handleUserDataUpdate as EventListener)

    return () => {
      window.removeEventListener('user-data-updated', handleUserDataUpdate as EventListener)
    }
  }, [user])

  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Never'
    
    // Ensure date is a valid Date object
    const validDate = date instanceof Date ? date : new Date(date)
    if (isNaN(validDate.getTime())) return 'Never'
    
    const now = new Date()
    const diff = now.getTime() - validDate.getTime()
    const minutes = Math.floor(diff / 60000)

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    return validDate.toLocaleDateString()
  }
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5 }}
      className="lg:col-span-3"
    >
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Account Menu</CardTitle>
          <CardDescription>Manage your account settings and preferences</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <nav>
            <div className="space-y-1 px-1">
              <Button
                variant="ghost"
                onClick={() => setActiveTab("overview")}
                className={`w-full justify-start ${activeTab === "overview" ? "bg-teal-50 text-teal-700 hover:bg-teal-50 hover:text-teal-700" : ""}`}
              >
                <User className="h-4 w-4 mr-3" />
                Account Overview
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setActiveTab("quotations")}
                className={`w-full justify-start ${activeTab === "quotations" ? "bg-teal-50 text-teal-700 hover:bg-teal-50 hover:text-teal-700" : ""}`}
              >
                <FileText className="h-4 w-4 mr-3" />
                Quotations
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setActiveTab("business")}
                className={`w-full justify-start ${activeTab === "business" ? "bg-teal-50 text-teal-700 hover:bg-teal-50 hover:text-teal-700" : ""}`}
              >
                <Building className="h-4 w-4 mr-3" />
                Business Profile
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setActiveTab("saved")}
                className={`w-full justify-start ${activeTab === "saved" ? "bg-teal-50 text-teal-700 hover:bg-teal-50 hover:text-teal-700" : ""}`}
              >
                <Heart className="h-4 w-4 mr-3" />
                Saved Products
              </Button>
              
              <Button
                variant="ghost"
                onClick={() => setActiveTab("payment")}
                className={`w-full justify-start ${activeTab === "payment" ? "bg-teal-50 text-teal-700 hover:bg-teal-50 hover:text-teal-700" : ""}`}
              >
                <CreditCard className="h-4 w-4 mr-3" />
                Payment Methods
              </Button>
            </div>
          </nav>
        </CardContent>
      </Card>
      
      {/* Account Status Card */}
      <Card className="mt-6">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">Account Status</CardTitle>
            <div className="flex items-center space-x-2">
              {lastUpdated && (
                <div className="flex items-center text-xs text-neutral-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatLastUpdated(lastUpdated)}
                </div>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshUserData}
                disabled={isRefreshing}
                className="h-6 w-6 p-0"
              >
                <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-neutral-500" />
                <span className="text-sm">Email</span>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Verified
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Building className="h-4 w-4 mr-2 text-neutral-500" />
                <span className="text-sm">Business</span>
              </div>
              <motion.div
                animate={statusAnimations.business ? { scale: [1, 1.1, 1] } : {}}
                transition={{ duration: 0.3 }}
              >
                {shopifyData?.tradeName || shopifyData?.businessName ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Registered
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-neutral-100 text-neutral-700 border-neutral-200">
                    Not Added
                  </Badge>
                )}
              </motion.div>
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-neutral-500" />
                <span className="text-sm">GST</span>
              </div>
              <motion.div
                animate={statusAnimations.gst ? { scale: [1, 1.1, 1] } : {}}
                transition={{ duration: 0.3 }}
              >
                {shopifyData?.isGstVerified || user?.isGstVerified ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Verified
                  </Badge>
                ) : shopifyData?.gstNumber || user?.gstNumber ? (
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                    Pending
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-neutral-100 text-neutral-700 border-neutral-200">
                    Not Added
                  </Badge>
                )}
              </motion.div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}