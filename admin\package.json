{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/server": "^4.12.2", "@as-integrations/next": "^3.2.0", "@graphql-tools/schema": "^10.0.23", "@graphql-tools/utils": "^10.8.6", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/nodemailer": "^6.4.17", "@types/three": "^0.178.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "cookies": "^0.9.1", "date-fns": "^4.1.0", "dotenv": "^16.6.0", "googleapis": "^150.0.1", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "iron-session": "^8.0.4", "jose": "^6.0.11", "lucide-react": "^0.517.0", "next": "15.3.4", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-globe.gl": "^2.34.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}