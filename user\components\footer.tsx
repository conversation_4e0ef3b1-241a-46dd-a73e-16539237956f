"use client"

import Link from "next/link"
import Image from "next/image"
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react"
import { usePathname } from "next/navigation"

export default function Footer() {
  const pathname = usePathname()

  // Hide footer on waiting-list page
  if (pathname === '/waiting-list' || pathname?.startsWith('/waiting-list')) {
    return null
  }
  return (
    <footer className="bg-neutral-900 text-neutral-400">
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            {/* <Link href="/" className="inline-block mb-6">
              <Image
                src="/images/logo.png"
                alt="Benzochem Industries"
                 width={120} height={120} className="md:w-32 w-24 object-cover"
              />
            </Link> */}
            <p className="mt-20 mb-6">
              Premium chemical solutions for modern industry. Delivering high-quality powder and liquid products with
              precision and performance.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-neutral-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-neutral-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-neutral-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-neutral-400 hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>

          <div>
            <h3 className="text-white text-lg font-medium mb-6">Products</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/categories/powder" className="hover:text-white transition-colors">
                  Powder Products
                </Link>
              </li>
              <li>
                <Link href="/categories/liquid" className="hover:text-white transition-colors">
                  Liquid Products
                </Link>
              </li>
              <li>
                <Link href="/new-arrivals" className="hover:text-white transition-colors">
                  New Arrivals
                </Link>
              </li>
              <li>
                <Link href="/best-sellers" className="hover:text-white transition-colors">
                  Best Sellers
                </Link>
              </li>
              <li>
                <Link href="/special-offers" className="hover:text-white transition-colors">
                  Special Offers
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-medium mb-6">Company</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/blog" className="hover:text-white transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/sustainability" className="hover:text-white transition-colors">
                  Sustainability
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-white transition-colors">
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/cookie-settings" className="hover:text-white transition-colors">
                  Cookie Settings
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-white text-lg font-medium mb-6">Contact</h3>
            <ul className="space-y-4">
              <li className="flex">
                <MapPin className="h-5 w-5 mr-3 flex-shrink-0" />
                <span>E-45 Jitali Road</span>
              </li>
              <li className="flex">
                <Phone className="h-5 w-5 mr-3 flex-shrink-0" />
                <Link href="tel:+1234567890" className="hover:text-white transition-colors">
                  +91 83206 67594
                </Link>
              </li>
              <li className="flex">
                <Mail className="h-5 w-5 mr-3 flex-shrink-0" />
                <Link href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-neutral-800 mt-12 pt-8 text-sm text-center">
          <p>&copy; {new Date().getFullYear()} Benzochem Industries. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}