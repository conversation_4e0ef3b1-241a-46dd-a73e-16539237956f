"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useRouter } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Search, 
  X, 
  Clock, 
  TrendingUp,
  Hash,
  Tag,
  Package,
  Beaker,
  Atom,
  History,
  Trash2
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

interface SearchSuggestion {
  text: string
  type: 'product' | 'cas' | 'formula' | 'chemical' | 'tag' | 'category' | 'general'
}

interface SearchHistoryItem {
  query: string
  timestamp: number
  resultCount?: number
}

interface SearchWithSuggestionsProps {
  placeholder?: string
  onSearch?: (query: string) => void
  className?: string
  showRecentSearches?: boolean
  autoFocus?: boolean
  initialQuery?: string
}

const getSuggestionIcon = (type: string) => {
  switch (type) {
    case 'product': return Package
    case 'cas': return Hash
    case 'formula': return Atom
    case 'chemical': return Beaker
    case 'tag': return Tag
    case 'category': return TrendingUp
    default: return Search
  }
}

const getSuggestionLabel = (type: string) => {
  switch (type) {
    case 'product': return 'Product'
    case 'cas': return 'CAS Number'
    case 'formula': return 'Formula'
    case 'chemical': return 'Chemical'
    case 'tag': return 'Tag'
    case 'category': return 'Category'
    default: return 'Search'
  }
}

export default function SearchWithSuggestions({
  placeholder = "Search by product name, CAS number, molecular formula...",
  onSearch,
  className,
  showRecentSearches = true,
  autoFocus = false,
  initialQuery = ""
}: SearchWithSuggestionsProps) {
  const router = useRouter()
  const [query, setQuery] = useState(initialQuery)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [recentSearches, setRecentSearches] = useState<SearchHistoryItem[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const suggestionsTimeoutRef = useRef<NodeJS.Timeout>()

  // Load recent searches on mount
  useEffect(() => {
    if (showRecentSearches) {
      loadRecentSearches()
    }
  }, [showRecentSearches])

  // Auto focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus()
    }
  }, [autoFocus])

  // Update query when initialQuery changes
  useEffect(() => {
    setQuery(initialQuery)
  }, [initialQuery])

  // Load recent searches from server
  const loadRecentSearches = async () => {
    try {
      const response = await fetch('/api/search-history')
      const result = await response.json()
      if (result.success && Array.isArray(result.data)) {
        setRecentSearches(result.data)
      }
    } catch (error) {
      console.error('Error loading recent searches:', error)
    }
  }

  // Save search to history
  const saveSearchToHistory = async (searchQuery: string, resultCount?: number) => {
    try {
      await fetch('/api/search-history', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery, resultCount })
      })
      // Reload recent searches to update UI
      loadRecentSearches()
    } catch (error) {
      console.error('Error saving search to history:', error)
    }
  }

  // Clear search history
  const clearSearchHistory = async () => {
    try {
      await fetch('/api/search-history', { method: 'DELETE' })
      setRecentSearches([])
    } catch (error) {
      console.error('Error clearing search history:', error)
    }
  }

  // Fetch suggestions with debouncing
  const fetchSuggestions = useCallback(async (searchQuery: string) => {
    if (searchQuery.trim().length < 2) {
      setSuggestions([])
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/search-suggestions?q=${encodeURIComponent(searchQuery)}`)
      const result = await response.json()
      if (result.success && Array.isArray(result.data)) {
        setSuggestions(result.data)
      } else {
        setSuggestions([])
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      setSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Handle input change with debouncing
  const handleInputChange = (value: string) => {
    setQuery(value)
    setSelectedIndex(-1)
    
    // Clear existing timeout
    if (suggestionsTimeoutRef.current) {
      clearTimeout(suggestionsTimeoutRef.current)
    }
    
    // Set new timeout for fetching suggestions
    suggestionsTimeoutRef.current = setTimeout(() => {
      fetchSuggestions(value)
    }, 300)
  }

  // Handle search execution
  const executeSearch = (searchQuery: string) => {
    const trimmedQuery = searchQuery.trim()
    if (!trimmedQuery) return

    // Save to search history
    saveSearchToHistory(trimmedQuery)
    
    // Close dropdown
    setIsOpen(false)
    setQuery(trimmedQuery)
    
    // Execute search
    if (onSearch) {
      onSearch(trimmedQuery)
    } else {
      // Navigate to search page
      router.push(`/search?q=${encodeURIComponent(trimmedQuery)}`)
    }
  }

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const totalItems = suggestions.length + (showRecentSearches ? recentSearches.length : 0)
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev + 1) % totalItems)
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev <= 0 ? totalItems - 1 : prev - 1)
        break
      case 'Enter':
        e.preventDefault()
        if (selectedIndex >= 0) {
          if (selectedIndex < suggestions.length) {
            executeSearch(suggestions[selectedIndex].text)
          } else {
            const recentIndex = selectedIndex - suggestions.length
            executeSearch(recentSearches[recentIndex].query)
          }
        } else {
          executeSearch(query)
        }
        break
      case 'Escape':
        setIsOpen(false)
        setSelectedIndex(-1)
        inputRef.current?.blur()
        break
    }
  }

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Show dropdown when there are suggestions or recent searches
  const shouldShowDropdown = isOpen && (suggestions.length > 0 || (showRecentSearches && recentSearches.length > 0))

  return (
    <div className={cn("relative w-full max-w-2xl", className)} ref={dropdownRef}>
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          className="pl-10 pr-10 h-12 text-base"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setQuery("")
              setSuggestions([])
              setSelectedIndex(-1)
              inputRef.current?.focus()
            }}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Dropdown */}
      <AnimatePresence>
        {shouldShowDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 z-50 mt-2"
          >
            <Card className="shadow-lg border-2">
              <CardContent className="p-0 max-h-96 overflow-y-auto">
                {/* Suggestions */}
                {suggestions.length > 0 && (
                  <div className="p-2">
                    <div className="text-xs font-medium text-muted-foreground px-2 py-1 mb-1">
                      Suggestions
                    </div>
                    {suggestions.map((suggestion, index) => {
                      const Icon = getSuggestionIcon(suggestion.type)
                      const isSelected = selectedIndex === index
                      
                      return (
                        <button
                          key={`suggestion-${index}`}
                          onClick={() => executeSearch(suggestion.text)}
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors",
                            isSelected 
                              ? "bg-primary text-primary-foreground" 
                              : "hover:bg-muted"
                          )}
                        >
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">{suggestion.text}</div>
                            <div className="text-xs opacity-70">
                              {getSuggestionLabel(suggestion.type)}
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                )}

                {/* Separator */}
                {suggestions.length > 0 && showRecentSearches && recentSearches.length > 0 && (
                  <Separator />
                )}

                {/* Recent Searches */}
                {showRecentSearches && recentSearches.length > 0 && (
                  <div className="p-2">
                    <div className="flex items-center justify-between px-2 py-1 mb-1">
                      <div className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                        <History className="h-3 w-3" />
                        Recent Searches
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearSearchHistory}
                        className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    {recentSearches.map((item, index) => {
                      const actualIndex = suggestions.length + index
                      const isSelected = selectedIndex === actualIndex
                      
                      return (
                        <button
                          key={`recent-${index}`}
                          onClick={() => executeSearch(item.query)}
                          className={cn(
                            "w-full flex items-center gap-3 px-3 py-2 text-left rounded-md transition-colors",
                            isSelected 
                              ? "bg-primary text-primary-foreground" 
                              : "hover:bg-muted"
                          )}
                        >
                          <Clock className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">{item.query}</div>
                            <div className="text-xs opacity-70">
                              {item.resultCount !== undefined && (
                                <span>{item.resultCount} results • </span>
                              )}
                              {new Date(item.timestamp).toLocaleDateString()}
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                )}

                {/* No results */}
                {suggestions.length === 0 && (!showRecentSearches || recentSearches.length === 0) && query.trim().length >= 2 && !isLoading && (
                  <div className="p-4 text-center text-muted-foreground">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <div className="text-sm">No suggestions found</div>
                  </div>
                )}

                {/* Loading */}
                {isLoading && (
                  <div className="p-4 text-center text-muted-foreground">
                    <div className="text-sm">Searching...</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}