import { NextRequest, NextResponse } from 'next/server'
import { emailService } from '@/lib/email-service'

export async function POST(request: NextRequest) {
  try {
    const { firstName, lastName, email, company, inquiryType, message } = await request.json()

    // Validate required fields
    if (!firstName || !lastName || !email || !message) {
      return NextResponse.json(
        { error: 'First name, last name, email, and message are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Prepare contact form data
    const fullName = `${firstName} ${lastName}`
    const formattedMessage = `
Name: ${fullName}
Email: ${email}
Company: ${company || 'Not provided'}
Inquiry Type: ${inquiryType || 'Not specified'}

Message:
${message}
    `.trim()

    // Send contact form email to company
    const emailResult = await emailService.sendContactFormEmail(fullName, email, formattedMessage)
    
    if (!emailResult.success) {
      console.error('Failed to send contact form email:', emailResult.error)
      return NextResponse.json(
        { error: 'Failed to send message. Please try again.' },
        { status: 500 }
      )
    }

    // Optionally send confirmation email to the user
    try {
      await emailService.sendEmail({
        to: email,
        subject: 'Thank you for contacting Benzochem Industries',
        template: 'contact-confirmation',
        data: {
          firstName,
          message: 'Thank you for reaching out to us. We have received your message and will get back to you within 24 hours.',
          supportEmail: '<EMAIL>'
        }
      })
    } catch (confirmationError) {
      // Don't fail the main request if confirmation email fails
      console.warn('Failed to send confirmation email:', confirmationError)
    }

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you within 24 hours.'
    })

  } catch (error) {
    console.error('Contact form submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}