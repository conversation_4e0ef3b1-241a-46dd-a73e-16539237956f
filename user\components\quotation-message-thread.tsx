"use client";

import { useState, useEffect, useRef } from "react";
import { useSecureQuotation } from "@/hooks/use-secure-quotation";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
    Send,
    User,
    Shield,
    Clock,
    CheckCircle2,
    AlertCircle,
    Lock,
    Unlock,
    MessageSquare,
    X
} from "lucide-react";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";

interface QuotationMessageThreadProps {
    quotationId: string;
    quotation: any;
    onClose?: () => void;
    onQuotationUpdate?: () => void;
}

interface Message {
    _id: string;
    quotationId: string;
    authorId: string;
    authorName: string;
    authorRole: "user" | "admin";
    content: string;
    messageType: "message" | "system_notification" | "closure_request" | "closure_permission_granted" | "closure_permission_rejected" | "thread_closed";
    isReadByUser?: boolean;
    isReadByAdmin?: boolean;
    readByUserAt?: number;
    readByAdminAt?: number;
    createdAt: number;
    updatedAt: number;
}

export default function QuotationMessageThread({
    quotationId,
    quotation,
    onClose,
    onQuotationUpdate
}: QuotationMessageThreadProps) {
    const [messages, setMessages] = useState<Message[]>([]);
    const [newMessage, setNewMessage] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [showRejectDialog, setShowRejectDialog] = useState(false);
    const [rejectReason, setRejectReason] = useState("");
    const messagesEndRef = useRef<HTMLDivElement>(null);

    const {
        sendMessage,
        getQuotationMessages,
        markMessagesAsRead,
        grantClosurePermission,
        rejectClosurePermission,
        refreshQuotations
    } = useSecureQuotation();

    // Load messages when component mounts
    useEffect(() => {
        loadMessages();
    }, [quotationId]);



    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
        }
    }, [messages]);

    // Mark messages as read when component mounts
    useEffect(() => {
        if (messages.length > 0) {
            markMessagesAsRead(quotationId).catch(console.error);
        }
    }, [messages, quotationId, markMessagesAsRead]);

    const loadMessages = async () => {
        try {
            setIsLoading(true);
            const fetchedMessages = await getQuotationMessages(quotationId);
            setMessages(fetchedMessages);
        } catch (error) {
            console.error("Error loading messages:", error);
            toast.error("Failed to load messages");
        } finally {
            setIsLoading(false);
        }
    };

    const handleSendMessage = async () => {
        if (!newMessage.trim() || isSubmitting) return;

        setIsSubmitting(true);
        try {
            await sendMessage(quotationId, newMessage.trim());
            setNewMessage("");

            // Reload messages to show the new one
            await loadMessages();
        } catch (error) {
            console.error("Error sending message:", error);
            toast.error("Failed to send message");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleGrantClosurePermission = async () => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        try {
            await grantClosurePermission(quotationId);

            // Reload messages to show the permission granted message
            await loadMessages();
            
            // Refresh quotations to update the thread status in the parent component
            await refreshQuotations();
            
            // Also trigger parent component update if callback provided
            if (onQuotationUpdate) {
                await onQuotationUpdate();
            }
        } catch (error) {
            console.error("Error granting closure permission:", error);
            toast.error("Failed to grant closure permission");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleRejectClosurePermission = async () => {
        if (isSubmitting) return;

        setIsSubmitting(true);
        try {
            await rejectClosurePermission(quotationId, rejectReason.trim() || undefined);

            // Reload messages to show the permission rejected message
            await loadMessages();
            
            // Refresh quotations to update the thread status in the parent component
            await refreshQuotations();
            
            // Also trigger parent component update if callback provided
            if (onQuotationUpdate) {
                await onQuotationUpdate();
            }
            
            // Close dialog and reset reason
            setShowRejectDialog(false);
            setRejectReason("");
        } catch (error) {
            console.error("Error rejecting closure permission:", error);
            toast.error("Failed to reject closure permission");
        } finally {
            setIsSubmitting(false);
        }
    };

    const getMessageTypeIcon = (messageType: string) => {
        switch (messageType) {
            case "system_notification":
                return <AlertCircle className="h-4 w-4" />;
            case "closure_request":
                return <Lock className="h-4 w-4" />;
            case "closure_permission_granted":
                return <Unlock className="h-4 w-4" />;
            case "closure_permission_rejected":
                return <X className="h-4 w-4" />;
            case "thread_closed":
                return <Lock className="h-4 w-4" />;
            default:
                return null;
        }
    };

    const getThreadStatusBadge = () => {
        if (!quotation?.threadStatus) return null;

        switch (quotation.threadStatus) {
            case "active":
                return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
            case "awaiting_user_permission":
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Permission Requested</Badge>;
            case "user_approved_closure":
                return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Approved for Closure</Badge>;
            case "closed":
                return <Badge variant="destructive">Closed</Badge>;
            default:
                return null;
        }
    };

    const canSendMessage = quotation?.threadStatus === "active" || !quotation?.threadStatus;
    const needsPermissionResponse = quotation?.threadStatus === "awaiting_user_permission";

    if (isLoading) {
        return (
            <Card className="w-full max-w-4xl mx-auto">
                <CardHeader>
                    <CardTitle>Loading Messages...</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center h-64">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="w-full max-w-4xl mx-auto">
            <CardHeader className="flex flex-row items-center justify-between">
    
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Messages */}
                <ScrollArea className="h-96 w-full border rounded-lg p-4">
                    <div className="space-y-4">
                        {messages.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <div className="space-y-2">
                                    <p className="font-medium">No messages yet</p>
                                    <p className="text-sm">Start a conversation with our team about this quotation!</p>
                                    <p className="text-xs">Ask questions, provide additional requirements, or discuss terms.</p>
                                </div>
                            </div>
                        ) : (
                            messages.map((message, index) => (
                                <div
                                    key={message._id || `message-${index}`}
                                    className={`flex gap-3 ${message.authorRole === "user" ? "justify-end" : "justify-start"
                                        }`}
                                >
                                    {message.authorRole === "admin" && (
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback>
                                                <Shield className="h-4 w-4" />
                                            </AvatarFallback>
                                        </Avatar>
                                    )}

                                    <div
                                        className={`max-w-[70%] rounded-lg p-3 ${message.authorRole === "user"
                                                ? "bg-blue-600 text-white"
                                                : message.messageType === "message"
                                                    ? "bg-gray-100 text-gray-900"
                                                    : "bg-yellow-50 text-yellow-800 border border-yellow-200"
                                            }`}
                                    >
                                        <div className="flex items-center gap-2 mb-1">
                                            {message.messageType !== "message" && getMessageTypeIcon(message.messageType)}
                                            <span className="text-xs font-medium">
                                                {message.authorName}
                                            </span>
                                            <span className="text-xs opacity-70">
                                                {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                                            </span>
                                        </div>
                                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                                        {message.authorRole === "user" && (
                                            <div className="flex items-center gap-1 mt-1 justify-end">
                                                {message.isReadByAdmin ? (
                                                    <CheckCircle2 className="h-3 w-3 opacity-70" />
                                                ) : (
                                                    <Clock className="h-3 w-3 opacity-70" />
                                                )}
                                                <span className="text-xs opacity-70">
                                                    {message.isReadByAdmin ? "Read" : "Sent"}
                                                </span>
                                            </div>
                                        )}
                                    </div>

                                    {message.authorRole === "user" && (
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback>
                                                <User className="h-4 w-4" />
                                            </AvatarFallback>
                                        </Avatar>
                                    )}
                                </div>
                            ))
                        )}
                        <div ref={messagesEndRef} />
                    </div>
                </ScrollArea>

                {/* Permission Request Response */}
                {needsPermissionResponse && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                            <Lock className="h-5 w-5 text-yellow-600" />
                            <h4 className="font-medium text-yellow-800">Thread Closure Requested</h4>
                        </div>
                        <p className="text-sm text-yellow-700 mb-3">
                            The admin has requested to close this message thread. Do you approve?
                        </p>
                        <div className="flex gap-2">
                            <Button
                                onClick={handleGrantClosurePermission}
                                disabled={isSubmitting}
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white"
                            >
                                <Unlock className="h-4 w-4 mr-2" />
                                Accept
                            </Button>
                            <Button
                                onClick={() => setShowRejectDialog(true)}
                                disabled={isSubmitting}
                                size="sm"
                                variant="outline"
                                className="border-red-300 text-red-700 hover:bg-red-50"
                            >
                                <X className="h-4 w-4 mr-2" />
                                Reject
                            </Button>
                        </div>
                    </div>
                )}

                {/* Message Input */}
                {canSendMessage && (
                    <>
                        <Separator />
                        <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MessageSquare className="h-4 w-4" />
                                <span>Send a message to our team</span>
                            </div>
                            <div className="flex gap-2">
                                <Textarea
                                    placeholder="Ask questions about pricing, delivery, specifications, or provide additional requirements..."
                                    value={newMessage}
                                    onChange={(e) => setNewMessage(e.target.value)}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" && !e.shiftKey) {
                                            e.preventDefault();
                                            handleSendMessage();
                                        }
                                    }}
                                    className="flex-1"
                                    rows={3}
                                />
                                <Button
                                    onClick={handleSendMessage}
                                    disabled={!newMessage.trim() || isSubmitting}
                                    className="self-end bg-blue-600 hover:bg-blue-700"
                                >
                                    {isSubmitting ? (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                    ) : (
                                        <Send className="h-4 w-4" />
                                    )}
                                </Button>
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Press Enter to send • Shift+Enter for new line
                            </p>
                        </div>
                    </>
                )}

                {!canSendMessage && quotation?.threadStatus === "closed" && (
                    <div className="text-center py-4 text-muted-foreground">
                        <Lock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <div className="space-y-1">
                            <p className="font-medium">Thread Closed</p>
                            <p className="text-sm">This conversation has been closed and no new messages can be sent.</p>
                            <p className="text-xs">If you need to discuss this quotation further, please contact our support team.</p>
                        </div>
                    </div>
                )}

                {quotation?.threadStatus === "user_approved_closure" && (
                    <div className="text-center py-4 text-muted-foreground">
                        <CheckCircle2 className="h-8 w-8 mx-auto mb-2 opacity-50 text-green-600" />
                        <div className="space-y-1">
                            <p className="font-medium">Thread Approved for Closure</p>
                            <p className="text-sm">You have approved this thread for closure. It will be closed soon.</p>
                        </div>
                    </div>
                )}
            </CardContent>

            {/* Reject Closure Dialog */}
            {showRejectDialog && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <Card className="w-full max-w-md mx-4">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <X className="h-5 w-5 text-red-600" />
                                Reject Thread Closure
                            </CardTitle>
                            <p className="text-sm text-muted-foreground">
                                You are rejecting the admin's request to close this message thread.
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium">Reason (Optional)</label>
                                <Textarea
                                    placeholder="Provide a reason for rejecting the closure request..."
                                    value={rejectReason}
                                    onChange={(e) => setRejectReason(e.target.value)}
                                    rows={3}
                                />
                            </div>
                            <div className="flex gap-2 justify-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setShowRejectDialog(false);
                                        setRejectReason("");
                                    }}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleRejectClosurePermission}
                                    disabled={isSubmitting}
                                    className="bg-red-600 hover:bg-red-700 text-white"
                                >
                                    <X className="h-4 w-4 mr-2" />
                                    Reject Closure
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}
        </Card>
    );
}