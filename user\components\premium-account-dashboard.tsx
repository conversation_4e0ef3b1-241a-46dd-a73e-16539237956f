"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/contexts/auth-context"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import QuotationHistory from "@/components/quotation-history"
import {
  User,
  Building,
  FileText,
  Mail,
  Phone,
  MapPin,
  CheckCircle2,
  AlertCircle,
  Edit,
  Save,
  X,
  Settings,
  Bell,
  Shield,
  TrendingUp,
  Clock,
  Star
} from "lucide-react"

export default function PremiumAccountDashboard() {
  const { user, logout } = useAuth()
  const { quotations, currentQuotation } = useSecureQuotation()
  const [activeTab, setActiveTab] = useState("overview")
  const [isEditing, setIsEditing] = useState(false)
  const [editedUser, setEditedUser] = useState(user || {})

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-xl">
          <CardContent className="pt-8 pb-6 text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <User className="w-8 h-8 text-primary" />
            </div>
            <h1 className="text-2xl font-bold mb-3 text-foreground">Access Required</h1>
            <p className="text-muted-foreground mb-8">Please log in to access your account dashboard and manage your chemical product quotations.</p>
            <Button 
              onClick={() => window.location.href = '/login'} 
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 h-12"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const handleSaveProfile = async () => {
    try {
      // TODO: Implement API call to update user profile
      // For now, just show a message that the feature is not yet implemented
      alert('Profile update feature will be implemented with server-side API. Changes are not saved yet.')
      setIsEditing(false)
    } catch (error) {
      console.error('Error updating profile:', error)
      alert('Failed to update profile. Please try again.')
    }
  }

  const pendingQuotations = quotations.filter(q => q.status === "submitted" || q.status === "under_review").length
  const quotedRequests = quotations.filter(q => q.status === "quoted").length
  const currentQuotationItems = currentQuotation?.items.length || 0

  const menuItems = [
    { id: "overview", label: "Overview", icon: TrendingUp },
    { id: "profile", label: "Profile", icon: User },
    { id: "business", label: "Business", icon: Building },
    { id: "quotations", label: "Quotations", icon: FileText },
    { id: "settings", label: "Settings", icon: Settings },
  ]

  return (
    <div className="min-h-screen pt-20">
      {/* Modern Header */}
      <div className="relative overflow-hidden">
        {/* Background with gradient and pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary via-primary to-primary/80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-background/5 to-background/10"></div>
        
        <div className="relative container mx-auto px-4 sm:px-6 py-12">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="flex-1">
              <div className="inline-flex items-center gap-2 bg-primary-foreground/10 text-primary-foreground px-3 py-1 rounded-full text-sm font-medium mb-4">
                <User className="h-4 w-4" />
                Account Dashboard
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold mb-3 text-primary-foreground">
                Welcome back, {user.firstName}
              </h1>
              <p className="dark:text-primary-foreground/80 text-slate-300 text-lg mb-6">{user.email}</p>
              
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2 bg-primary-foreground/10 text-primary-foreground px-3 py-2 rounded-lg text-sm">
                  <FileText className="h-4 w-4" />
                  <span>{currentQuotationItems} items in draft</span>
                </div>
                {user.gstNumber && (
                  <Badge className={`${user.isGstVerified ? 'bg-green-500/20 text-green-100 border-green-500/30' : 'bg-blue-500/20 text-blue-100 border-blue-500/30'} px-3 py-1`}>
                    {user.isGstVerified ? (
                      <>
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        GST Verified
                      </>
                    ) : (
                      <>
                        <FileText className="h-3 w-3 mr-1" />
                        GST Registered
                      </>
                    )}
                  </Badge>
                )}
              </div>
            </div>
            
            <Button 
              variant="outline" 
              onClick={logout} 
              className="bg-primary-foreground/10 hover:bg-primary-foreground/20 text-primary-foreground border-primary-foreground/20 hover:border-primary-foreground/30 backdrop-blur-sm"
            >
              <Settings className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8">
          {/* Modern Sidebar */}
          <div className="lg:col-span-1">
            <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-foreground">Navigation</CardTitle>
                <CardDescription className="text-sm text-muted-foreground">Manage your account</CardDescription>
              </CardHeader>
              <CardContent className="p-6 pt-0">
                <nav className="space-y-2">
                  {menuItems.map((item) => {
                    const Icon = item.icon
                    return (
                      <Button
                        key={item.id}
                        variant={activeTab === item.id ? "default" : "ghost"}
                        className={`w-full justify-start transition-all duration-200 ${
                          activeTab === item.id 
                            ? "bg-primary text-primary-foreground shadow-md hover:bg-primary/90" 
                            : "text-muted-foreground hover:text-foreground hover:bg-accent/50"
                        }`}
                        onClick={() => setActiveTab(item.id)}
                      >
                        <Icon className="h-4 w-4 mr-3" />
                        {item.label}
                      </Button>
                    )
                  })}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Overview Tab */}
            {activeTab === "overview" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* Modern Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground mb-2 font-medium">Pending Quotations</p>
                          <p className="text-3xl font-bold text-foreground">{pendingQuotations}</p>
                          <p className="text-xs text-muted-foreground mt-1">Awaiting response</p>
                        </div>
                        <div className="h-14 w-14 bg-gradient-to-br from-orange-500/10 to-orange-600/20 rounded-2xl flex items-center justify-center">
                          <Clock className="h-7 w-7 text-orange-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground mb-2 font-medium">Quoted Requests</p>
                          <p className="text-3xl font-bold text-foreground">{quotedRequests}</p>
                          <p className="text-xs text-muted-foreground mt-1">Ready for review</p>
                        </div>
                        <div className="h-14 w-14 bg-gradient-to-br from-green-500/10 to-green-600/20 rounded-2xl flex items-center justify-center">
                          <FileText className="h-7 w-7 text-green-600" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-muted-foreground mb-2 font-medium">Current Draft</p>
                          <p className="text-3xl font-bold text-foreground">{currentQuotationItems}</p>
                          <p className="text-xs text-muted-foreground mt-1">Items in cart</p>
                        </div>
                        <div className="h-14 w-14 bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl flex items-center justify-center">
                          <Edit className="h-7 w-7 text-primary" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Modern Quick Actions */}
                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-xl font-semibold text-foreground">Quick Actions</CardTitle>
                    <CardDescription className="text-muted-foreground">Common tasks and shortcuts for your workflow</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button 
                        className="h-16 bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-base font-semibold"
                        onClick={() => window.location.href = '/products'}
                      >
                        <FileText className="h-5 w-5 mr-2" />
                        Browse Products
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-16 border-border/20 hover:bg-accent/50 hover:border-primary/20 rounded-xl text-base font-semibold transition-all duration-300"
                        onClick={() => window.location.href = '/quotation'}
                      >
                        <Edit className="h-5 w-5 mr-2" />
                        View Current Quotation
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Modern Account Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-3 text-foreground">
                        <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        Personal Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-foreground font-medium">{user.firstName} {user.lastName}</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-foreground">{user.email}</span>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-foreground">{user.phone || 'Not provided'}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {user.businessName && (
                    <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-3 text-foreground">
                          <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                            <Building className="h-4 w-4 text-primary" />
                          </div>
                          Business Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="p-3 bg-muted/20 rounded-lg">
                          <p className="text-sm text-muted-foreground mb-1">Trade Name</p>
                          <p className="font-semibold text-foreground">{user.businessName}</p>
                        </div>
                        {user.gstNumber && (
                          <div className="p-3 bg-muted/20 rounded-lg">
                            <p className="text-sm text-muted-foreground mb-2">GST Number</p>
                            <div className="flex items-center gap-2">
                              <p className="font-semibold text-foreground">{user.gstNumber}</p>
                              <Badge variant={user.isGstVerified ? "default" : "secondary"} className={`text-xs ${user.isGstVerified ? 'bg-green-100 text-green-700 border-green-200' : 'bg-blue-100 text-blue-600 border-blue-200'}`}>
                                {user.isGstVerified ? "Verified" : "Registered"}
                              </Badge>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </div>
              </motion.div>
            )}

            {/* Modern Profile Tab */}
            {activeTab === "profile" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <User className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-foreground">Personal Information</CardTitle>
                        <CardDescription className="text-muted-foreground">Update your personal details and contact information</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="firstName" className="text-sm font-semibold text-foreground">First Name</Label>
                        <Input
                          id="firstName"
                          value={editedUser.firstName || ''}
                          onChange={(e) => setEditedUser(prev => ({ ...prev, firstName: e.target.value }))}
                          className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="lastName" className="text-sm font-semibold text-foreground">Last Name</Label>
                        <Input
                          id="lastName"
                          value={editedUser.lastName || ''}
                          onChange={(e) => setEditedUser(prev => ({ ...prev, lastName: e.target.value }))}
                          className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-semibold text-foreground">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={editedUser.email || ''}
                        onChange={(e) => setEditedUser(prev => ({ ...prev, email: e.target.value }))}
                        className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-sm font-semibold text-foreground">Phone Number</Label>
                      <Input
                        id="phone"
                        value={editedUser.phone || ''}
                        onChange={(e) => setEditedUser(prev => ({ ...prev, phone: e.target.value }))}
                        className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                        placeholder="Enter your phone number"
                      />
                    </div>
                    <div className="pt-4">
                      <Button onClick={handleSaveProfile} className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300">
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Modern Business Tab */}
            {activeTab === "business" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Building className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-foreground">Business Information</CardTitle>
                        <CardDescription className="text-muted-foreground">Manage your business details and GST information</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="businessName" className="text-sm font-semibold text-foreground">Trade Name</Label>
                      <Input
                        id="businessName"
                        value={editedUser.businessName || ''}
                        onChange={(e) => setEditedUser(prev => ({ ...prev, businessName: e.target.value }))}
                        className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                        placeholder="Enter your business/trade name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gstNumber" className="text-sm font-semibold text-foreground">GST Number</Label>
                      <Input
                        id="gstNumber"
                        value={editedUser.gstNumber || ''}
                        onChange={(e) => setEditedUser(prev => ({ ...prev, gstNumber: e.target.value }))}
                        className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20"
                        placeholder="Enter your GST registration number"
                      />
                      {user.isGstVerified && (
                        <div className="flex items-center gap-2 text-sm text-green-600">
                          <CheckCircle2 className="h-4 w-4" />
                          <span>GST number verified</span>
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="businessAddress" className="text-sm font-semibold text-foreground">Business Address</Label>
                      <Textarea
                        id="businessAddress"
                        value={editedUser.principalPlaceOfBusiness || ''}
                        onChange={(e) => setEditedUser(prev => ({ ...prev, principalPlaceOfBusiness: e.target.value }))}
                        rows={4}
                        className="border-border/20 bg-background/50 focus:border-primary focus:ring-primary/20 resize-none"
                        placeholder="Enter your complete business address"
                      />
                    </div>
                    <div className="pt-4">
                      <Button onClick={handleSaveProfile} className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300">
                        <Save className="h-4 w-4 mr-2" />
                        Save Business Info
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Modern Quotations Tab */}
            {activeTab === "quotations" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <FileText className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-foreground">Quotation History</CardTitle>
                        <CardDescription className="text-muted-foreground">View and manage your quotation requests and responses</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <QuotationHistory />
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Modern Settings Tab */}
            {activeTab === "settings" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Bell className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-foreground">Notifications</CardTitle>
                        <CardDescription className="text-muted-foreground">Manage your notification preferences and alerts</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-border/10">
                        <div>
                          <p className="font-semibold text-foreground">Email Notifications</p>
                          <p className="text-sm text-muted-foreground">Receive updates about your quotations and account</p>
                        </div>
                        <Button variant="outline" size="sm" className="border-border/20 hover:bg-accent/50 hover:border-primary/20">
                          Configure
                        </Button>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-border/10">
                        <div>
                          <p className="font-semibold text-foreground">SMS Notifications</p>
                          <p className="text-sm text-muted-foreground">Get SMS updates for urgent matters and alerts</p>
                        </div>
                        <Button variant="outline" size="sm" className="border-border/20 hover:bg-accent/50 hover:border-primary/20">
                          Configure
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-2xl shadow-sm">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Shield className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-foreground">Security</CardTitle>
                        <CardDescription className="text-muted-foreground">Manage your account security and authentication settings</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-border/10">
                        <div>
                          <p className="font-semibold text-foreground">Change Password</p>
                          <p className="text-sm text-muted-foreground">Update your account password for better security</p>
                        </div>
                        <Button variant="outline" size="sm" className="border-border/20 hover:bg-accent/50 hover:border-primary/20">
                          Change
                        </Button>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-muted/20 rounded-xl border border-border/10">
                        <div>
                          <p className="font-semibold text-foreground">Two-Factor Authentication</p>
                          <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
                        </div>
                        <Button variant="outline" size="sm" className="border-border/20 hover:bg-accent/50 hover:border-primary/20">
                          Enable
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}