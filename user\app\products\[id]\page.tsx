import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from 'react'
import ProductDetail from "@/components/product-detail"
import ProductSpecifications from "@/components/product-specifications"
import RelatedProducts from "@/components/related-products"
import { Skeleton } from "@/components/ui/skeleton"
import { Card } from "@/components/ui/card"
import { getProductById } from "@/lib/api-client"

interface ProductPageProps {
  params: {
    id: string
  }
}

// Dynamic rendering - no static generation
// This ensures all product data is fetched in real-time from the API
export const dynamic = 'force-dynamic'

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const { id } = await params
    const product = await getProductById(id)
    
    if (!product) {
      return {
        title: "Product Not Found | Benzochem Industries",
        description: "The requested product could not be found.",
      }
    }
    
    return {
      title: `${product.title} | Benzochem Industries`,
      description: product.description,
    }
  } catch (error) {
    return {
      title: "Product Not Found | Benzochem Industries",
      description: "The requested product could not be found.",
    }
  }
}

// Enhanced skeleton component for the page
function ProductPageLoadingSkeleton() {
  return (
    <main className="flex min-h-screen flex-col pt-20 bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Skeleton for Enhanced Breadcrumbs */}
        <div className="mb-8">
          <Card className="vanilla-card border-0 bg-gradient-to-r from-muted/30 to-muted/10 shadow-sm">
            <div className="p-4">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-16 rounded" />
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-20 rounded" />
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-24 rounded" />
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-4 rounded" />
                <Skeleton className="h-4 w-32 rounded" />
              </div>
            </div>
          </Card>
        </div>

        {/* Skeleton for ProductDetail */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          <div className="space-y-6"> {/* Left column: Image Gallery */}
            <Skeleton className="h-[500px] w-full rounded-2xl" /> {/* Main Image */}
            <div className="grid grid-cols-4 gap-3"> {/* Thumbnails */}
              <Skeleton className="aspect-square rounded-xl" />
              <Skeleton className="aspect-square rounded-xl" />
              <Skeleton className="aspect-square rounded-xl" />
              <Skeleton className="aspect-square rounded-xl" />
            </div>
            {/* Product Features Skeleton */}
            <Card className="vanilla-card border-0">
              <div className="p-6">
                <Skeleton className="h-6 w-40 mb-4 rounded" />
                <div className="grid grid-cols-2 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex items-start space-x-3 p-4 rounded-xl bg-muted/30">
                      <Skeleton className="h-8 w-8 rounded-xl" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-20 mb-1 rounded" />
                        <Skeleton className="h-3 w-24 rounded" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
          
          <div className="space-y-6"> {/* Right column: Product Info */}
            {/* Header Section */}
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-6 w-24 rounded-full" />
                  <Skeleton className="h-6 w-20 rounded-full" />
                </div>
                <Skeleton className="h-10 w-10 rounded-full" />
              </div>
              
              <Skeleton className="h-10 w-3/4 rounded" /> {/* Title */}
              
              <div className="flex items-center space-x-4">
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-4 rounded" />
                  ))}
                  <Skeleton className="h-4 w-24 ml-2 rounded" />
                </div>
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              
              {/* Description */}
              <div className="space-y-2">
                <Skeleton className="h-4 w-full rounded" />
                <Skeleton className="h-4 w-full rounded" />
                <Skeleton className="h-4 w-3/4 rounded" />
              </div>
            </div>

            {/* Specifications Tabs Skeleton */}
            <Card className="vanilla-card border-0">
              <div className="p-6">
                <div className="grid grid-cols-2 gap-1 bg-muted/50 p-1 rounded-lg mb-6">
                  <Skeleton className="h-10 rounded-md" />
                  <Skeleton className="h-10 rounded-md" />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 rounded-xl bg-muted/30">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-8 w-8 rounded-xl" />
                        <div>
                          <Skeleton className="h-3 w-16 mb-1 rounded" />
                          <Skeleton className="h-4 w-20 rounded" />
                        </div>
                      </div>
                      <Skeleton className="h-6 w-6 rounded" />
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Purchase Section Skeleton */}
            <Card className="vanilla-card border-0">
              <div className="p-6 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-16 mb-3 rounded" />
                    <Skeleton className="h-12 w-full rounded-xl" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-20 mb-3 rounded" />
                    <Skeleton className="h-12 w-full rounded-xl" />
                  </div>
                </div>
                <Skeleton className="h-14 w-full rounded-xl" /> {/* Add to quotation button */}
                <div className="grid grid-cols-2 gap-3">
                  <Skeleton className="h-12 rounded-xl" />
                  <Skeleton className="h-12 rounded-xl" />
                </div>
              </div>
            </Card>

            {/* Trust Indicators Skeleton */}
            <div className="grid grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex flex-col items-center text-center p-4 rounded-xl bg-muted/30">
                  <Skeleton className="h-6 w-6 mb-2 rounded" />
                  <Skeleton className="h-3 w-16 rounded" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skeleton for ProductSpecifications */}
        <div className="mt-16">
          <Skeleton className="h-8 w-1/3 mb-6 rounded" /> {/* Section Title: "Product Specifications" */}
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => ( // Assuming 3 spec rows for skeleton
              <div key={i} className="grid grid-cols-3 gap-4"> {/* Key-value pair */}
                <Skeleton className="h-4 w-1/3 rounded" /> {/* Spec Name */}
                <Skeleton className="h-4 w-2/3 col-span-2 rounded" /> {/* Spec Value */}
              </div>
            ))}
          </div>
        </div>

        {/* Skeleton for RelatedProducts */}
        <div className="mt-24 mb-16">
          <Skeleton className="h-8 w-1/4 mb-8 rounded" /> {/* Section Title */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="vanilla-card border-0">
                <div className="p-4">
                  <Skeleton className="aspect-square w-full rounded-lg mb-4" /> {/* Image */}
                  <Skeleton className="h-6 w-3/4 mb-2 rounded" /> {/* Title */}
                  <Skeleton className="h-4 w-1/2 rounded" /> {/* Price */}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}

// Component to fetch and render actual content
async function ProductPageContent({ productId }: { productId: string }) {
  try {
    const product = await getProductById(productId)
    
    if (!product) {
      notFound()
    }

    return (
      <main className="flex min-h-screen flex-col pt-20">
        <ProductDetail product={product} />
        
        <div className="container mx-auto px-4 py-8">
          <ProductSpecifications product={product} />
          
          <RelatedProducts 
            currentProductId={productId}
            category={product.collections?.edges?.[0]?.node?.title || ""}
          />
        </div>
      </main>
    )
  } catch (error) {
    console.error("Error in ProductPageContent:", error)
    notFound()
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { id } = await params

  return (
    <Suspense fallback={<ProductPageLoadingSkeleton />}>
      <ProductPageContent productId={id} />
    </Suspense>
  )
}