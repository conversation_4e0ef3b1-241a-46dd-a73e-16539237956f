"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { Linkedin, Mail } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TeamMemberProps {
  name: string
  position: string
  image: string
  bio: string
}

export default function TeamMember({ name, position, image, bio }: TeamMemberProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg overflow-hidden shadow-sm"
    >
      <div className="relative aspect-square">
        <Image src={image || "/placeholder.svg"} alt={name} fill className="object-contain" />
      </div>
      <div className="p-6">
        <h3 className="text-lg font-medium">{name}</h3>
        <p className="text-primary text-sm mb-3">{position}</p>
        <p className="text-muted-foreground text-sm mb-4">{bio}</p>
        <div className="flex space-x-2">
          <Button variant="ghost" size="icon" className="rounded-full">
            <Linkedin className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="rounded-full">
            <Mail className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
