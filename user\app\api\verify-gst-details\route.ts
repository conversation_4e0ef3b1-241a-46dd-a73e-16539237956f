import { NextResponse } from 'next/server';
import { getRapidAPIConfig } from '@/lib/env-validation';

// Dynamic API route for real-time data processing
export const dynamic = 'force-dynamic';

export async function POST(request: Request) {
  let rapidApiConfig;

  try {
    rapidApiConfig = getRapidAPIConfig();
    console.log('🔧 GST API Configuration loaded successfully');
  } catch (error) {
    console.error("RapidAPI GST environment variables are not properly configured:", error);
    return NextResponse.json({
      isValid: false,
      message: "API configuration error. Please check server configuration."
    }, { status: 500 });
  }

  try {
    const { gstNumber } = await request.json();

    if (!gstNumber || typeof gstNumber !== 'string') {
      return NextResponse.json({ isValid: false, message: "GST number is required." }, { status: 400 });
    }

    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    if (!gstRegex.test(gstNumber.toUpperCase())) {
      return NextResponse.json({ isValid: false, message: "Invalid GST number format." }, { status: 400 });
    }

    const targetUrl = `${rapidApiConfig.gstBaseUrl}/gstin/${gstNumber.toUpperCase()}`;


    const apiResponse = await fetch(targetUrl, {
      method: 'GET',
      headers: {
        'x-rapidapi-key': rapidApiConfig.key,
        'x-rapidapi-host': rapidApiConfig.gstHost,
        'Content-Type': 'application/json', // As per RapidAPI snippet
      },
    });

    const responseText = await apiResponse.text(); // Get text first for robust error handling
    console.log("Raw RapidAPI GST Response Text:", responseText);

    let result;
    try {
      result = JSON.parse(responseText); // Try to parse as JSON
    } catch (e) {
      console.error("Failed to parse RapidAPI response as JSON:", e);
      console.error("Response text was:", responseText);
      // If RapidAPI returns non-JSON for errors but with a non-200 status, apiResponse.ok would be false.
      // If it's a 200 OK with non-JSON, that's an issue with the API or our expectation.
      return NextResponse.json({ isValid: false, message: `Error fetching GST details: Unexpected response format from provider. Status: ${apiResponse.status}` }, { status: 500 });
    }
    
    console.log("Parsed RapidAPI GST Response:", JSON.stringify(result, null, 2));

    if (!apiResponse.ok) {
      // RapidAPI often includes error details in the JSON body even for non-2xx responses
      console.error(`RapidAPI GST request failed with status ${apiResponse.status}:`, result);
      return NextResponse.json({ isValid: false, message: `GST verification failed. Please enter a valid GST number.` }, { status: apiResponse.status });
    }
    
    // IMPORTANT: The response structure from RapidAPI's "GST Return Status" needs to be accurately mapped.
    // The following mapping is an educated guess based on common GST fields and the previous API.
    // YOU MUST VERIFY AND ADJUST THIS MAPPING based on the actual successful response from RapidAPI.
    
    const gstData = result.data || result; // If result has a 'data' property, use it, otherwise use result directly.

    if (typeof gstData !== 'object' || gstData === null) {
        console.error("Unexpected response data structure from RapidAPI GST:", gstData);
        return NextResponse.json({ isValid: false, message: "Could not parse GST details from provider (unexpected data structure)." }, { status: 500 });
    }

    // Check for a success flag or specific error messages if RapidAPI returns 200 OK for "not found".
    // If 'gstData.lgnm' (Legal Name) is missing, we might assume it's not a valid/found GSTIN.
    // Some APIs return a specific field like `success: false` or an error message within the 200 OK.
    if (gstData.error || (gstData.message && !gstData.lgnm && !gstData.tradNm) ) { // Example check for error in 200 OK
        return NextResponse.json({
            isValid: false,
            message: gstData.error || gstData.message || "GST number not found or is invalid via RapidAPI.",
            gstin: gstNumber.toUpperCase()
        }, { status: 200 });
    }
    
    // If we reach here with a 200 OK and no explicit error in the body, assume data is present.
    // If 'lgnm' or 'tradNm' is still missing, it implies the API didn't find the GSTIN or it's invalid.
     if (!gstData.lgnm && !gstData.tradNm) {
        return NextResponse.json({
            isValid: false,
            message: "GST number not found or is invalid (no business name returned).",
            gstin: gstNumber.toUpperCase()
        }, { status: 200 });
    }


    const isActive = gstData.sts ? gstData.sts.toLowerCase() === "active" : false;

    let formattedDateOfRegistration = gstData.rgdt; // DD/MM/YYYY from API
    if (gstData.rgdt && /^\d{2}\/\d{2}\/\d{4}$/.test(gstData.rgdt)) {
      const parts = gstData.rgdt.split('/');
      formattedDateOfRegistration = `${parts[2]}-${parts[1]}-${parts[0]}`; // YYYY-MM-DD
    } else if (gstData.rgdt) {
      // If format is unexpected but value exists, log and use as is or nullify
      console.warn(`Unexpected date format for rgdt: ${gstData.rgdt}. Expected DD/MM/YYYY.`);
      // formattedDateOfRegistration = null; // Or keep as is if input field can handle it
    }


    const responsePayload = {
      isValid: isActive,
      message: gstData.message || (isActive ? "GST details verified via RapidAPI." : (gstData.sts ? `GSTIN status: ${gstData.sts}` : "GSTIN status unknown.")),
      gstin: gstData.gstin || gstNumber.toUpperCase(),
      businessName: gstData.tradNm || gstData.lgnm, // Removed as per new requirement
      legalNameOfBusiness: gstData.lgnm,
      tradeName: gstData.tradeName,
      dateOfRegistration: formattedDateOfRegistration, // Use formatted date
      constitutionOfBusiness: gstData.ctb,
      taxpayerType: gstData.dty || gstData.taxPayerType, // Check actual field name from RapidAPI
      status: gstData.sts,
      principalPlaceOfBusiness: (gstData.adr) ? gstData.adr :
                                (gstData.adr ? `${gstData.adr.bno || ''} ${gstData.adr.bnm || ''} ${gstData.adr.st || ''} ${gstData.adr.loc || ''} ${gstData.adr.city || ''} ${gstData.adr.dst || ''} ${gstData.adr.pncd || ''} ${gstData.adr.stcd || ''}`.replace(/\s+/g, ' ').trim() : undefined),
      principalPlaceEmail: gstData.adr?.em,
      principalPlaceMobile: gstData.adr?.mb,
      natureOfCoreBusinessActivity: Array.isArray(gstData.nba) ? gstData.nba.join(', ') : gstData.nba,
    };
    return NextResponse.json(responsePayload, { status: 200 });

  } catch (error: any) {
    console.error("API Error verifying GST via RapidAPI:", error);
    let message = "An unexpected error occurred while fetching from RapidAPI.";
    if (error.message) {
        message = error.message;
    }
    if (error.cause) {
        console.error("Fetch error cause (RapidAPI):", error.cause);
        message = `Network error or issue reaching RapidAPI GST service: ${error.message}`;
    }
    return NextResponse.json({ isValid: false, message }, { status: 500 });
  }
}