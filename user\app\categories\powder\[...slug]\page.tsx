import type { Metadata } from "next"
import { Suspense } from 'react' 
import CategoryHeader from "@/components/category-header"
import ProductGrid from "@/components/product-grid"
import { getProductsByCollection } from "@/lib/products"
import { Skeleton } from "@/components/ui/skeleton" 

export const metadata: Metadata = {
  title: "Powder Products | Benzochem Industries",
  description: "Browse our premium powder chemical products for industrial and laboratory applications.",
}

// Dynamic rendering - no static generation
// This ensures all category data is fetched in real-time from the API
export const dynamic = 'force-dynamic'

// Basic skeleton for an individual product card (copied from liquid/page.tsx)
function BasicProductCardSkeleton() {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3">
      <Skeleton className="h-40 w-full rounded-md" /> {/* Image */}
      <Skeleton className="h-6 w-3/4 rounded" /> {/* Title */}
      <Skeleton className="h-4 w-full rounded" /> {/* Description line 1 */}
      <Skeleton className="h-4 w-5/6 rounded" /> {/* Description line 2 */}
      <div className="flex justify-between items-center pt-2">
        <Skeleton className="h-5 w-1/3 rounded" /> {/* Price */}
        <Skeleton className="h-8 w-1/3 rounded" /> {/* Button */}
      </div>
    </div>
  );
}

// Skeleton for the product grid section of a category page (copied from liquid/page.tsx)
function CategoryPageProductGridSkeleton() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="space-y-8">
        {/* Skeleton for Header (Title, Count) and Controls (Filter, Sort) */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <Skeleton className="h-7 w-32 mb-1 rounded" /> {/* "Products" title */}
            <Skeleton className="h-4 w-24 rounded" /> {/* Product count */}
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Filter Button */}
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Sort Button */}
          </div>
        </div>

        {/* Skeleton for Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => ( 
            <BasicProductCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  )
}

// Component to fetch and render actual category content
async function PowderCategoryPageContent() {
  const products = await getProductsByCollection("powder")
  // console.log("Powder category page received", products.length, "products") // Original log

  return (
    <div className="container mx-auto px-4 py-12">
      <ProductGrid products={products} />
    </div>
  )
}

export default async function PowderCategoryPage() { // No params needed here
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <CategoryHeader
        title="Powder Products"
        description="High-purity powder chemicals for industrial and laboratory applications"
        image="/images/powder-category.png" // Ensure this image exists or use a placeholder
      />
      <Suspense fallback={<CategoryPageProductGridSkeleton />}>
        <PowderCategoryPageContent />
      </Suspense>
    </main>
  )
}