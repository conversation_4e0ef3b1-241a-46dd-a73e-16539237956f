"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useAuth } from "@/contexts/auth-context"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Eye, EyeOff, Loader2, Mail, Lock, AlertCircle, CheckCircle, HelpCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function LoginForm() {
  const { login, isLoading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string | null>>({})
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)

  // Check for messages from URL params
  const message = searchParams.get('message')

  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return "Please enter your email address"
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) return "Please enter a valid email address"
    return null
  }

  const validatePassword = (password: string) => {
    if (!password) return "Please enter your password"
    return null
  }

  const validateForm = () => {
    const errors: Record<string, string | null> = {}
    errors.email = validateEmail(formData.email)
    errors.password = validatePassword(formData.password)

    setFieldErrors(errors)
    return Object.values(errors).every((err) => err === null)
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: null }))
    }
    // Clear general error when user starts typing
    if (error) {
      setError(null)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    if (!validateForm()) {
      return
    }

    try {
      const result = await login(formData.email, formData.password)

      if (result.success) {
        // Redirect to dashboard or intended page
        const redirectTo = searchParams.get('redirect') || '/account'
        router.push(redirectTo)
      } else {
        setError(result.error || "Login failed. Please try again.")
      }
    } catch (error) {
      console.error("Login error:", error)
      setError("An unexpected error occurred. Please try again.")
    }
  }

  const handleForgotPassword = () => {
    // Navigate to forgot password page
    router.push('/forgot-password')
  }

  return (
    <div className="w-full space-y-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Success Message */}
        {message && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-700">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Account Info */}
        <Alert className="border-primary/20 bg-primary/5">
          <HelpCircle className="h-4 w-4 text-primary" />
          <AlertDescription className="text-primary">
            <span className="font-medium">Account Access:</span> Sign in with your registered credentials. 
            New users need to register and wait for admin approval.
          </AlertDescription>
        </Alert>

        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
            <Mail className="w-4 h-4 text-primary" />
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            className={`transition-all duration-200 ${
              fieldErrors.email 
                ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                : "focus:border-primary focus:ring-primary/20"
            }`}
            placeholder="Enter your email address"
          />
          {fieldErrors.email && (
            <p className="text-destructive text-sm flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {fieldErrors.email}
            </p>
          )}
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="text-sm font-medium flex items-center gap-2">
              <Lock className="w-4 h-4 text-primary" />
              Password
            </Label>
            <button
              type="button"
              onClick={handleForgotPassword}
              className="text-sm text-primary hover:text-primary/80 font-medium transition-colors duration-200"
            >
              Forgot password?
            </button>
          </div>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              className={`pr-10 transition-all duration-200 ${
                fieldErrors.password 
                  ? "border-destructive focus:border-destructive focus:ring-destructive/20" 
                  : "focus:border-primary focus:ring-primary/20"
              }`}
              placeholder="Enter your password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors duration-200"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
          {fieldErrors.password && (
            <p className="text-destructive text-sm flex items-center gap-1">
              <AlertCircle className="w-3 h-3" />
              {fieldErrors.password}
            </p>
          )}
        </div>

        {/* Remember Me */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="rememberMe"
            checked={formData.rememberMe}
            onCheckedChange={(checked) =>
              handleInputChange("rememberMe", checked as boolean)
            }
          />
          <Label htmlFor="rememberMe" className="text-sm font-medium cursor-pointer">
            Remember me for 30 days
          </Label>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full h-11 font-medium transition-all duration-200 hover:shadow-md"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Signing In...
            </>
          ) : (
            "Sign In to Your Account"
          )}
        </Button>
      </form>

      {/* Help Section */}
      <div className="bg-muted/50 rounded-xl p-4 space-y-3">
        <div className="flex items-center gap-2">
          <HelpCircle className="w-4 h-4 text-primary" />
          <h4 className="font-medium text-foreground">Need Help?</h4>
        </div>
        <ul className="text-sm text-muted-foreground space-y-2 ml-6">
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            New users must register and wait for admin approval
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            Contact support if you have login issues
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            Ensure your account has been approved by admin
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
            Check your email for account status updates
          </li>
        </ul>
      </div>
    </div>
  )
}