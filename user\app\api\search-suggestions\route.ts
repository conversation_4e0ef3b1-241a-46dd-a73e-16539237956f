import { NextRequest, NextResponse } from 'next/server'
import { apiClient } from '@/lib/api-client'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({ success: true, data: [] })
    }

    // Get products that match the query for suggestions
    const response = await apiClient.getProducts({ 
      search: query.trim(), 
      limit: 8 // Limit suggestions to 8 items
    })

    if (!response.success || !response.data || !Array.isArray(response.data)) {
      return NextResponse.json({ success: true, data: [] })
    }

    // Extract unique suggestions from products
    const suggestions = new Set<string>()
    const queryLower = query.toLowerCase()

    response.data.forEach((product: any) => {
      // Add product title if it matches
      if (product.title && product.title.toLowerCase().includes(queryLower)) {
        suggestions.add(product.title)
      }

      // Add CAS numbers if they match
      if (product.casNumber && product.casNumber.toLowerCase().includes(queryLower)) {
        suggestions.add(product.casNumber)
      }

      // Add molecular formulas if they match
      if (product.molecularFormula && product.molecularFormula.toLowerCase().includes(queryLower)) {
        suggestions.add(product.molecularFormula)
      }

      // Add chemical names if they match
      if (product.chemicalName && product.chemicalName.toLowerCase().includes(queryLower)) {
        suggestions.add(product.chemicalName)
      }

      // Add tags if they match
      if (product.tags && Array.isArray(product.tags)) {
        product.tags.forEach((tag: string) => {
          if (tag.toLowerCase().includes(queryLower)) {
            suggestions.add(tag)
          }
        })
      }

      // Add collections if they match
      if (product.collections && Array.isArray(product.collections)) {
        product.collections.forEach((collection: string) => {
          if (collection.toLowerCase().includes(queryLower)) {
            suggestions.add(collection)
          }
        })
      }
    })

    // Convert to array and limit results
    const suggestionArray = Array.from(suggestions)
      .slice(0, 8)
      .map(suggestion => ({
        text: suggestion,
        type: getSuggestionType(suggestion, response.data)
      }))

    return NextResponse.json({ success: true, data: suggestionArray })
  } catch (error) {
    console.error('Error getting search suggestions:', error)
    return NextResponse.json({ success: false, error: 'Failed to get suggestions' })
  }
}

function getSuggestionType(suggestion: string, products: any[]): string {
  // Determine the type of suggestion based on the data
  for (const product of products) {
    if (product.title === suggestion) return 'product'
    if (product.casNumber === suggestion) return 'cas'
    if (product.molecularFormula === suggestion) return 'formula'
    if (product.chemicalName === suggestion) return 'chemical'
    if (product.tags && product.tags.includes(suggestion)) return 'tag'
    if (product.collections && product.collections.includes(suggestion)) return 'category'
  }
  return 'general'
}