import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import Reset<PERSON>asswordForm from "@/components/reset-password-form"
import { ArrowLeft, Shield, Lock, CheckCircle } from "lucide-react"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Reset Password | Benzochem Industries",
  description: "Create a new password for your Benzochem Industries account",
}

export default function ResetPasswordPage() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-background via-background to-accent/20 pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[calc(100vh-8rem)]">
            
            {/* Left side - Information */}
            <div className="space-y-8 lg:pr-8">
              {/* Back to Login */}
              <Link
                href="/login"
                className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors duration-200"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Login
              </Link>

              {/* Hero Section */}
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium">
                  <Shield className="w-4 h-4" />
                  Secure Password Reset
                </div>
                
                <div className="space-y-4">
                  <h1 className="text-4xl lg:text-5xl font-bold text-foreground leading-tight">
                    Create New
                    <span className="block text-primary">Password</span>
                  </h1>
                  <p className="text-lg text-muted-foreground max-w-lg">
                    Choose a strong, secure password to protect your account and business data.
                  </p>
                </div>
              </div>

              {/* Password Requirements */}
              <div className="space-y-4">
                <h3 className="font-semibold text-foreground mb-4">Password Requirements:</h3>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Lock className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">Minimum 8 Characters</h4>
                      <p className="text-sm text-muted-foreground">Use a combination of letters, numbers, and symbols</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">Strong & Unique</h4>
                      <p className="text-sm text-muted-foreground">Don't reuse passwords from other accounts</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3 p-4 rounded-xl bg-card border border-border">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Shield className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground mb-1">Secure Storage</h4>
                      <p className="text-sm text-muted-foreground">Consider using a password manager</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Security Notice */}
              <div className="hidden lg:block relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-3xl"></div>
                <div className="relative bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <Shield className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-card-foreground">Account Security</h4>
                      <p className="text-sm text-muted-foreground">Your password protects sensitive business data</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Encrypted password storage
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Secure reset token validation
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Session security monitoring
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Form */}
            <div className="w-full max-w-md mx-auto lg:mx-0">
              <div className="bg-card border border-border rounded-2xl shadow-lg p-8">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-card-foreground mb-2">Reset Password</h2>
                  <p className="text-muted-foreground">Enter your new password below</p>
                </div>

                <Suspense fallback={
                  <div className="space-y-4">
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                    <div className="animate-pulse bg-muted h-10 rounded-lg"></div>
                  </div>
                }>
                  <ResetPasswordForm />
                </Suspense>

                <div className="mt-8 text-center">
                  <p className="text-muted-foreground text-sm">
                    Remember your password?{" "}
                    <Link
                      href="/login"
                      className="text-primary hover:text-primary/80 font-medium transition-colors duration-200"
                    >
                      Sign In
                    </Link>
                  </p>
                </div>
              </div>

              {/* Help Section */}
              <div className="mt-6 bg-muted/50 rounded-xl p-4">
                <h4 className="font-medium text-foreground mb-2">Need Help?</h4>
                <p className="text-sm text-muted-foreground">
                  If you're having trouble resetting your password, please{" "}
                  <Link href="/contact" className="text-primary hover:text-primary/80 font-medium">
                    contact our support team
                  </Link>
                  {" "}for assistance.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}