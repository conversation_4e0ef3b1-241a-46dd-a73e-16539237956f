import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import crypto from 'crypto'
import { emailService } from '@/lib/email-service'

// In a real application, you would use a proper database
// For now, we'll simulate with a temporary storage
const resetTokens = new Map<string, { email: string, token: string, expires: Date }>()

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json()

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex')
    const expires = new Date(Date.now() + 15 * 60 * 1000) // 15 minutes

    // Store reset token (in production, store in database)
    resetTokens.set(email, {
      email,
      token: resetToken,
      expires
    })

    // Send password reset email using the new email service
    try {
      const emailResult = await emailService.sendPasswordResetEmail(email, resetToken)
      
      if (!emailResult.success) {
        console.error('Failed to send password reset email:', emailResult.error)
        return NextResponse.json(
          { error: 'Failed to send reset email. Please try again.' },
          { status: 500 }
        )
      }
    } catch (emailError) {
      console.error('Email service error:', emailError)
      return NextResponse.json(
        { error: 'Failed to send reset email. Please try again.' },
        { status: 500 }
      )
    }

    // Set a secure cookie with the reset token for additional security
    const cookieStore = cookies()
    cookieStore.set('reset_request', email, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 // 15 minutes
    })

    return NextResponse.json({
      success: true,
      message: 'Password reset instructions have been sent to your email address'
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to get stored reset tokens (for testing purposes)
export async function GET(request: NextRequest) {
  const url = new URL(request.url)
  const email = url.searchParams.get('email')
  
  if (!email) {
    return NextResponse.json({ error: 'Email parameter required' }, { status: 400 })
  }

  const tokenData = resetTokens.get(email)
  if (!tokenData) {
    return NextResponse.json({ error: 'No reset token found' }, { status: 404 })
  }

  // Check if token is expired
  if (new Date() > tokenData.expires) {
    resetTokens.delete(email)
    return NextResponse.json({ error: 'Reset token expired' }, { status: 410 })
  }

  return NextResponse.json({
    token: tokenData.token,
    expires: tokenData.expires
  })
}