"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back, useMemo } from "react"
import { useSearchPara<PERSON>, useRout<PERSON> } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Search,
  Filter, 
  X, 
  Package,
  Tag,
  AlertCircle,
  Loader2
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { apiClient } from "@/lib/api-client"
import type { Product } from "@/lib/types"
import ProductCard from "./product-card"
import SearchWithSuggestions from "./search-with-suggestions"

interface SearchFilters {
  categories: string[]
  priceRange: {
    min: number
    max: number
  }
  inStock: boolean
  featured: boolean
}

interface SearchState {
  query: string
  filters: SearchFilters
  sortBy: 'relevance' | 'name' | 'price-low' | 'price-high' | 'newest'
  currentPage: number
  itemsPerPage: number
}

const INITIAL_FILTERS: SearchFilters = {
  categories: [],
  priceRange: { min: 0, max: 10000 },
  inStock: false,
  featured: false
}

const INITIAL_STATE: SearchState = {
  query: '',
  filters: INITIAL_FILTERS,
  sortBy: 'relevance',
  currentPage: 1,
  itemsPerPage: 20
}

const API_LIMIT = 100 // API limit per request

export default function SearchProducts() {
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const [state, setState] = useState<SearchState>(INITIAL_STATE)
  const [allProducts, setAllProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [totalResults, setTotalResults] = useState(0)
  const [availableCategories, setAvailableCategories] = useState<string[]>([])
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)
  const [hasMoreProducts, setHasMoreProducts] = useState(true)
  const [currentOffset, setCurrentOffset] = useState(0)

  // Initialize from URL params
  useEffect(() => {
    const query = searchParams.get('q') || ''
    const category = searchParams.get('category')
    const sort = searchParams.get('sort') as SearchState['sortBy'] || 'relevance'
    
    setState(prev => ({
      ...prev,
      query,
      filters: {
        ...prev.filters,
        categories: category ? [category] : []
      },
      sortBy: sort
    }))
  }, [searchParams])

  // Fetch products with pagination - Fixed duplicate issue
  const fetchProducts = useCallback(async (offset = 0, reset = false) => {
    if (reset) {
      setIsLoading(true)
      setAllProducts([])
      setCurrentOffset(0)
      setHasMoreProducts(true)
    } else {
      setIsLoadingMore(true)
    }

    try {
      const response = await apiClient.getProducts({ 
        limit: API_LIMIT, 
        offset: offset,
        search: state.query.trim() || undefined
      })

      if (response.success && response.data && Array.isArray(response.data)) {
        const transformedProducts = response.data.map(product => ({
          id: product.id,
          title: product.title,
          description: product.description,
          descriptionHtml: product.description,
          tags: product.tags || [],
          quantity: product.quantity || 0,
          collections: {
            edges: (product.collections || []).map((collection: string) => ({
              node: { title: collection }
            }))
          },
          images: {
            edges: (product.images || []).map((image: any) => ({
              node: { url: image.url || image }
            }))
          },
          media: {
            edges: (product.images || []).map((image: any, index: number) => ({
              node: {
                id: `media_${index}`,
                image: { url: image.url || image }
              }
            }))
          },
          priceRange: {
            minVariantPrice: {
              amount: product.priceRange?.minVariantPrice?.amount || '0.00',
              currencyCode: product.priceRange?.minVariantPrice?.currencyCode || 'INR'
            },
            maxVariantPrice: {
              amount: product.priceRange?.maxVariantPrice?.amount || product.priceRange?.minVariantPrice?.amount || '0.00',
              currencyCode: product.priceRange?.maxVariantPrice?.currencyCode || product.priceRange?.minVariantPrice?.currencyCode || 'INR'
            }
          },
          compareAtPriceRange: {
            minVariantPrice: { amount: '0.00', currencyCode: 'INR' },
            maxVariantPrice: { amount: '0.00', currencyCode: 'INR' }
          },
          metafields: [
            ...(product.purity ? [{
              id: 'purity',
              key: 'purity',
              namespace: 'chemical',
              value: product.purity,
              type: 'single_line_text_field'
            }] : []),
            ...(product.casNumber ? [{
              id: 'cas_number',
              key: 'cas_number',
              namespace: 'chemical',
              value: product.casNumber,
              type: 'single_line_text_field'
            }] : []),
            ...(product.molecularFormula ? [{
              id: 'molecular_formula',
              key: 'molecular_formula',
              namespace: 'chemical',
              value: product.molecularFormula,
              type: 'single_line_text_field'
            }] : []),
            ...(product.packaging ? [{
              id: 'packaging',
              key: 'packaging',
              namespace: 'chemical',
              value: JSON.stringify(product.packaging),
              type: 'json'
            }] : [])
          ]
        }))

        if (reset) {
          // Remove duplicates using Map for better performance
          const uniqueProducts = new Map()
          transformedProducts.forEach(product => {
            uniqueProducts.set(product.id, product)
          })
          setAllProducts(Array.from(uniqueProducts.values()))
        } else {
          // Merge with existing products, avoiding duplicates
          setAllProducts(prev => {
            const existingIds = new Set(prev.map(p => p.id))
            const newProducts = transformedProducts.filter(product => !existingIds.has(product.id))
            return [...prev, ...newProducts]
          })
        }

        // Update pagination info
        setCurrentOffset(offset + response.data.length)
        setHasMoreProducts(response.data.length === API_LIMIT)

        // Extract unique categories from transformed products
        const categories = new Set<string>()
        transformedProducts.forEach(product => {
          product.collections.edges.forEach(edge => {
            if (edge.node.title) {
              categories.add(edge.node.title)
            }
          })
        })
        setAvailableCategories(prev => {
          const combined = new Set([...prev, ...Array.from(categories)])
          return Array.from(combined).sort()
        })

        // If we have pagination info from the API, use it
        if (response.pagination?.total !== undefined) {
          setTotalResults(response.pagination.total)
        }
      } else {
        setHasMoreProducts(false)
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      setHasMoreProducts(false)
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }, [state.query])

  // Initial load and search changes
  useEffect(() => {
    fetchProducts(0, true)
  }, [state.query]) // Only trigger on search query changes

  // Load more products when needed
  const loadMoreProducts = useCallback(() => {
    if (!isLoadingMore && hasMoreProducts) {
      fetchProducts(currentOffset, false)
    }
  }, [fetchProducts, currentOffset, isLoadingMore, hasMoreProducts])

  // Filter and sort products (client-side for loaded products)
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = [...allProducts]

    // Apply additional client-side filtering (beyond search query which is handled server-side)
    if (state.query.trim()) {
      const query = state.query.toLowerCase().trim()
      filtered = filtered.filter(product => {
        // Search in title
        if (product.title.toLowerCase().includes(query)) return true
        
        // Search in description
        if (product.description.toLowerCase().includes(query)) return true
        
        // Search in tags
        if (product.tags.some(tag => tag.toLowerCase().includes(query))) return true
        
        // Search in CAS number
        const casNumber = product.metafields.find(m => m.key === 'cas_number')?.value
        if (casNumber && casNumber.toLowerCase().includes(query)) return true
        
        // Search in molecular formula
        const molecularFormula = product.metafields.find(m => m.key === 'molecular_formula')?.value
        if (molecularFormula && molecularFormula.toLowerCase().includes(query)) return true
        
        // Search in categories
        if (product.collections.edges.some(edge => 
          edge.node.title.toLowerCase().includes(query)
        )) return true
        
        return false
      })
    }

    // Apply category filter
    if (state.filters.categories.length > 0) {
      filtered = filtered.filter(product =>
        product.collections.edges.some(edge =>
          state.filters.categories.includes(edge.node.title)
        )
      )
    }

    // Apply stock filter
    if (state.filters.inStock) {
      filtered = filtered.filter(product => (product.quantity || 0) > 0)
    }

    // Apply sorting
    switch (state.sortBy) {
      case 'name':
        filtered.sort((a, b) => a.title.localeCompare(b.title))
        break
      case 'price-low':
        filtered.sort((a, b) => 
          parseFloat(a.priceRange.minVariantPrice.amount) - parseFloat(b.priceRange.minVariantPrice.amount)
        )
        break
      case 'price-high':
        filtered.sort((a, b) => 
          parseFloat(b.priceRange.minVariantPrice.amount) - parseFloat(a.priceRange.minVariantPrice.amount)
        )
        break
      case 'newest':
        // For now, keep original order as we don't have creation date
        break
      default: // relevance
        // Keep search relevance order
        break
    }

    return filtered
  }, [allProducts, state.query, state.filters, state.sortBy])

  // Paginate results
  const paginatedProducts = useMemo(() => {
    const startIndex = (state.currentPage - 1) * state.itemsPerPage
    const endIndex = startIndex + state.itemsPerPage
    return filteredAndSortedProducts.slice(startIndex, endIndex)
  }, [filteredAndSortedProducts, state.currentPage, state.itemsPerPage])

  // Update total results when filtered products change
  useEffect(() => {
    setTotalResults(filteredAndSortedProducts.length)
  }, [filteredAndSortedProducts])

  // Update URL when state changes
  const updateURL = useCallback((newState: Partial<SearchState>) => {
    const params = new URLSearchParams()
    
    if (newState.query || state.query) {
      params.set('q', newState.query || state.query)
    }
    
    if (newState.filters?.categories?.length || state.filters.categories.length) {
      const categories = newState.filters?.categories || state.filters.categories
      if (categories.length > 0) {
        params.set('category', categories[0]) // For simplicity, use first category
      }
    }
    
    if (newState.sortBy || state.sortBy !== 'relevance') {
      params.set('sort', newState.sortBy || state.sortBy)
    }

    const newURL = params.toString() ? `/search?${params.toString()}` : '/search'
    router.push(newURL, { scroll: false })
  }, [state, router])

  // Save search to history
  const saveSearchToHistory = async (searchQuery: string, resultCount: number) => {
    try {
      await fetch('/api/search-history', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery, resultCount })
      })
    } catch (error) {
      console.error('Error saving search to history:', error)
    }
  }

  // Handle search input
  const handleSearch = (query: string) => {
    const newState = { ...state, query, currentPage: 1 }
    setState(newState)
    updateURL(newState)
  }

  // Save search to history when results are loaded
  useEffect(() => {
    if (state.query.trim() && !isLoading && totalResults >= 0) {
      saveSearchToHistory(state.query.trim(), totalResults)
    }
  }, [state.query, totalResults, isLoading])

  // Handle filter changes
  const handleFilterChange = (filterType: keyof SearchFilters, value: any) => {
    const newFilters = { ...state.filters, [filterType]: value }
    const newState = { ...state, filters: newFilters, currentPage: 1 }
    setState(newState)
    updateURL(newState)
  }

  // Handle sort change
  const handleSortChange = (sortBy: SearchState['sortBy']) => {
    const newState = { ...state, sortBy }
    setState(newState)
    updateURL(newState)
  }

  // Clear all filters
  const clearFilters = () => {
    const newState = { ...state, filters: INITIAL_FILTERS, currentPage: 1 }
    setState(newState)
    updateURL(newState)
  }

  // Get active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (state.filters.categories.length > 0) count++
    if (state.filters.inStock) count++
    if (state.filters.featured) count++
    return count
  }, [state.filters])

  // Check if we need to load more products when reaching the end of current page
  useEffect(() => {
    const endIndex = state.currentPage * state.itemsPerPage
    if (endIndex >= filteredAndSortedProducts.length && hasMoreProducts && !isLoadingMore) {
      loadMoreProducts()
    }
  }, [state.currentPage, filteredAndSortedProducts.length, hasMoreProducts, isLoadingMore, loadMoreProducts])

  return (
    <main className="flex min-h-screen flex-col pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl font-bold text-foreground mb-2">Search Products</h1>
            <p className="text-muted-foreground mb-6">
              Discover our extensive catalog of high-quality chemical products
            </p>
          </motion.div>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="w-full"
          >
            <SearchWithSuggestions
              placeholder="Search by product name, CAS number, molecular formula..."
              onSearch={handleSearch}
              showRecentSearches={true}
              autoFocus={!state.query}
            />
          </motion.div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters - Desktop */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="hidden lg:block lg:w-64 space-y-6"
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold text-foreground">Filters</h3>
                  {activeFilterCount > 0 && (
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      Clear ({activeFilterCount})
                    </Button>
                  )}
                </div>

                {/* Categories */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-foreground">Categories</h4>
                  <div className="space-y-2 max-h-48 overflow-y-auto thin-scrollbar">
                    {availableCategories.map((category) => (
                      <div key={category} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category}`}
                          checked={state.filters.categories.includes(category)}
                          onCheckedChange={(checked) => {
                            const newCategories = checked
                              ? [...state.filters.categories, category]
                              : state.filters.categories.filter(c => c !== category)
                            handleFilterChange('categories', newCategories)
                          }}
                        />
                        <label
                          htmlFor={`category-${category}`}
                          className="text-sm text-muted-foreground cursor-pointer"
                        >
                          {category}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator className="my-4" />

                {/* Stock Status */}
                <div className="space-y-3">
                  <h4 className="font-medium text-sm text-foreground">Availability</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="in-stock"
                      checked={state.filters.inStock}
                      onCheckedChange={(checked) => handleFilterChange('inStock', checked)}
                    />
                    <label htmlFor="in-stock" className="text-sm text-muted-foreground cursor-pointer">
                      In Stock Only
                    </label>
                  </div>
                </div>

                {/* Load More Products Button */}
                {hasMoreProducts && (
                  <div className="pt-4">
                    <Button
                      variant="outline"
                      onClick={loadMoreProducts}
                      disabled={isLoadingMore}
                      className="w-full"
                    >
                      {isLoadingMore ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        'Load More Products'
                      )}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Toolbar */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6"
            >
              <div className="flex items-center gap-4">
                {/* Mobile Filter Button */}
                <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
                  <SheetTrigger asChild>
                    <Button variant="outline" className="lg:hidden">
                      <Filter className="h-4 w-4 mr-2" />
                      Filters
                      {activeFilterCount > 0 && (
                        <Badge variant="secondary" className="ml-2">
                          {activeFilterCount}
                        </Badge>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-80">
                    <SheetHeader>
                      <SheetTitle>Filter Products</SheetTitle>
                      <SheetDescription>
                        Refine your product search with multiple criteria
                      </SheetDescription>
                    </SheetHeader>
                    <div className="mt-6 space-y-6">
                      {/* Mobile version of filters - same content as desktop */}
                      <div className="space-y-3">
                        <h4 className="font-medium text-sm text-foreground">Categories</h4>
                        <div className="space-y-2 max-h-48 overflow-y-auto thin-scrollbar">
                          {availableCategories.map((category) => (
                            <div key={category} className="flex items-center space-x-2">
                              <Checkbox
                                id={`mobile-category-${category}`}
                                checked={state.filters.categories.includes(category)}
                                onCheckedChange={(checked) => {
                                  const newCategories = checked
                                    ? [...state.filters.categories, category]
                                    : state.filters.categories.filter(c => c !== category)
                                  handleFilterChange('categories', newCategories)
                                }}
                              />
                              <label
                                htmlFor={`mobile-category-${category}`}
                                className="text-sm text-muted-foreground cursor-pointer"
                              >
                                {category}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-3">
                        <h4 className="font-medium text-sm text-foreground">Availability</h4>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="mobile-in-stock"
                            checked={state.filters.inStock}
                            onCheckedChange={(checked) => handleFilterChange('inStock', checked)}
                          />
                          <label htmlFor="mobile-in-stock" className="text-sm text-muted-foreground cursor-pointer">
                            In Stock Only
                          </label>
                        </div>
                      </div>

                      {/* Mobile Load More */}
                      {hasMoreProducts && (
                        <Button
                          variant="outline"
                          onClick={loadMoreProducts}
                          disabled={isLoadingMore}
                          className="w-full"
                        >
                          {isLoadingMore ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Loading...
                            </>
                          ) : (
                            'Load More Products'
                          )}
                        </Button>
                      )}

                      {activeFilterCount > 0 && (
                        <Button variant="outline" onClick={clearFilters} className="w-full">
                          Clear All Filters
                        </Button>
                      )}
                    </div>
                  </SheetContent>
                </Sheet>

                {/* Results Count */}
                <div className="text-sm text-muted-foreground">
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading...
                    </div>
                  ) : (
                    <>
                      {totalResults} product{totalResults !== 1 ? 's' : ''} found
                      {hasMoreProducts && (
                        <span className="text-xs ml-2">(loading more...)</span>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Sort */}
                <Select value={state.sortBy} onValueChange={handleSortChange}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="relevance">Relevance</SelectItem>
                    <SelectItem value="name">Name A-Z</SelectItem>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                    <SelectItem value="newest">Newest First</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </motion.div>

            {/* Active Filters */}
            {activeFilterCount > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="flex flex-wrap items-center gap-2 mb-6"
              >
                {state.filters.categories.map((category) => (
                  <Badge key={category} variant="secondary" className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {category}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newCategories = state.filters.categories.filter(c => c !== category)
                        handleFilterChange('categories', newCategories)
                      }}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
                
                {state.filters.inStock && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    In Stock
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFilterChange('inStock', false)}
                      className="h-auto p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </motion.div>
            )}

            {/* Products Grid */}
            <AnimatePresence mode="wait">
              {isLoading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                >
                  {[...Array(8)].map((_, i) => (
                    <Card key={`skeleton-${i}`}>
                      <CardContent className="p-4">
                        <Skeleton className="h-48 w-full rounded-md mb-4" />
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-4 w-2/3" />
                      </CardContent>
                    </Card>
                  ))}
                </motion.div>
              ) : paginatedProducts.length > 0 ? (
                <motion.div
                  key="products"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                >
                  {paginatedProducts.map((product, index) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <ProductCard product={product} />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="no-results"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-12"
                >
                  <div className="w-20 h-20 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <AlertCircle className="w-10 h-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No products found</h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your search criteria or browse our categories.
                  </p>
                  <Button variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Pagination */}
            {totalResults > state.itemsPerPage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="flex justify-center mt-12"
              >
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    disabled={state.currentPage === 1}
                    onClick={() => setState(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
                  >
                    Previous
                  </Button>
                  
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.ceil(totalResults / state.itemsPerPage) }, (_, i) => i + 1)
                      .filter(page => 
                        page === 1 || 
                        page === Math.ceil(totalResults / state.itemsPerPage) ||
                        Math.abs(page - state.currentPage) <= 2
                      )
                      .map((page, index, array) => (
                        <div key={`page-${page}`} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="px-2 text-muted-foreground">...</span>
                          )}
                          <Button
                            variant={page === state.currentPage ? 'default' : 'ghost'}
                            size="sm"
                            onClick={() => setState(prev => ({ ...prev, currentPage: page }))}
                          >
                            {page}
                          </Button>
                        </div>
                      ))}
                  </div>
                  
                  <Button
                    variant="outline"
                    disabled={state.currentPage === Math.ceil(totalResults / state.itemsPerPage)}
                    onClick={() => setState(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
                  >
                    Next
                  </Button>
                </div>
              </motion.div>
            )}

            {/* Loading More Indicator */}
            {isLoadingMore && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Loading more products...
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  )
}