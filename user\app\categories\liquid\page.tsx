import type { Metada<PERSON> } from "next"
import { Suspense } from 'react'
import CategoryHeader from "@/components/category-header"
import ProductGrid from "@/components/product-grid"
import { getProductsByCategory } from "@/lib/product-filters"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Liquid Products | Benzochem Industries",
  description: "Browse our premium liquid chemical products for industrial and laboratory applications.",
}

// Basic skeleton for an individual product card
function BasicProductCardSkeleton() {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3">
      <Skeleton className="h-40 w-full rounded-md" /> {/* Image */}
      <Skeleton className="h-6 w-3/4 rounded" /> {/* Title */}
      <Skeleton className="h-4 w-full rounded" /> {/* Description line 1 */}
      <Skeleton className="h-4 w-5/6 rounded" /> {/* Description line 2 */}
      <div className="flex justify-between items-center pt-2">
        <Skeleton className="h-5 w-1/3 rounded" /> {/* Price */}
        <Skeleton className="h-8 w-1/3 rounded" /> {/* Button */}
      </div>
    </div>
  );
}

// Skeleton for the product grid section of a category page
function CategoryPageProductGridSkeleton() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="space-y-8">
        {/* Skeleton for Header (Title, Count) and Controls (Filter, Sort) */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <Skeleton className="h-7 w-32 mb-1 rounded" /> {/* "Products" title */}
            <Skeleton className="h-4 w-24 rounded" /> {/* Product count */}
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Filter Button */}
            <Skeleton className="h-10 w-24 rounded-md" /> {/* Sort Button */}
          </div>
        </div>

        {/* Skeleton for Product Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <BasicProductCardSkeleton key={i} />
          ))}
        </div>
      </div>
    </div>
  )
}

// Component to fetch and render actual category content
async function LiquidCategoryPageContent() {
  const products = await getProductsByCategory('liquid');
  console.log(`Liquid category page received ${products.length} products`);

  return (
    <div className="container mx-auto px-4 py-12">
      <ProductGrid products={products} />
    </div>
  )
}

export default async function LiquidCategoryPage() {
  return (
    <main className="flex min-h-screen flex-col pt-20">
      <CategoryHeader
        title="Liquid Products"
        description="Premium liquid solutions for specialized chemical processes"
        image="https://images.unsplash.com/photo-1582719471384-894fbb16e074?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3"
      />
      <Suspense fallback={<CategoryPageProductGridSkeleton />}>
        <LiquidCategoryPageContent />
      </Suspense>
    </main>
  )
}