"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function InitCollectionsPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const initializeCollections = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/init-collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <h1 className="text-3xl font-bold mb-8">Initialize Collections</h1>
      
      <div className="space-y-4">
        <Button 
          onClick={initializeCollections} 
          disabled={loading}
          className="w-full max-w-md"
        >
          {loading ? 'Initializing...' : 'Initialize Basic Collections'}
        </Button>
        
        {result && (
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h2 className="text-xl font-semibold mb-2">Result:</h2>
            <pre className="text-sm overflow-auto custom-scrollbar">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}