"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { FileText, Send, Plus, Clock } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { useSecureQuotation } from "@/hooks/use-secure-quotation"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"

// Skeleton for the QuotationSummary component
function QuotationSummarySkeleton() {
  return (
    <div className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-xl overflow-hidden sticky top-24 shadow-sm">
      <div className="p-6 border-b border-border/20">
        <Skeleton className="h-6 w-3/4 rounded" />
      </div>
      <div className="p-6 space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/2 rounded" />
          <Skeleton className="h-4 w-2/3 rounded" />
        </div>
        <div className="border-t border-border/20 pt-4">
          <Skeleton className="h-20 w-full rounded" />
        </div>
        <Skeleton className="h-10 w-full rounded" />
      </div>
    </div>
  )
}

export default function QuotationSummary() {
  const { currentQuotation, submitQuotation, isLoading } = useSecureQuotation()
  const [notes, setNotes] = useState("")
  const [urgency, setUrgency] = useState<"standard" | "urgent" | "asap">("standard")
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (isLoading) {
    return <QuotationSummarySkeleton />
  }

  const itemCount = currentQuotation?.items.length || 0
  const totalQuantity = currentQuotation?.items.reduce((sum, item) => sum + item.quantity, 0) || 0

  const handleSubmit = async () => {
    if (!currentQuotation || currentQuotation.items.length === 0) return
    
    try {
      setIsSubmitting(true)
      await submitQuotation(notes, urgency)
      setNotes("")
      setUrgency("standard")
    } catch (error) {
      console.error("Error submitting quotation:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-card/50 backdrop-blur-sm border border-border/20 rounded-xl overflow-hidden sticky top-24 shadow-sm"
    >
      <div className="p-6 border-b border-border/20 bg-gradient-to-r from-primary/5 to-transparent">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <FileText className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Quote Summary</h2>
            <p className="text-sm text-muted-foreground">Review your request</p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Summary Stats */}
        <div className="space-y-4">
          <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg border border-border/10">
            <span className="text-muted-foreground font-medium">Total Items:</span>
            <span className="font-semibold text-foreground text-lg">{itemCount}</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-muted/20 rounded-lg border border-border/10">
            <span className="text-muted-foreground font-medium">Total Quantity:</span>
            <span className="font-semibold text-foreground text-lg">{totalQuantity} units</span>
          </div>
        </div>

        {/* Urgency Section */}
        <div className="border-t border-border/20 pt-6 space-y-4">
          <Label className="text-sm font-semibold text-foreground flex items-center gap-2">
            <Clock className="h-4 w-4 text-primary" />
            Urgency Level
          </Label>
          <Select value={urgency} onValueChange={(value: "standard" | "urgent" | "asap") => setUrgency(value)}>
            <SelectTrigger className="w-full border-border/20 bg-background/50">
              <SelectValue placeholder="Select urgency level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Standard (3-5 business days)</span>
                </div>
              </SelectItem>
              <SelectItem value="urgent">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Urgent (1-2 business days)</span>
                </div>
              </SelectItem>
              <SelectItem value="asap">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>ASAP (Within 24 hours)</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Notes Section */}
        <div className="border-t border-border/20 pt-6 space-y-4">
          <label className="text-sm font-semibold text-foreground">
            Additional Notes (Optional)
          </label>
          <Textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add specific requirements, purity levels, delivery preferences, or technical questions..."
            rows={4}
            className="text-sm border-border/20 bg-background/50 resize-none"
          />
        </div>

        {/* Action Buttons */}
        <div className="border-t border-border/20 pt-6 space-y-4">
          {itemCount > 0 ? (
            <>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 h-12 text-base font-semibold"
              >
                <Send className="h-5 w-5 mr-2" />
                {isSubmitting ? "Submitting Request..." : "Submit Quote Request"}
              </Button>
              <div className="text-center p-3 bg-primary/5 rounded-lg border border-primary/10">
                <p className="text-sm text-primary font-medium">
                  ⚡ Fast Response Guaranteed
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Our experts will review and respond within 24 hours
                </p>
              </div>
            </>
          ) : (
            <>
              <Button disabled className="w-full h-12 text-base">
                <FileText className="h-5 w-5 mr-2" />
                No Items Added
              </Button>
              <Button asChild variant="outline" className="w-full h-12 text-base border-border/20 hover:bg-primary/5 hover:border-primary/20">
                <Link href="/products">
                  <Plus className="h-5 w-5 mr-2" />
                  Browse Products
                </Link>
              </Button>
            </>
          )}
        </div>

        {/* Info Box */}
        <div className="bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 rounded-xl p-5">
          <h4 className="text-sm font-semibold text-primary mb-3 flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Why Request a Quote?
          </h4>
          <ul className="text-xs text-foreground/80 space-y-2">
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              Get competitive pricing for bulk orders
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              Receive detailed technical specifications
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              Custom packaging options available
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
              Direct communication with chemical experts
            </li>
          </ul>
        </div>
      </div>
    </motion.div>
  )
}