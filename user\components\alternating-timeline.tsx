"use client"

import { motion } from "framer-motion"
import { Calendar, ChevronRight } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface TimelineEvent {
  year: string
  title: string
  description: string
  category?: string
  highlights?: string[]
}

interface AlternatingTimelineProps {
  events: TimelineEvent[]
}

export default function AlternatingTimeline({ events }: AlternatingTimelineProps) {
  return (
    <div className="relative max-w-6xl mx-auto">
      {/* Center line - Hidden on mobile, visible on desktop */}
      <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-primary/20 via-primary to-primary/20"></div>

      {/* Mobile center line */}
      <div className="md:hidden absolute left-8 top-0 h-full w-0.5 bg-gradient-to-b from-primary/20 via-primary to-primary/20"></div>

      <div className="space-y-8 md:space-y-16">
        {events.map((event, index) => {
          const isLeft = index % 2 === 0
          
          return (
            <motion.div
              key={index}
              initial={{ 
                opacity: 0, 
                x: isLeft ? -50 : 50,
                y: 30 
              }}
              whileInView={{ 
                opacity: 1, 
                x: 0,
                y: 0 
              }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ 
                duration: 0.6, 
                delay: index * 0.1,
                ease: "easeOut"
              }}
              className="relative"
            >
              {/* Desktop Layout */}
              <div className="hidden md:flex items-center">
                {/* Left Side Content */}
                <div className="w-5/12 pr-8">
                  {isLeft && (
                    <TimelineCard event={event} />
                  )}
                </div>

                {/* Center Timeline Dot */}
                <div className="flex flex-col items-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: index * 0.1 + 0.3 }}
                    className="relative z-10"
                  >
                    <div className="w-6 h-6 rounded-full bg-primary border-4 border-background shadow-lg">
                      <div className="absolute inset-0 rounded-full bg-primary animate-pulse opacity-30"></div>
                    </div>
                  </motion.div>
                  
                  {/* Year Badge */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: index * 0.1 + 0.5 }}
                    className="mt-3"
                  >
                    <Badge variant="outline" className="bg-background/80 backdrop-blur-sm border-primary/30 text-primary font-semibold">
                      {event.year}
                    </Badge>
                  </motion.div>
                </div>

                {/* Right Side Content */}
                <div className="w-5/12 pl-8">
                  {!isLeft && (
                    <TimelineCard event={event} />
                  )}
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="md:hidden flex items-start space-x-6">
                {/* Timeline Dot */}
                <div className="flex flex-col items-center mt-2">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: index * 0.1 + 0.3 }}
                    className="relative z-10"
                  >
                    <div className="w-5 h-5 rounded-full bg-primary border-3 border-background shadow-lg">
                      <div className="absolute inset-0 rounded-full bg-primary animate-pulse opacity-30"></div>
                    </div>
                  </motion.div>
                </div>

                {/* Content */}
                <div className="flex-1 pb-8">
                  <div className="mb-3">
                    <Badge variant="outline" className="bg-background/80 backdrop-blur-sm border-primary/30 text-primary font-semibold">
                      {event.year}
                    </Badge>
                  </div>
                  <TimelineCard event={event} />
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>
    </div>
  )
}

// Timeline Card Component - Always uses left-aligned content
function TimelineCard({ event }: { event: TimelineEvent }) {
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 bg-card/50 backdrop-blur-sm border-0 shadow-md hover:bg-card/80">
      <CardContent className="p-6">
        <div className="space-y-4 text-left">
          {/* Category Badge */}
          {event.category && (
            <div className="flex justify-start">
              <Badge variant="secondary" className="text-xs bg-primary/10 text-primary border-primary/20">
                {event.category}
              </Badge>
            </div>
          )}

          {/* Title */}
          <h3 className="text-xl font-semibold text-card-foreground group-hover:text-primary transition-colors duration-300">
            {event.title}
          </h3>

          {/* Description */}
          <p className="text-muted-foreground leading-relaxed">
            {event.description}
          </p>

          {/* Highlights */}
          {event.highlights && event.highlights.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                <Calendar className="h-4 w-4 text-primary" />
                Key Highlights
              </h4>
              <ul className="space-y-1">
                {event.highlights.map((highlight, idx) => (
                  <li key={idx} className="text-sm text-muted-foreground flex items-start gap-2">
                    <ChevronRight className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                    <span>{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}