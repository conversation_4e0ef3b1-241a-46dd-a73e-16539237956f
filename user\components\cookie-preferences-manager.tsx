"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Cookie, 
  Shield, 
  Settings, 
  Info,
  Save,
  RotateCcw,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { motion } from 'framer-motion'
import { cookieConsentUtils } from '@/lib/cookie-consent-storage'

interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  functional: boolean
}

interface CookiePreferencesManagerProps {
  className?: string
}

export default function CookiePreferencesManager({ className }: CookiePreferencesManagerProps) {
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true,
    analytics: false,
    marketing: false,
    functional: false
  })
  const [originalPreferences, setOriginalPreferences] = useState<CookiePreferences>({
    essential: true,
    analytics: false,
    marketing: false,
    functional: false
  })
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<string | null>(null)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')

  useEffect(() => {
    loadCurrentPreferences()
  }, [])

  const loadCurrentPreferences = async () => {
    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.checkConsentStatus()
      
      if (result.hasConsent && result.preferences) {
        setPreferences(result.preferences)
        setOriginalPreferences(result.preferences)
        setLastUpdated(result.timestamp || new Date().toISOString())
      }
    } catch (error) {
      console.error('Error loading cookie preferences:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updatePreference = (key: keyof CookiePreferences, value: boolean) => {
    if (key === 'essential') return // Cannot disable essential cookies
    setPreferences(prev => ({ ...prev, [key]: value }))
    setSaveStatus('idle')
  }

  const savePreferences = async () => {
    // Check if preferences have actually changed
    const hasActualChanges = JSON.stringify(preferences) !== JSON.stringify(originalPreferences)
    if (!hasActualChanges) {
      console.log('🍪 No changes detected, skipping save')
      return
    }

    setIsSaving(true)
    setSaveStatus('idle')

    try {
      // Use secure cookie storage
      const result = await cookieConsentUtils.saveConsent(preferences, 'settings_page')

      if (result.success) {
        setOriginalPreferences(preferences)
        setLastUpdated(new Date().toISOString())
        setSaveStatus('success')

        // Apply consent preferences
        window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
          detail: preferences
        }))

        // Clear success message after 3 seconds
        setTimeout(() => setSaveStatus('idle'), 3000)
      } else {
        setSaveStatus('error')
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const resetPreferences = () => {
    setPreferences(originalPreferences)
    setSaveStatus('idle')
  }

  const hasChanges = JSON.stringify(preferences) !== JSON.stringify(originalPreferences)

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cookie className="w-5 h-5" />
            Cookie Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cookie className="w-5 h-5" />
          Cookie Preferences
        </CardTitle>
        <CardDescription>
          Manage your cookie preferences and control how we use cookies on our website.
          {lastUpdated && (
            <span className="block mt-2 text-xs text-muted-foreground">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Status Alert */}
        {saveStatus === 'success' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <Alert className="border-green-200 bg-green-50 dark:bg-green-950/20">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Your cookie preferences have been saved successfully.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {saveStatus === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <Alert className="border-red-200 bg-red-50 dark:bg-red-950/20">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800 dark:text-red-200">
                Failed to save your preferences. Please try again.
              </AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Cookie Categories */}
        <div className="space-y-4">
          {/* Essential Cookies */}
          <div className="flex items-start justify-between p-4 bg-muted/30 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="w-4 h-4 text-green-600" />
                <h4 className="font-medium text-foreground">Essential Cookies</h4>
                <Badge variant="secondary" className="text-xs">Always Active</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                These cookies are necessary for the website to function and cannot be switched off. 
                They are usually only set in response to actions made by you which amount to a request for services.
              </p>
            </div>
            <Switch
              checked={preferences.essential}
              disabled={true}
              className="ml-4"
            />
          </div>

          {/* Analytics Cookies */}
          <div className="flex items-start justify-between p-4 bg-muted/30 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Info className="w-4 h-4 text-blue-600" />
                <h4 className="font-medium text-foreground">Analytics Cookies</h4>
                <Badge variant="outline" className="text-xs">Optional</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                These cookies allow us to count visits and traffic sources so we can measure and improve 
                the performance of our site. They help us know which pages are most popular.
              </p>
            </div>
            <Switch
              checked={preferences.analytics}
              onCheckedChange={(checked) => updatePreference('analytics', checked)}
              className="ml-4"
            />
          </div>

          {/* Marketing Cookies */}
          <div className="flex items-start justify-between p-4 bg-muted/30 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Cookie className="w-4 h-4 text-purple-600" />
                <h4 className="font-medium text-foreground">Marketing Cookies</h4>
                <Badge variant="outline" className="text-xs">Optional</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                These cookies may be set through our site by our advertising partners. They may be used 
                to build a profile of your interests and show you relevant adverts on other sites.
              </p>
            </div>
            <Switch
              checked={preferences.marketing}
              onCheckedChange={(checked) => updatePreference('marketing', checked)}
              className="ml-4"
            />
          </div>

          {/* Functional Cookies */}
          <div className="flex items-start justify-between p-4 bg-muted/30 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Settings className="w-4 h-4 text-orange-600" />
                <h4 className="font-medium text-foreground">Functional Cookies</h4>
                <Badge variant="outline" className="text-xs">Optional</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                These cookies enable the website to provide enhanced functionality and personalization. 
                They may be set by us or by third party providers whose services we have added to our pages.
              </p>
            </div>
            <Switch
              checked={preferences.functional}
              onCheckedChange={(checked) => updatePreference('functional', checked)}
              className="ml-4"
            />
          </div>
        </div>

        <Separator />

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={savePreferences}
            disabled={isSaving || !hasChanges}
            className="flex-1"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Preferences'}
          </Button>
          <Button
            variant="outline"
            onClick={resetPreferences}
            disabled={isSaving || !hasChanges}
            className="flex-1 sm:flex-none"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
        </div>

        {/* Information */}
        <div className="text-xs text-muted-foreground space-y-2">
          <p>
            <strong>Note:</strong> Changes to your cookie preferences will take effect immediately. 
            Some features may not work properly if you disable certain cookies.
          </p>
          <p>
            For more information about how we use cookies, please read our{' '}
            <a href="/privacy" className="text-primary hover:underline">
              Privacy Policy
            </a>.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}