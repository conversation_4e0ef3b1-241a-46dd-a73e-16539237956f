"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import LoginPage from "@/app/login/page-content"
import { Loader2 } from "lucide-react"

export default function LoginPageWrapper() {
  const { user, isLoading } = useAuth()
  const [isRedirecting, setIsRedirecting] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Check URL for redirect parameter to prevent infinite loops
    const url = new URL(window.location.href)
    const hasRedirectParam = url.searchParams.has('redirect')

    // If user is already authenticated AND there's no redirect parameter, redirect to account page
    if (!isLoading && user && !hasRedirectParam) {
      setIsRedirecting(true)
      console.log("User is already authenticated, redirecting to account page")
      // Small delay to ensure smooth transition
      setTimeout(() => {
        router.push("/account")
      }, 500)
    }
  }, [isLoading, user, router])

  // Show loading state while checking or redirecting
  if (isLoading || isRedirecting) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-[#FAFAFA]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-teal-600" />
          <p className="text-lg text-gray-600">
            {isRedirecting ? "Login successful! Redirecting..." : "Checking authentication status..."}
          </p>
        </div>
      </div>
    )
  }

  // Show login page once we've confirmed user is not signed in
  return <LoginPage />
}
