import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/email-service';

// Dynamic API route for real-time data processing
export const dynamic = 'force-dynamic';

// For server-side API calls, we'll create a server-side API client
class ServerApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || 'http://localhost:3001';
    this.apiKey = process.env.NEXT_PUBLIC_API_KEY || '';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ success: boolean; data?: T; error?: string; pagination?: any }> {
    try {
      const url = `${this.baseUrl}/api/v1${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          ...options.headers,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      return result;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async createQuotation(quotationData: any) {
    return this.makeRequest('/quotations', {
      method: 'POST',
      body: JSON.stringify(quotationData),
    });
  }

  async getUserQuotations(userId: string, params: any = {}) {
    const searchParams = new URLSearchParams();
    searchParams.set('userId', userId);
    
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());
    
    const query = searchParams.toString();
    return this.makeRequest(`/quotations?${query}`);
  }

  async getQuotation(quotationId: string) {
    return this.makeRequest(`/quotations/${quotationId}`);
  }
}

const serverApiClient = new ServerApiClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      userEmail,
      userName,
      userPhone,
      businessName,
      products,
      additionalRequirements,
      deliveryLocation,
      urgency
    } = body;

    // Validate required fields
    if (!userId || !userEmail || !userName || !products || products.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create quotation via admin API
    const result = await serverApiClient.createQuotation({
      userId,
      userEmail,
      userName,
      userPhone,
      businessName,
      products,
      additionalRequirements,
      deliveryLocation,
      urgency: urgency || "standard",
    });

    if (result.success) {
      // Send quotation email notification
      try {
        const firstName = userName.split(' ')[0] || userName;
        await emailService.sendQuotationEmail(
          userEmail,
          firstName,
          result.data?.id || 'Unknown',
          'created'
        );
      } catch (emailError) {
        console.error('Failed to send quotation email:', emailError);
        // Don't fail the quotation creation if email fails
      }

      return NextResponse.json({
        success: true,
        quotationId: result.data?.id,
        message: "Quotation request submitted successfully"
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to submit quotation request' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Quotation API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit quotation request' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user quotations via admin API
    const result = await serverApiClient.getUserQuotations(userId, {
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        ...result
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to fetch quotations' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Get quotations API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch quotations' },
      { status: 500 }
    );
  }
}